import datetime
import traceback

from bson import ObjectId

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 测试任务结果
class TestTaskResult(BaseModel):
    _doc = "test_task_result"
    task_id: str # 任务id
    subjective_metric_id: str # 主观指标id
    objective_metric_id: str # 客观指标id

    rate_final: float# 准确率（最终）
    rate_answer: float# 回答正确率
    rate_category: float# 落域正确率
    rate_ad_final: float# 校准后 准确率（最终）
    rate_ad_answer: float# 校准后 回答正确率
    rate_ad_category: float# 校准后 落域正确率
    rate_recall: float# 召回率
    satisfactory: int # 满意度 1：非常满意，2：比较满意
    total_use_time: int# 总耗时 单位：秒
    avg_score: int# 平均得分

    result_status: int # 是否成功 1=成功 -1=失败 0=初始，执行中
    eval_config_id: str # 评估通道id

    data_set_id: str  # 测试的数据集id

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", task_id="", subjective_metric_id="",
                 objective_metric_id="", rate_final=0, rate_answer=0, rate_category=0, rate_ad_final=0,
                 rate_ad_answer=0, rate_ad_category=0, rate_recall=0, satisfactory=1, total_use_time=0, avg_score=0,
                 result_status=0, eval_config_id="", data_set_id=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del)
        self.task_id = task_id
        self.subjective_metric_id = subjective_metric_id
        self.objective_metric_id = objective_metric_id
        self.rate_final = rate_final
        self.rate_answer = rate_answer
        self.rate_category = rate_category
        self.rate_ad_final = rate_ad_final
        self.rate_ad_answer = rate_ad_answer
        self.rate_ad_category = rate_ad_category
        self.rate_recall = rate_recall
        self.satisfactory = satisfactory
        self.total_use_time = total_use_time
        self.avg_score = avg_score
        self.result_status = result_status
        self.oem_codes = oem_codes
        self.eval_config_id = eval_config_id
        self.data_set_id = data_set_id

    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = ObjectId(self._id) if self._id and len(str(self._id)) > 0 else ""
        base_json["task_id"] = ObjectId(self.task_id) if self.task_id and len(str(self.task_id)) > 0 else ""
        base_json["subjective_metric_id"] = ObjectId(self.subjective_metric_id) if self.subjective_metric_id and len(str(self.subjective_metric_id)) > 0 else ""
        base_json["objective_metric_id"] = ObjectId(self.objective_metric_id) if self.objective_metric_id and len(str(self.objective_metric_id)) > 0 else ""
        base_json["data_set_id"] = ObjectId(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        base_json["eval_config_id"] = ObjectId(self.eval_config_id) if self.eval_config_id and len(str(self.eval_config_id)) > 0 else ""
        base_json["rate_final"] = self.rate_final
        base_json["rate_answer"] = self.rate_answer
        base_json["rate_category"] = self.rate_category
        base_json["rate_ad_final"] = self.rate_ad_final
        base_json["rate_ad_answer"] = self.rate_ad_answer
        base_json["rate_ad_category"] = self.rate_ad_category
        base_json["rate_recall"] = self.rate_recall
        base_json["satisfactory"] = self.satisfactory
        base_json["total_use_time"] = self.total_use_time
        base_json["avg_score"] = self.avg_score
        base_json["result_status"] = self.result_status
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["task_id"] = str(self.task_id) if self.task_id and len(str(self.task_id)) > 0 else ""
        data_json["subjective_metric_id"] = str(self.subjective_metric_id) if self.subjective_metric_id and len(str(self.subjective_metric_id)) > 0 else ""
        data_json["objective_metric_id"] = str(self.objective_metric_id) if self.objective_metric_id and len(str(self.objective_metric_id)) > 0 else ""
        data_json["data_set_id"] = str(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        data_json["eval_config_id"] = str(self.eval_config_id) if self.eval_config_id and len(str(self.eval_config_id)) > 0 else ""
        return data_json

    # 根据任务id，删除其下所有测试任务结果数据
    @classmethod
    def del_by_tid(cls, task_id, do_user):
        condition = {"task_id": ObjectId(task_id)}
        # del_pro = {"is_del": 1, "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        return MongoDBUtil.delete_by_many(cls._doc, condition, do_user)

    @classmethod
    def find_status_timeout(cls):
        condition = {"result_status": 0}
        compare_time = (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')
        condition["update_time"] = {"$lt":compare_time}
        # 执行聚合查询
        results = MongoDBUtil.find_condition(cls._doc, condition)
        if results is not None:
            items = []
            for item in results:
                result = TestTaskResult(**item)
                items.append(result)
            return items
        else:
            return None

    @classmethod
    def query_new_id_by_task_ids(cls, task_ids):
        """
        根据评测任务id集合，按评测任务id分组获取最新的评测任务结果
        :param task_ids: 评测任务id集合
        :return: 测集分组后，每组最新的任务id str、评测集id str;[{"data_set_id":"评测集id", "task_id":"最新的任务id"}]
        """
        et_log.info(f"query_new_id_by_task_ids task_ids:{task_ids}")
        try:
            pipeline = [
                {"$match": {"task_id": {"$in":[ObjectId(task_id) for task_id in task_ids]}, "is_del":0}},
                {"$sort": {"create_time": -1}},
                {"$group": {
                    "_id": "$task_id",
                    "new_result_id": {"$first": "$_id" },
                    "latest_time": {"$first": "$create_time"},
                    "task_id": {"$first": "$task_id"}  # 获取task_id（与_id相同，但转为普通字段）
                }}
            ]
            et_log.info(f"query_new_id_by_task_ids pipeline:{pipeline}")
            result = []
            group_data = list(cls.aggregate(pipeline))
            if group_data:
                for data in group_data:
                    item = {"task_id":str(data.get("task_id", "")), "result_id":str(data.get("new_result_id", ""))}
                    result.append(item)
            return result
        except Exception as e:
            et_log.error(f"query_new_id_by_task_ids exception:{e}\n{traceback.print_stack()}")
            return []


