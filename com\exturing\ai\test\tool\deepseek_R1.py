from openai import OpenAI
from com.exturing.ai.test.comm.comm_constant import QIANFAN_MODELCONTROL_URL, QIANFAN_MODELCONTROL_API_KEY


def ds_r1_chat_speak(query,model_url,model_ak,model_bot,system_prompt):

    client = OpenAI(
      base_url= model_url,
      api_key= model_ak,
   )
    if system_prompt:
        system_prompt = system_prompt
    else:
        system_prompt = "你好"


    completion = client.chat.completions.create(
        model=model_bot,  # 原始基座模型
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": query},
        ],
        temperature=0.01,
        stream = False,
        )


    return completion.choices[0].message.content


# if __name__ == "__main__":
#     query = "给我推荐一些苏州自驾游路线"  # 用户输入的问题，需根据实际情况修改
#     responce=ds_r1_chat_speak(query)
#     print(f"______________{responce}")