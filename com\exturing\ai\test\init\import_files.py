import datetime
import os

from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.service.origin_data_item_service import create, check_question


def read_files_in_folder(folder_path, format=0):
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"文件夹 {folder_path} 不存在。")
        return

    question_set = set()
    type_set = set()
    # 遍历文件夹下的所有文件
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            print(f"正在读取文件: {file_path}")
            try:
                # 打开文件并按行读取内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        # 去除行末的换行符
                        row = line.strip()
                        row = row[1:] if row and row[0] == ',' else row
                        print(row)
                        cols = row.split(",")
                        if format == 1:
                            type_set.add(cols[2])
                            if "闲聊及百科大问答" == cols[2]:
                                question_set.add(cols[1])
                        else:
                            type_set.add(cols[1])
                            if "闲聊及百科大问答" == cols[1]:
                                question_set.add(cols[0])
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
    return question_set, type_set


def read_file_path(file_path):
    workbook = None
    prd_tag = "67b6d82d13c368c66caa89d1" # 生产数据 标签
    valid_tag = "67b6d83a13c368c66caa89d2" # 有效数据 标签
    invalid_tag = "67b6f28d13c368c66caa89d3" # 无效数据 标签
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件 {file_path} 不存在。")
            return

        CTX_OEM_CODES.set("all")
        # 打开 Excel 文件
        workbook = load_workbook(file_path)
        # 获取所有表名
        sheet_names = workbook.sheetnames

        # 遍历不同的工作表
        for sheet_name in sheet_names:
            if "Sheet" in sheet_name:# 跳过未标注数据
                continue
            sheet = workbook[sheet_name]
            print(f"正在读取工作表: {sheet_name}")

            sheet_row_count = 0
            # 逐行读取工作表内容
            for row in sheet.rows:
                question = row[0].value
                if "语料" == question:
                    continue
                is_valid = valid_tag
                if row[1].value is None or 0 == int(row[1].value):
                    is_valid = invalid_tag
                insert_origin_item(question, f"{prd_tag},{is_valid}")

                sheet_row_count += 1
            print(f"{sheet_name} 已读取{sheet_row_count}条数据")
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
    finally:
        if workbook:
            workbook.close()

def insert_origin_item(question, data_tags):
    model = "678b3f2ba3bd2780819d0c03"# 百度百科
    dimension = "678b3f2ca3bd2780819d0c18"# 闲聊
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    def_user = 9
    count = check_question(question)
    if count > 0:
        return
    origin_dic = {
        "create_time": current_time,
        "create_by": def_user,
        "update_time": current_time,
        "update_by": def_user,
        "is_del": 0,
        "parent_id": "",
        "model_id": model,
        "dimension_id": dimension,
        "question": question,
        "expected_answer": "",
        "expected_task": "",
        "expected_category": "",
        "expected_url": 0,
        "expected_cmd": 0,
        "expected_error_id": "",
        "is_last": 1,
        "qa_keywords": "",
        "oem_codes": "all",
        "data_src": "",
        "data_tag_ids": data_tags
    }
    origin_data = OriginDataItemModel(**origin_dic)
    create(origin_data)




# 指定文件夹路径
# folder_path = r"""C:\work\test_eval\生产数据\用户说法1"""
# question_collection, type_set = read_files_in_folder(folder_path, 0)
# folder_path = r"""C:\work\test_eval\生产数据\用户说法1-1"""
# question_collection_1, type_set_1 = read_files_in_folder(folder_path, 1)
# question_collection = question_collection.union(question_collection_1)
# type_set = type_set.union(type_set_1)
# folder_path = r"""C:\work\test_eval\生产数据\用户说法2"""
# question_collection_2, type_set_2 = read_files_in_folder(folder_path, 0)
# question_collection = question_collection.union(question_collection_2)
# type_set = type_set.union(type_set_2)
# folder_path = r"""C:\work\test_eval\生产数据\用户说法2-1"""
# question_collection_2_2, type_set_2_2 = read_files_in_folder(folder_path, 1)
# question_collection = question_collection.union(question_collection_2_2)
# type_set = type_set.union(type_set_2_2)
# print(len(question_collection))
# print(type_set)

# # 创建一个新的工作簿
# workbook = Workbook()
# # 获取活动工作表
# sheet = workbook.active
#
# # sheet.append(["Q1", "A1_answer"])
# # 逐行处理文本内容
# for question in question_collection:
#     # # 去除行末的换行符
#     # line = line.strip()
#     # # 按逗号分割每行文本，得到单元格数据列表
#     # cells = line.split(',')
#     # 将单元格数据添加到工作表的一行中
#     if not question or len(question) == 0:
#         continue
#     sheet.append([question])
#
# # 保存工作簿为 Excel 文件
# output_file_path = 'C:\work\py_workspace\exturing_ai_test\output.xlsx'
# workbook.save(output_file_path)
# print(f"Excel 文件已保存到 {output_file_path}")


# model = "678b3f2ba3bd2780819d0c03"
# dimension = "678b3f2ca3bd2780819d0c18"
#
# current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
# def_user = 1
# for question in question_collection:
#     count = check_question(question)
#     if count > 0:
#         continue
#     origin_dic = {
#         "create_time": current_time,
#         "create_by": def_user,
#         "update_time": current_time,
#         "update_by": def_user,
#         "is_del": 0,
#         "parent_id": "",
#         "model_id": model,
#         "dimension_id": dimension,

#         "question": question,
#         "expected_answer": "",
#         "expected_task": "",
#         "expected_category": "",
#         "expected_url": 0,
#         "expected_cmd": 0,
#         "expected_error_id": "",
#         "is_last": 1,
#         "qa_keywords": ""
#     }
#     origin_data = OriginDataItemModel(**origin_dic)
#     create(origin_data)


# file_path = r"""C:\work\test_eval\生产数据\标注后-output.xlsx"""

# read_file_path(file_path)
# print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

# file_path = r"""C:\work\test_eval\生产数据\闲聊数据标注.xlsx"""
# read_file_path(file_path)