from datetime import datetime

from com.exturing.ai.test.comm.log_tool import et_log


class DateUtil:

    @classmethod
    def date_format_convert(cls, date_str: str):
        """
        日期格式转换  2025/3/8 转换为 2025-03-08 00:00:00
        :param date_str:字串2025/3/8格式日期
        :return:返回2025-03-08 00:00:00格式日期
        """
        et_log.info(f"date_format_convert date_str:{date_str}")
        if date_str is None or len(date_str) == 0:
            et_log.error(f"date_format_convert date_str is empty")
            return ""

        # 解析日期字符串为 datetime 对象
        date_obj = datetime.strptime(date_str, '%Y/%m/%d')

        # 将 datetime 对象格式化为目标字符串
        formatted_date = date_obj.strftime('%Y-%m-%d %H:%M:%S')
        return formatted_date