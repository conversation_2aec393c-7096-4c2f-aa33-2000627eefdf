# service.py

from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.model.origin_data_item import (
    OriginDataItemModel,
    OriginDataItemQueryModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log
from werkzeug.datastructures import FileStorage
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.service.data_tag_service import _doc as data_tag_doc
import pandas as pd
import math
from io import BytesIO
from com.exturing.ai.test.comm.comm_util import str_format, int_format, time_format

_doc = "origin_data_item"


# 新增OriginDataItem
def create(data: OriginDataItemModel) -> str:
    data_dict = data.model_dump()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data_dict["oem_codes"] = CTX_OEM_CODES.get()
    data_dict["create_time"] = current_time
    et_log.info(f"create origin_data_item: {data_dict}")
    result = MongoDBUtil.insert_one(_doc, data_dict)

    parent_id = data_dict.get("parent_id")

    if parent_id:
        parent_data = MongoDBUtil.find_by_id(_doc, parent_id)
        if parent_data:
            parent_data["is_last"] = 0
            MongoDBUtil.update_one_pro(_doc, parent_data)

    return result


# 分页查询OriginDataItem
def query_page(
    page_num: int = 1, page_size: int = 10, query: OriginDataItemQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询OriginDataItem的子项
def query_children(parent_id: str):
    def get_child(p_id):
        result = []
        condition = {"parent_id": p_id, "is_del": 0}
        list = MongoDBUtil.find_condition_page(_doc, condition, None, 0, 0)

        if list is not None:
            for item in list:
                result.append(item)
                result.extend(get_child(str(item.get("_id"))))

        return result

    json_list = get_child(parent_id)

    return [MongoDBUtil.serialize_document(doc) for doc in json_list]


# 修改OriginDataItem
def update(id: str, data: OriginDataItemModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data_dict["update_time"] = current_time
    et_log.info(f"update origin_data_item: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 批量修改原始语料数据
def update_batch(ids: list, data: dict) -> bool:
    update_dict = {}

    if data.get("model_id"):
        update_dict["model_id"] = data.get("model_id")
        update_dict["dimension_id"] = data.get("dimension_id", "")
    if data.get("item_type") is not None:
        update_dict["item_type"] = data.get("item_type", 0)
    if data.get("qa_keywords"):
        update_dict["qa_keywords"] = data.get("qa_keywords")
    if data.get("data_tag_ids"):
        update_dict["data_tag_ids"] = data.get("data_tag_ids")
    if data.get("exec_user_id"):
        update_dict["exec_user_id"] = data.get("exec_user_id")
    if data.get("expected_category"):
        update_dict["expected_category"] = data.get("expected_category")
    if data.get("expected_task"):
        update_dict["expected_task"] = data.get("expected_task")
    if data.get("expected_answer"):
        update_dict["expected_answer"] = data.get("expected_answer")

    et_log.info(f"update_batch origin_data_item: {update_dict}")
    condition = {"_id": {"$in": [ObjectId(id) for id in ids]}}

    result = MongoDBUtil.update_many(_doc, condition, update_dict)

    if result.modified_count and result.modified_count > 0:
        return True
    else:
        return False


# 删除OriginDataItem
def delete(id: str) -> bool:
    et_log.info(f"delete origin_data_item by id:{id}")

    delete_data = MongoDBUtil.find_by_id(_doc, id)

    # 将删除项的子项都添加到删除列表中
    def append_del_children(p_id, ids = []):
        condition = {"parent_id": p_id}
        list = MongoDBUtil.find_condition(_doc, condition)

        if list is not None:
            for item in list:
                append_del_children(str(item.get("_id")), ids)
                ids.append(str(item.get("_id")))

    need_del_ids = [id]
    append_del_children(id, need_del_ids)

    # 批量删除
    condition = {"_id": {"$in": [ObjectId(id) for id in need_del_ids]}}
    del_count = MongoDBUtil.delete_by_many(_doc, condition)

    parent_id = delete_data.get("parent_id")
    # 将父轮的is_last置为1
    if parent_id:
        parent_data = MongoDBUtil.find_by_id(_doc, parent_id)
        if parent_data:
            parent_data["is_last"] = 1
            MongoDBUtil.update_one_pro(_doc, parent_data)

    return del_count


# 批量删除OriginDataItem
def delete_batch(ids: list) -> bool:
    et_log.info(f"delete_batch origin_data_item by ids:{ids}")

    need_del_ids = ids.copy()

    def append_children_ids(p_ids, ids=[]):
        condition = {"parent_id": {"$in": p_ids}}
        list = MongoDBUtil.find_condition(_doc, condition)

        if list is not None:
            children_ids = [str(item.get("_id")) for item in list]
            ids.extend(children_ids)

            append_children_ids(children_ids, ids)

    append_children_ids(ids, need_del_ids)

    condition = {"_id": {"$in": [ObjectId(id) for id in need_del_ids]}}

    del_count = MongoDBUtil.delete_by_many(_doc, condition)

    return del_count


# 原始语料检查
def check_question(question: str):
    et_log.info(f"check_question origin_data_item question:{question}")
    return MongoDBUtil.find_count(_doc, {"question": question})


# 原始语料查询
def query_question(question: str):
    et_log.info(f"query_question origin_data_item question:{question}")
    return MongoDBUtil.find_one(_doc, {"question": question})


# 导入原始数据
def import_data(file: FileStorage):
    all_sheets = pd.read_excel(file, sheet_name=None)

    model_list = DicService.query_list(None, None, "dm-model") or []
    dimension_list = DicService.query_list(None, None, "dm-dimension") or []

    for model in model_list:
        for dimension in dimension_list:
            if dimension.get("pid") == model.get("_id"):
                model["children"] = model.get("children") or []
                model["children"].append(dimension)

    for sheet_name in all_sheets:
        df = all_sheets[sheet_name]
        list = df.to_dict(orient="records")

        for item in list:
            try:
                insert_data_item(item, model_list)
            except Exception as e:
                et_log.error(f"import_data error: {e}")
                return False

    return True


# 插入语料数据
def insert_data_item(data: dict, model_list: list):
    index = 1  # 语料轮数

    if not data.get(f"Q{index}"):  # 语料为空
        return

    result = MongoDBUtil.find_one(_doc, {"question": data.get(f"Q{index}")})

    if result:  # 语料已存在
        return

    parent_id = None
    model_id = None
    dimension_id = None

    while data.get(f"Q{index}") is not None:
        data = remove_nan_fields(data)

        if data.get(f"Q{index}_model"):
            models = [
                item
                for item in model_list
                if item.get("name") == data.get(f"Q{index}_model")
            ]
            model = models[0] if models else None
            if model is not None:
                model_id = model.get("_id")
                dimensions = [
                    item
                    for item in model.get("children") or []
                    if item.get("name") == data.get(f"Q{index}_dimension")
                ]
                dimension = dimensions[0] if dimensions else None
                if dimension is not None:
                    dimension_id = dimension.get("_id")

        data_item = OriginDataItemModel(
            model_id=str(model_id) if model_id else "",
            dimension_id=str(dimension_id) if dimension_id else "",
            parent_id=parent_id,
            data_src=str_format("data_src", data),
            question=str_format(f"Q{index}", data),
            expected_answer=str_format(f"A{index}_expected", data),
            expected_task=str_format(f"Q{index}_task_expected", data),
            expected_category=str_format(f"Q{index}_category_expected", data),
            expected_url=int_format(f"Q{index}_url", data),
            expected_cmd=int_format(f"Q{index}_cmd", data),
            expected_error_id=str_format(f"Q{index}_errorId", data),
            is_last=0 if data.get(f"Q{index+1}") is not None else 1,
            qa_keywords=str_format(f"Q{index}_keyword", data),
            real_time=time_format(f"Q{index}_real_time", data),
        )

        if not data_item.question:
            return

        parent_id = str(create(data_item))
        index += 1


def remove_nan_fields(d: dict) -> dict:
    return {
        key: value
        for key, value in d.items()
        if not (isinstance(value, float) and math.isnan(value))
    }


# id查询原始语料
def find_pk(_id: str):
    et_log.info(f"find_pk origin_data_item id:{_id}")
    item = MongoDBUtil.find_by_id(_doc, _id)
    if item:
        return OriginDataItemModel(**item)
    return None


def find_child(pid: str):
    et_log.info(f"find_child origin_data_item pid:{pid}")
    child = MongoDBUtil.find_condition(_doc, {"parent_id": ObjectId(pid)})
    if child:
        return OriginDataItemModel(**child[0])
    return None


def export_data(query: OriginDataItemQueryModel, export_keys=[]):
    condition = query.get_query_condition()
    # 获取原始语料数据
    result = MongoDBUtil.find_condition(_doc, condition)

    # 获取模型和维度数据
    models = DicService.query_list(None, None, "dm-model") or []
    dimensions = DicService.query_list(None, None, "dm-dimension") or []
    tags = MongoDBUtil.find_condition(data_tag_doc, {"is_del": 0})

    data_list = list(result or [])
    model_list = list(models or [])
    dimension_list = list(dimensions or [])
    tag_list = list(tags or [])

    export_list = []

    for data in data_list:
        # 获取模型和维度名称
        if data.get("model_id"):
            data["model_name"] = [
                item.get("name")
                for item in model_list
                if item.get("_id") == data.get("model_id")
            ][0]

        if data.get("dimension_id"):
            data["dimension_name"] = [
                item.get("name")
                for item in dimension_list
                if item.get("_id") == data.get("dimension_id")
            ][0]

            if data.get("model_name") is None:
                data["model_name"] = [
                    item.get("name")
                    for item in model_list
                    if item.get("_id")
                    == [
                        item.get("pid")
                        for item in dimension_list
                        if item.get("_id") == data.get("dimension_id")
                    ][0]
                ][0]

        # 获取标签名称
        if data.get("data_tag_ids"):
            data_tag_ids = data.get("data_tag_ids").split(",")
            data["data_tag_names"] = ",".join(
                [
                    item.get("tag_name")
                    for item in tag_list
                    if str(item.get("_id")) in data_tag_ids
                ]
            )

        index = 1
        export_item = {}
        export_item[f"data_src"] = data.get("data_src")
        export_item = formatData(data, index, export_item, export_keys)

        # 查询子轮数据并格式化
        if data.get("is_last") != 1:
            children = query_children(str(data.get("_id")))
            for child in children:
                index += 1
                export_item = formatData(child, index, export_item, export_keys)

        export_list.append(export_item)

    # 导出数据
    export_df = pd.DataFrame(export_list)
    output = BytesIO()

    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        export_df.to_excel(writer, sheet_name="原始语料数据", index=False)

    excel_binary = output.getvalue()
    output.close()

    return excel_binary


def formatData(data_item, index, export_item={}, export_keys=[]):
    export_all = export_keys is None or len(export_keys) < 1

    if export_all or "data_src" in export_keys:
        export_item[f"data_src"] = data_item.get("data_src")
    if export_all or "model" in export_keys:
        export_item[f"Q{index}_model"] = data_item.get("model_name")
    if export_all or "dimension" in export_keys:
        export_item[f"Q{index}_dimension"] = data_item.get("dimension_name")
    if export_all or "question" in export_keys:
        export_item[f"Q{index}"] = data_item.get("question")
    if export_all or "expected_answer" in export_keys:
        export_item[f"A{index}_expected"] = data_item.get("expected_answer")
    if export_all or "expected_task" in export_keys:
        export_item[f"Q{index}_task_expected"] = data_item.get("expected_task")
    if export_all or "expected_category" in export_keys:
        export_item[f"Q{index}_category_expected"] = data_item.get("expected_category")
    if export_all or "qa_keywords" in export_keys:
        export_item[f"Q{index}_keyword"] = data_item.get("qa_keywords")
    if export_all or "expected_url" in export_keys:
        export_item[f"Q{index}_url"] = data_item.get("expected_url")
    if export_all or "expected_cmd" in export_keys:
        export_item[f"Q{index}_cmd"] = data_item.get("expected_cmd")
    if export_all or "expected_error_id" in export_keys:
        export_item[f"Q{index}_errorId"] = data_item.get("expected_error_id")
    if export_all or "tags" in export_keys:
        export_item[f"A{index}_tags"] = data_item.get("data_tag_names")
    if export_all or "real_time" in export_keys:
        export_item[f"A{index}_real_time"] = data_item.get("real_time")

    return export_item
