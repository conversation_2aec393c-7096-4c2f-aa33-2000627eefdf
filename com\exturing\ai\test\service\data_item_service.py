import traceback
from datetime import datetime
from typing import Dict

import pymongo
from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID
from com.exturing.ai.test.comm.comm_util import list_to_tree
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.service.data_set_service import DataSetService
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.service.data_tag_service import _doc as data_tag_doc
import pandas as pd
from io import BytesIO


# 数据集数据项Service
class DataItemService:

    # 新增数据集数据项
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one data_set_item post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            pid = post_data["parent_id"]
            set_id = post_data["set_id"]
            question = post_data["question"]
            bol_val = cls.check_question(
                set_id, post_data["model_id"], post_data["dimension_id"], question
            )
            if bol_val:
                et_log.error(
                    f"set_id:{set_id} question:{question} has already been used"
                )
                return None

            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = EtDataSetItem(
                "",
                current_time,
                post_data["do_user"],
                current_time,
                post_data["do_user"],
                0,
                ctx_oem_codes,
                post_data["parent_id"],
                ObjectId(set_id),
                post_data["metric_id"],
                post_data["model_id"],
                post_data["dimension_id"],
                post_data["item_type"],
                post_data["scene_id"],
                post_data["question"],
                post_data["expected_answer"],
                post_data["expected_task"],
                post_data["expected_category"],
                post_data["item_tags"],
                1,
                post_data["real_time"],
                post_data.get("qa_keywords", ""),
                post_data.get("data_src", ""),
            ).insert_one()
            # 检查数据项的父节点是否需要改写is_last标识
            if insert_id and len(str(insert_id)) > 0 and len(str(pid)) > 0:
                parent_item: EtDataSetItem = cls.find_pk(pid)
                if 1 == parent_item.is_last:
                    parent_item.is_last = 0
                EtDataSetItem.update_entity_json(
                    parent_item.to_json()
                )  # 更改is_last标识

            DataSetService.update_dataset_count(set_id)
            return insert_id
        except Exception as e:
            et_log.error(f"insert_one data_set_item exception")
            traceback.print_exc()
            return None

    # 根据主键查询数据集数据项对象
    @classmethod
    def find_pk(cls, _id):
        data_item = EtDataSetItem.find_by_pk(_id)
        if data_item is not None:
            return EtDataSetItem(**data_item)
        return None

        # 根据set_id返回所有数据

    @classmethod
    def find_all_by_set_id(cls, set_id):
        return EtDataSetItem.find_all_by_set_id(set_id)

        # 根据名称搜索数据集(用于名称校验)

    @classmethod
    def find_dataname(cls, name, set_id):
        return EtDataSetItem.find_nameondition(set_id, None, None, name, 10, 0)

    # 根据名称搜索数据集(用于名称校验)
    @classmethod
    def find_name(cls, name, set_id):
        return EtDataSetItem.find_page_condition(
            set_id, None, None, name, {}, [], 10, 0
        )

    # 数据集 数据项 查询
    @classmethod
    def query_page(
        cls,
        set_id,
        model_id,
        dimension_id,
        name: str,
        model_dimension,
        item_tags,
        page_num: int,
        page_size: int,
        real_time_start,
        real_time_end,
    ):
        et_log.info(
            f"query_page set_id:{set_id}, model_id:{model_id}, dimension_id:{dimension_id}, name:{name}, "
            f"model_dimension:{model_dimension}, item_tags:{item_tags}, page_num:{page_num}, page_size:{page_size}, "
            f"real_time_start:{real_time_start}, real_time_end:{real_time_end}"
        )
        # 查询匹配的数量
        total = EtDataSetItem.find_page_count(
            set_id,
            model_id,
            dimension_id,
            name,
            model_dimension,
            item_tags,
            real_time_start,
            real_time_end,
        )
        if total < 1:  # 未匹配到结果
            return None
        page = PageResult(page_num, page_size, total)
        item_list = EtDataSetItem.find_page_condition(
            set_id,
            model_id,
            dimension_id,
            name,
            model_dimension,
            item_tags,
            page_size,
            page.skip,
            real_time_start,
            real_time_end,
        )
        if item_list is None or len(item_list) < 1:
            et_log.error(f"query not found data error")
            return None
        item_json_list = []
        for item in item_list:  # 返回集合转json格式
            item_json_list.append(item.to_json_str())
        page.page_data = item_json_list
        return page

    # 删除数据集
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"data_set_item delete by _id:{_id} do_user:{do_user}")
        try:
            del_item: EtDataSetItem = EtDataSetItem.find_by_pk(_id)
            if del_item is None:
                et_log.error(f"data_set_item delete by _id error, _id not found item")
                return False
            # 删除id对应的数据集记录
            EtDataSetItem.delete_by_id(_id, do_user)
            # 根据id检查，该数据项是否有子节点，若有则需更新=所有子节点都逻辑删除
            EtDataSetItem.del_by_pid(_id, do_user)
            # 根据当前节点pid，检查其是否还有子节点；若无，则需更新当前节点的父节点is_last标识=1;否则，不动
            parent_id = del_item["parent_id"]
            if parent_id and len(parent_id) > 0:
                count = EtDataSetItem.find_count_by_pid(parent_id)
                if 0 == count:  # 父节点当前有效子节点无数据时
                    parent_item: EtDataSetItem = EtDataSetItem.find_by_pk(parent_id)
                    parent_item.is_last = 1
                    if do_user and do_user > 0:
                        parent_item.update_by = do_user
                    EtDataSetItem.update_entity_json(
                        parent_item.to_json()
                    )  # 更新父节点is_last标识

            DataSetService.update_dataset_count(del_item["data_set_id"])
            return True
        except Exception as e:
            et_log.error(f"data_set_item delete by _id exception")
            traceback.print_exc()
            return False

    # 更新数据集数据项
    @classmethod
    def update(cls, data_item: EtDataSetItem):
        et_log.info(f"data_set_item update by data_item:{data_item}")
        try:
            # 当前更新的节点是最终节点，需检查重复性；否则不需检查重复性
            if 1 == data_item.is_last:
                # 检查数据项数据是否重复
                items = EtDataSetItem.find_page_condition(
                    str(data_item.data_set_id),
                    str(data_item.model_id),
                    str(data_item.dimension_id),
                    str(data_item.question),
                    {},
                    [],
                    10,
                    0,
                )
                if items and len(items) > 0:
                    for data_em in items:
                        if str(data_item.id) != str(data_em.id):  # 检查表明有重复的问题
                            et_log.error(f"data_set_item error, repeat question")
                            return 0
            # 检查是否更新对应的原数据 model_id|dimension_id|expected_task|expected_category|expected_answer
            update_data = {
                "model_id": data_item.model_id,
                "dimension_id": data_item.dimension_id,
                "expected_task": data_item.expected_task,
                "expected_category": data_item.expected_category,
                "expected_answer": data_item.expected_answer,
            }
            EtDataSetItem.revise_dataset_update_origin_item(data_item.id, update_data)
            # 更新请求的数据项数据
            return EtDataSetItem.update_entity_json(data_item.to_json())
        except Exception as e:
            et_log.error(f"data_set_item update by data_set exception")
            traceback.print_exc()
            return 0

    # 检查数据集中的语料是否重复
    @classmethod
    def check_question(cls, set_id, model_id, dimension_id, question):
        total = EtDataSetItem.find_page_count(
            set_id, model_id, dimension_id, question, {}, []
        )
        if total > 0:
            return True
        else:
            return False

    # 根据父数据项id,获取其下所有子节点数据列表
    @classmethod
    def query_child_list(cls, item_pid):
        et_log.info(f"query_child_list param item_pid:{item_pid}")
        try:
            json_list = []
            count = EtDataSetItem.find_count_by_pid(item_pid)
            if count < 1:
                return json_list

            result_list = EtDataSetItem.find_by_pid(item_pid)
            if result_list and len(result_list) > 0:
                for item in result_list:
                    json_list.append(item.to_json_str())
                    child_list = cls.query_child_list(item.id)
                    if child_list and len(child_list) > 0:
                        json_list.extend(child_list)
            return json_list
        except Exception as e:
            et_log.error(f"query_child_list query data_set_item child list exception")
            traceback.print_exc()
            return []

    # 根据data_set_id获取第一轮数据项列表
    @classmethod
    def query_by_set_id(cls, set_id):
        et_log.info(f"query_by_set_id param set_id:{set_id}")
        try:
            if set_id is None or len(str(set_id)) == 0:
                et_log.error(f"query_by_set_id set_id is null")
                return None
            condition = {"data_set_id": ObjectId(str(set_id)), "parent_id": ""}
            count = EtDataSetItem.find_comm_condition_count(condition)
            if count < 1:
                et_log.error(f"query_by_set_id set_id is invalid")
                return None

            result_list = EtDataSetItem.find_comm_condition(
                condition, [("_id", pymongo.ASCENDING)], count, 0
            )
            if result_list is None or len(result_list) == 0:
                et_log.error(f"query_by_set_id set_id is not found items")
                return None
            return result_list
        except Exception as e:
            et_log.error(f"query_child_list query data_set_item child list exception")
            traceback.print_exc()
            return None

    # 根据data_set_id更新数据项信息
    @classmethod
    def update_by_set_id(cls, set_id, data):
        et_log.info(f"update_by_set_id param set_id:{set_id} update_dict:{data}")
        try:
            condition = {"data_set_id": ObjectId(str(set_id))}

            update_dict = {}
            if data.get("model_id"):
                update_dict["model_id"] = data.get("model_id")
                update_dict["dimension_id"] = data.get("dimension_id", "")
            if data.get("item_type") is not None:
                update_dict["item_type"] = data.get("item_type", 0)
            if data.get("metric_id", ""):
                update_dict["metric_id"] = data.get("metric_id", "")
            if data.get("item_tags", ""):
                update_dict["item_tags"] = data.get("item_tags", "")
            if data.get("expected_category"):
                update_dict["expected_category"] = data.get("expected_category")
            if data.get("expected_task"):
                update_dict["expected_task"] = data.get("expected_task")
            if data.get("expected_answer"):
                update_dict["expected_answer"] = data.get("expected_answer")

            return EtDataSetItem.update_batch_json(condition, update_dict)
        except Exception as e:
            et_log.error(f"update_by_set_id update data_set_item exception")
            traceback.print_exc()
            return None

    # 根据ids更新数据项信息
    @classmethod
    def update_batch(cls, ids, data):
        et_log.info(f"update_by_ids param ids:{ids} update_dict:{data}")
        try:
            _ids = [ObjectId(id) for id in ids]
            condition = {"_id": {"$in": _ids}}

            update_dict = {}
            if data.get("model_id"):
                update_dict["model_id"] = data.get("model_id")
                update_dict["dimension_id"] = data.get("dimension_id", "")
            if data.get("item_type") is not None:
                update_dict["item_type"] = data.get("item_type", 0)
            if data.get("metric_id", ""):
                update_dict["metric_id"] = data.get("metric_id", "")
            if data.get("item_tags", ""):
                update_dict["item_tags"] = data.get("item_tags", "")
            if data.get("expected_category"):
                update_dict["expected_category"] = data.get("expected_category")
            if data.get("expected_task"):
                update_dict["expected_task"] = data.get("expected_task")
            if data.get("expected_answer"):
                update_dict["expected_answer"] = data.get("expected_answer")

            return EtDataSetItem.update_batch_json(condition, update_dict)
        except Exception as e:
            et_log.error(f"update_by_ids update data_set_item exception")
            traceback.print_exc()
            return None

    # 根据ids删除数据项
    @classmethod
    def delete_batch(cls, data_set_id, ids):
        et_log.info(f"data-item delete_batch param data_set_id:{data_set_id} ids:{ids}")
        try:
            # 获取数据集中所有数据项
            data_items = MongoDBUtil.find_condition(
                EtDataSetItem._doc, {"data_set_id": ObjectId(data_set_id)}
            )

            data_item_list = list(data_items or [])
            data_item_tree = list_to_tree(data_item_list)

            # 递归获取所有子节点id
            def get_children_ids(item):
                id_list = []
                children = item.get("children", [])
                for child in children:
                    id_list.append(child.get("_id"))
                    id_children = get_children_ids(child)
                    id_list.extend(id_children)

                return id_list

            # 获取需要删除的数据项id,包括子节点
            del_ids = []
            for item in data_item_tree:
                id = item.get("_id")
                if str(id) in ids:
                    del_ids.append(id)
                    del_ids.extend(get_children_ids(item))

            # 删除数据项
            condition = {"_id": {"$in": del_ids}}

            del_count = MongoDBUtil.delete_by_many(EtDataSetItem._doc, condition)
            DataSetService.update_dataset_count(data_set_id)
            return del_count

        except Exception as e:
            et_log.error(f"delete_batch delete data_set_item exception")
            traceback.print_exc()
            return None

    # 根据data_set_id导出数据项数据
    @classmethod
    def export_data(cls, set_id, export_keys=[]):
        result = MongoDBUtil.find_condition(
            EtDataSetItem._doc,
            {"data_set_id": ObjectId(set_id), "parent_id": {"$in": [None, ""]}},
        )
        data_list = list(result or [])

        # 获取模型和维度数据
        models = DicService.query_list(None, None, "dm-model") or []
        dimensions = DicService.query_list(None, None, "dm-dimension") or []
        tags = MongoDBUtil.find_condition(data_tag_doc, {"is_del": 0})

        model_list = list(models or [])
        dimension_list = list(dimensions or [])
        tag_list = list(tags or [])

        export_list = []

        for data in data_list:
            # 获取模型和维度名称
            if data.get("model_id"):
                model_names = [
                    item.get("name")
                    for item in model_list
                    if str(item.get("_id")) == str(data.get("model_id"))
                ]
                if model_names and len(model_names) > 0:
                    data["model_name"] = model_names[0]

            if data.get("dimension_id"):
                dimension_names = [
                    item.get("name")
                    for item in dimension_list
                    if str(item.get("_id")) == str(data.get("dimension_id"))
                ]
                if dimension_names and len(dimension_names) > 0:
                    data["dimension_name"] = dimension_names[0]

                if (
                    data.get("model_name") is None
                    and data.get("dimension_name") is not None
                ):
                    try:
                        data["model_name"] = [
                            item.get("name")
                            for item in model_list
                            if item.get("_id")
                            == [
                                item.get("pid")
                                for item in dimension_list
                                if item.get("_id") == data.get("dimension_id")
                            ][0]
                        ][0]
                    except:
                        data["model_name"] = None

            # 获取标签名称
            if data.get("item_tags"):
                data_tag_ids = data.get("item_tags").split(",")
                data["data_tag_names"] = ",".join(
                    [
                        item.get("tag_name")
                        for item in tag_list
                        if str(item.get("_id")) in data_tag_ids
                    ]
                )

            index = 1
            export_item = {}
            export_item = formatData(data, index, export_item, export_keys)

            # 查询子轮数据并格式化
            if data.get("is_last") != 1:
                children = cls.query_child_list(str(data.get("_id")))
                for child in children:
                    index += 1
                    export_item = formatData(child, index, export_item, export_keys)

            export_list.append(export_item)

        # 导出数据
        export_df = pd.DataFrame(export_list)
        output = BytesIO()

        with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
            export_df.to_excel(writer, sheet_name="数据集数据", index=False)

        excel_binary = output.getvalue()
        output.close()

        return excel_binary


def formatData(data_item, index, export_item={}, export_keys=[]):
    export_all = export_keys is None or len(export_keys) < 1

    if export_all or "data_src" in export_keys:
        export_item[f"data_src"] = data_item.get("data_src")
    if export_all or "model" in export_keys:
        export_item[f"Q{index}_model"] = data_item.get("model_name")
    if export_all or "dimension" in export_keys:
        export_item[f"Q{index}_dimension"] = data_item.get("dimension_name")
    if export_all or "question" in export_keys:
        export_item[f"Q{index}"] = data_item.get("question")
    if export_all or "expected_answer" in export_keys:
        export_item[f"A{index}_expected"] = data_item.get("expected_answer")
    if export_all or "expected_task" in export_keys:
        export_item[f"Q{index}_task_expected"] = data_item.get("expected_task")
    if export_all or "expected_category" in export_keys:
        export_item[f"Q{index}_category_expected"] = data_item.get("expected_category")
    if export_all or "qa_keywords" in export_keys:
        export_item[f"Q{index}_keyword"] = data_item.get("qa_keywords")
    if export_all or "expected_url" in export_keys:
        export_item[f"Q{index}_url"] = data_item.get("expected_url")
    if export_all or "expected_cmd" in export_keys:
        export_item[f"Q{index}_cmd"] = data_item.get("expected_cmd")
    if export_all or "expected_error_id" in export_keys:
        export_item[f"Q{index}_errorId"] = data_item.get("expected_error_id")
    if export_all or "tags" in export_keys:
        export_item[f"A{index}_tags"] = data_item.get("data_tag_names")
    if export_all or "real_time" in export_keys:
        export_item[f"A{index}_real_time"] = data_item.get("real_time")

    return export_item


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))
