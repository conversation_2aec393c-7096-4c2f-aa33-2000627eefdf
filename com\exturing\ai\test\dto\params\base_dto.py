from typing import Optional

from pydantic import BaseModel


class BaseDto(BaseModel):
    """
    公共的Header中Authorization值解析请求数据
    """
    request_id: Optional[str] = ""
    user_id: Optional[int] = None
    username: Optional[str] = ""
    remote_ip: Optional[str] = ""
    oem_codes: Optional[str] = ""

    def comm_dto_condition(self):
        """
        根据公共参数，构建查询条件dic
        :return: 公共参数中有值的参数dic
        """
        condition = {}
        if self.request_id is not None and len(self.request_id) > 0:
            condition["request_id"] = self.request_id
        if self.user_id is not None:
            condition["user_id"] = self.user_id
        if self.username is not None and len(self.username) > 0:
            condition["username"] = self.username
        if self.remote_ip is not None and len(self.remote_ip) > 0:
            condition["remote_ip"] = self.remote_ip
        if self.oem_codes is not None and len(self.oem_codes) > 0:
            condition["oem_codes"] = self.oem_codes
        return condition
