# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.project import ProjectModel, ProjectQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.project_service import (
    create,
    query_page,
    update,
    delete,
)

project = Blueprint("project", __name__)


# 新增项目
@project.route(f"/{URL_PREFIX}/staff/project/create", methods=["POST"])
def project_create():
    et_log.info("############project_create################")
    req_data = request.get_json()
    project_instance = ProjectModel(**req_data)
    project_id = create(project_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(project_id)
    ).to_json()


# 项目分页查询
@project.route(f"/{URL_PREFIX}/staff/project/page", methods=["POST"])
def project_page():
    et_log.info("############project_page################")
    data = request.get_json()
    query = ProjectQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 修改项目
@project.route(f"/{URL_PREFIX}/staff/project/update", methods=["POST"])
def project_update():
    et_log.info("############project_update################")
    req_data = request.get_json()
    id = req_data.get("project_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
        ).to_json()

    project_instance = ProjectModel(**req_data)
    update_result = update(id, project_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
    ).to_json()


# 删除项目
@project.route(f"/{URL_PREFIX}/staff/project/del", methods=["POST"])
def project_delete():
    et_log.info("############project_delete################")
    req_data = request.get_json()
    id = req_data.get("project_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()
