from typing import Optional, Dict

from pydantic import BaseModel

from com.exturing.ai.test.enums.exception_enum import ResponseEnum


class ResponseSseDto(BaseModel):
    """
    通用SSE协议返回的对象结构
    """
    code: Optional[int] = 0   # 响应代码
    msg: Optional[str] = ""     # 响应描述
    result: Optional[Dict]    # 响应的结果对象(yield方式返回)

    @classmethod
    def construct_enum(cls, response_enum:ResponseEnum, msg="", data=None):
        """
        异常返回DTO
        :param response_enum: 通用返回枚举
        :param msg: 返回描述（若未传值，采用ExceptionEnum中的msg）
        :param data: 返回数据
        :return:
        """
        if msg and len(msg) > 0:
            return ResponseSseDto(**{"code":response_enum.get_code, "msg":msg, "result":data})
        else:
            return ResponseSseDto(**{"code":response_enum.get_code, "msg":response_enum.get_msg, "result":data})