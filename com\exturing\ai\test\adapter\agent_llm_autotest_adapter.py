import copy
import json
import traceback
from datetime import datetime
from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import dsk_prompt
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
from com.exturing.ai.test.service.dic_service import DicService


# adapter_code=agent-llm-test 适配agent autotest
class AgentLlmTestAdapter:

    # 单条记录运行
    @classmethod
    async def run_agent_autotest_item(cls, config_info: TestConfigInfoModel, item: EtDataSetItem, record_id, request_id, session_id,
                                      parent_id, task_id,
                                      task_result_id, do_user):
        et_log.info(
            f"run_agent_autotest_item params config_info:{config_info} item:{item} record_id:{record_id} request_id:{request_id} "
            f"session_id:{session_id} parent_id:{parent_id} task_id:{task_id} task_result_id:{task_result_id}")

        if item.dimension_id and not item.model_id:
            dimension = MongoDBUtil.find_one("dict_info", item.dimension_id)
            item.model_id = dimension["parent_id"]

        try:
            if item.model_id :
                model_data = DicService.find_pk(item.model_id).to_json_str()
                # 获取 model_data 中的 "name"，如果没有则默认为 "闲聊百科"
                model_name = model_data.get("name", "闲聊及百科大问答")
            else:
                model_name = "闲聊及百科大问答"
            if item.dimension_id:
                dimension_data = DicService.find_pk(item.dimension_id).to_json_str()
                dimension_name = dimension_data.get("name", "N/A")
                print(f"dimension_name:{dimension_name}")
            else:
                dimension_name = "N/A"
            message = copy.deepcopy(json.loads(config_info.env_script))
            message['context']['product']['productId'] = config_info.pid_val
            message['context']['product']['oem']['code'] = config_info.oem_code
            message['context']['device']['deviceName'] = config_info.device_id
            message['request']['task'] = model_name
            message['request']['inputs'][0]['input'] = item.question
            message['request']['timestamp'] = int(datetime.now().timestamp())
            message['request']['recordId'] = record_id
            message['request']['request_id'] = request_id
            message['session']['sessionId'] = session_id

            # headers = {
            #     "Content-Type": "application/json; charset=utf-8"
            # }
            # payload = json.dumps(message, ensure_ascii=False).encode('utf-8')
            uri = config_info.env_uri
            if config_info.branch_name and len(config_info.branch_name) > 0:
                uri = uri.format(alias=config_info.branch_name)
            if config_info.pid_val and len(config_info.pid_val) > 0:
                uri = uri.format(productId=config_info.pid_val)
            if config_info.device_id and len(config_info.device_id) > 0:
                uri = uri.format(deviceId=config_info.device_id)
            if config_info.user_id and len(config_info.user_id) > 0:
                uri = uri.format(userId=config_info.user_id)
            if config_info.version and len(config_info.version) > 0:
                uri = uri.format(version=config_info.version)
            start_now = datetime.now()
            actual_answer, cmd, qa_use_time, err_id, is_websearch, require_link, asrend_time = await dsk_prompt(message,
                                                                                                                uri,dimension_name)
            actual_task = model_name
            actual_category = model_name
            et_log.info(f"run_agent_autotest_item uri:{uri} "
                        f"data:{message} "
                        f"actual_answer:{actual_answer} cmd:{cmd} qa_use_time:{qa_use_time} err_id:{err_id} is_websearch:{is_websearch}")
            if not actual_answer or len(actual_answer) == 0:
                et_log.error(f"run_agent_autotest_item actual_answer is null")
                return None
            result_answer = 0
            result_task = 0
            result_category = 0
            if item.expected_answer and len(item.expected_answer) > 0 and str(item.expected_answer) in str(
                    actual_answer):
                result_answer = 1
            # if item.expected_task and len(item.expected_task) > 0 and str(item.expected_task) in str(actual_task):
            result_task = 1
            # if item.expected_category and len(item.expected_category) > 0 and str(item.expected_category) in str(
            #         actual_category):
            result_category = 1
            re_interval_time = -99
            if item.real_time and len(item.real_time) > 0:
                if isinstance(item.real_time, datetime):
                    re_interval_time = (datetime.now() - item.real_time).days
                else:
                    re_interval_time = (datetime.now() - datetime.strptime(item.real_time, "%Y/%m/%d")).days
            et_log.info(f"_______________item_result{item}")
            test_task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
            # set_item.expected_task, set_item.expected_category, set_item.expected_answer,
            item_result = {"parent_id": parent_id, "data_set_item_id": str(item.id), "task_id": task_id,
                           "task_result_id": str(task_result_id), "expected_answer": item.expected_answer,
                           "expected_category": model_name, "expected_task": model_name,
                           "actual_category": model_name, "result_category": model_name,
                           "actual_answer": actual_answer, "result_answer": result_answer, "answer_score": 0,
                           "actual_task": actual_task, "result_task": result_task, "qa_recall": 0, "qa_keywords": "",
                           "first_res_time": qa_use_time, "qa_use_time": asrend_time, "result_final": result_answer,
                           "recall_id": record_id, "remark": f"request_id:{request_id}|session_id:{session_id}",
                           "do_user": do_user, "re_url": require_link,"eval_config_id": test_task.config_id,
                           "re_cmd": cmd, "re_error_id": err_id, "re_interval_time": re_interval_time,
                           "is_websearch": int(is_websearch)}
            # 记录每条数据的执行结果
            et_log.info(f"_________________item_result:{item_result}")
            item_result_id = TestItemResultService.insert_one(item_result)
            return item_result_id
        except Exception as e:
            et_log.error(f"run_agent_autotest_item item_id:{item.id} exception:{e},\n{traceback.print_exc()}")
            return None
