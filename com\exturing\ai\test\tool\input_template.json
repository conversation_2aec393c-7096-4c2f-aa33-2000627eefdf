{"version": "1.1", "streamType": "DUIRequire", "session": {"sessionId": "fc44abc6e849435192a4686db33c5182", "dialogHistory": [], "attributes": {"vadBeginPlayerState": "off", "dskdm": {"loadMscp": "false"}, "playerState": "off"}}, "request": {"recordId": "b35a685e8de447748c694adc7645edfc:891fbe2d499e492eb2eaf79583d788e6", "task": "音乐自由说", "typeExt": "continue", "llmRequestId": "29c4e9ec4f4f45dc8e4adf49e716bdf3", "type": "continue", "communicateType": "fullduplex", "nluDiscardThreshold": 0.5, "requestId": "b35a685e8de447748c694adc7645edfc:891fbe2d499e492eb2eaf79583d788e6", "slots": {}, "inputs": [{"skillId": "2024031500000086", "skill": "中枢大模型技能", "input": "画一个小狗的图片", "task": "壁纸生成", "skillVersion": "91", "timestamp": 1734675858}], "asr": {"languageClass": "putong<PERSON>", "nlpLanguageClass": "Mandarin"}, "requestBodyType": "nluResult"}, "context": {"languageClass": "putong<PERSON>", "product": {"aliasKey": "LLM_prod2.0_TEST", "options": {"enableNlgTranslation": true}, "oem": {"code": "gwm", "name": "长城"}, "dmSysSelectErrorDiscard": "on", "errorHandle": {"offlineWords": ["当前网络状态不佳，我先退下啦"], "asrEmptyWords": ["你好像没有说话哦，有什么可以帮你？", "可以再给点提示吗", "这个我该怎么回答呢", "这个我不知道什么意思啊"], "visible": true, "exitWords": ["再见"], "retryTimes": 1, "nluEmptyWords": ["我听不懂你说什么，可以换个说法吗？", "你说的这个我真的不太懂", "这个问题好难哦，我还不会", "我还没学会呢"]}, "productId": "279623629", "profileId": ["duicomm"], "yieldSwitch": "on", "productVersion": "519"}, "user": {"userId": "6b14bcbc9632f7f59335caba7c0c583f9c26db53_279623629"}, "skill": {"skillId": "2024031500000086", "skillVersion": "91", "settings": {"isInCruise": "false", "currentMedia": {"music": {"title": "单车", "album": "Shall We Dance? Shall We Talk!", "artist": "陈奕迅"}}, "destLocation": "", "filterSwitch": "on", "llmSwitch": "on", "followUpAllowedJumpToBaSwitch": "off", "gwmModel": "200V35", "customScreenInfo": {"width": 1920, "height": 984}, "status": {"backrunning": "", "platform": "screenx", "active": "null", "commonsite4what": "null"}, "isInNavi": "true", "fullduplexSessionTimeout": "30000"}}, "followUpAllowedJumpToBaSwitch": "off", "device": {"deviceName": "6b14bcbc9632f7f59335caba7c0c583f9c26db53", "location": {"time": "2024-12-20T14:24:14+0800", "city": "北京市", "useIP": 0, "longitude": 116.397482, "area": "东城区", "latitude": 39.908286}}, "customScreenInfo": {"width": 1920, "height": 984}, "attributes": {"agent_protocal_version": "1.1", "enablePredict": false, "display_debug_info": true, "llm": {"repetition_penalty": 1.2, "hide_action_tips": true, "send_waiting_tips": true, "output_doc_and_chat_both": true, "dfmContext": {"requestPlatform": "dds"}, "synonymRepId": ["S0002"], "withoutDocOpenChat": true, "control": {"showCornerMark": false, "openCornerMark": true}, "docInfo": {"docIds": [], "userSettingPrompt": "若干篇背景知识无法回答当前问题，请回复:'抱歉，未在车辆说明书中找到相关内容。'", "enableLtr": false, "withoutDocReply": "抱歉，未在车辆说明书中找到相关内容。", "maxChars": 3000, "qaTopN": 5, "topN": 5}, "chatPrecision": 0.5, "skillId": ["0234fcd8cb3142b2ae2d71ef8e8d9010"], "useSidFilter": true, "presence_penalty": 0, "output_doc_and_chat_both_tips": "但据我所知,", "type": "chat", "nlg_tips_typing_done": "以下回答由AI⼤模型⽣成", "plugins": "true", "nlg_tips_typing": "AI⼤模型⽣成中...", "temperature": 0.2, "moduleName": "dfm2.0-s4-l-v3.5", "frequency_penalty": 0.3, "plugin_list": ["search", "news"], "appId": "", "rewriteEngine": "yitu", "useCache": false}}, "isInCruise": "false", "skillId": "2024031500000086", "currentMedia": {"music": {"title": "单车", "album": "Shall We Dance? Shall We Talk!", "artist": "陈奕迅"}}, "destLocation": "", "status": {"backrunning": "", "platform": "screenx", "active": "null", "commonsite4what": "null"}, "llmSwitch": "on", "filterSwitch": "on", "gwmModel": "200V35", "isInNavi": "true", "docInfo": {"trigger": false}, "system": {"settings": {"isInCruise": "false", "currentMedia": {"music": {"title": "单车", "album": "Shall We Dance? Shall We Talk!", "artist": "陈奕 迅"}}, "destLocation": "", "filterSwitch": "on", "llmSwitch": "on", "followUpAllowedJumpToBaSwitch": "off", "gwmModel": "200V35", "customScreenInfo": {"width": 1920, "height": 984}, "status": {"backrunning": "", "platform": "screenx", "active": "null", "commonsite4what": "null"}, "isInNavi": "true", "fullduplexSessionTimeout": "30000"}}, "location": {"time": "2024-12-20T14:24:14+0800", "city": "北京市", "useIP": 0, "longitude": 116.397482, "area": "东城区", "latitude": 39.908286}, "fullduplexSessionTimeout": "30000"}}