from com.exturing.ai.test.volcengine_public.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>
from com.exturing.ai.test.volcengine_public.volcengine_client import VolcengineClient

api_path = "/dpfm/v1/chat/full"


def chat_full_api():
    """
    一步/聚合接口测试
    """
    # 测试数据
    test_data = {
        "query": "周杰伦有哪些歌？",
    }

    with VolcengineClient() as client:
        response = client.stream_post(
            path=api_path,
            data=test_data,
        )

        answer = CurriedJoin()

        for chunk in response:
            if chunk:
                print("Chunk:", chunk)
                if chunk.get("content"):
                    answer(chunk["content"])

        print("Response:", answer.result())


if __name__ == "__main__":
    chat_full_api()

# python -m com.exturing.ai.test.volcengine_public.api_test.chat_full
