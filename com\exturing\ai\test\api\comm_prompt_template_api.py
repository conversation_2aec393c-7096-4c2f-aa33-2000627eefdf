import traceback

from flask import Blueprint, request
from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.service.comm_prompt_template_service import CommPromptTemplateService

comm_prompt = Blueprint("comm_prompt", __name__)


# 新增模板记录
@comm_prompt.route(f"/{URL_PREFIX}/comm-prompt/create", methods=["POST"])
def comm_prompt_create():
    et_log.info("############comm_prompt_create################")
    try:
        req_data = request.get_json()
        prompt_template_instance = CommPromptTemplateModel(**req_data)
        if not prompt_template_instance.tmp_name or len(prompt_template_instance.tmp_name) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_name is empty", "").to_json()
        if not prompt_template_instance.tmp_code or len(prompt_template_instance.tmp_code) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_code is empty", "").to_json()
        if not prompt_template_instance.tmp_content or len(prompt_template_instance.tmp_content) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_content is empty", "").to_json()

        prompt_template_id = CommPromptTemplateService.create(prompt_template_instance)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(prompt_template_id)).to_json()
    except Exception as e:
        et_log.error(f"comm_prompt_create exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "comm_prompt_create exception").to_json()

# 模板信息分页查询
@comm_prompt.route(f"/{URL_PREFIX}/comm-prompt/page", methods=["POST"])
def comm_prompt_page():
    et_log.info("############comm_prompt_page################")
    try:
        data = request.get_json()
        query = CommPromptTemplateModel(**data)

        page_num = data.get("page_num") or 1
        page_size = data.get("page_size") or 10

        page = CommPromptTemplateService.query_page(page_num, page_size, query)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()
    except Exception as e:
        et_log.error(f"comm_prompt_page exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "comm_prompt_page exception").to_json()


# 修改模板信息
@comm_prompt.route(f"/{URL_PREFIX}/comm-prompt/update", methods=["POST"])
def comm_prompt_update():
    et_log.info("############comm_prompt_update################")
    try:
        req_data = request.get_json()
        tmp_id = req_data.get("tmp_id", "")

        if tmp_id is None or len(tmp_id) < 1:
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "tmp_id is empty", "").to_json()

        prompt_template_instance = CommPromptTemplateModel(**req_data)

        if not prompt_template_instance.tmp_name or len(prompt_template_instance.tmp_name) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_name is empty", "").to_json()
        if not prompt_template_instance.tmp_code or len(prompt_template_instance.tmp_code) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_code is empty", "").to_json()
        if not prompt_template_instance.tmp_content or len(prompt_template_instance.tmp_content) == 0:
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "tmp_content is empty", "").to_json()

        update_result = CommPromptTemplateService.update(tmp_id, prompt_template_instance)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()
    except Exception as e:
        et_log.error(f"comm_prompt_update exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "comm_prompt_update exception").to_json()


# 删除模板信息
@comm_prompt.route(f"/{URL_PREFIX}/comm-prompt/del", methods=["POST"])
def comm_prompt_delete():
    et_log.info("############comm_prompt_delete################")
    try:
        req_data = request.get_json()
        tmp_id = req_data.get("tmp_id")

        if tmp_id is None or len(tmp_id) < 1:
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "tmp_id is empty", "").to_json()

        delete_result = CommPromptTemplateService.delete(tmp_id)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(delete_result)).to_json()
    except Exception as e:
        et_log.error(f"comm_prompt_delete exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "comm_prompt_delete exception").to_json()
