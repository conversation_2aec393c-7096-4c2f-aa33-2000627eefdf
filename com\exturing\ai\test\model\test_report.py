from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional, Literal
from pydantic import BaseModel
from bson import ObjectId
from com.exturing.ai.test.comm.condition_util import model_dimension_condition

class AccuracyReportModel(BaseModel):
    task_id: Optional[str] = None # 测试任务id
    task_result_id: Optional[str] = None # 测试任务结果id
    report_time:Optional[str] = None # 报告时间
    group_id: Optional[str] = None # 分组id (维度id或数据标签id或all) all是汇总
    result_item_count: Optional[int] = 0 # 结果项数量
    pass_count: Optional[int] = 0 # 通过数量
    ad_pass_count: Optional[int] = 0 # 通过数量(标定)
    accuracy_rate: Optional[float] = None # 通过率
    ad_accuracy_rate: Optional[float] = None # 通过率(标定)
    category_pass_count: Optional[int] = 0 # 落域通过数量
    ad_category_pass_count: Optional[int] = 0 # 落域通过数量(标定)
    category_accuracy_rate: Optional[float] = None # 落域通过率
    ad_category_accuracy_rate: Optional[float] = None # 落域通过率(标定)
    answer_pass_count: Optional[int] = 0 # 答案通过数量
    ad_answer_pass_count: Optional[int] = 0 # 答案通过数量(标定)
    answer_accuracy_rate: Optional[float] = None # 答案通过率
    ad_answer_accuracy_rate: Optional[float] = None # 答案通过率(标定)
    task_pass_count: Optional[int] = 0 # 任务型通过数量
    ad_task_pass_count: Optional[int] = 0 # 任务型通过数量(标定)
    task_accuracy_rate: Optional[float] = None # 任务型通过率
    ad_task_accuracy_rate: Optional[float] = None # 任务型通过率(标定)
    result_item_list: Optional[list] = None # 结果项列表
    
    
class AccuracyReportQueryModel(BaseModel):
    task_id: Optional[str] = None # 测试任务id
    task_result_id: Optional[str] = None # 测试任务结果id
    model_dimension: Optional[dict] = None # 模型维度
    data_tags: Optional[list[str]] = None # 数据标签
    start_time: Optional[str] = None # 开始时间
    end_time: Optional[str] = None # 结束时间

    def get_result_condition(self):
        condition = {"is_del": 0}
        
        if self.task_id:
            condition["task_id"] = ObjectId(self.task_id)

        if self.task_result_id:
            condition["_id"] = ObjectId(self.task_result_id)
        
        if self.start_time and self.end_time:
            condition["create_time"] = {"$gte": self.start_time, "$lte": self.end_time}
       
        return condition

    def get_result_item_condition(self, result_list = []):
        condition = {"is_del": 0, "parent_id": ""}

        if len(result_list) > 0:
            task_result_ids = [result["_id"] for result in result_list]
            condition["task_result_id"] = {"$in": task_result_ids}

        if self.task_result_id:
            condition["task_result_id"] = ObjectId(self.task_result_id)

        if self.data_tags:
            regex_conditions = [{"data_tags": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in self.data_tags]
            condition["$and"] = regex_conditions

        model_dimension_condition(condition, self.model_dimension)

        return condition
    
class AccuracyReportGroupQueryModel(BaseModel):
    task_id: Optional[str] = None # 测试任务id
    task_result_id: Optional[str] = None # 测试任务结果id
    group_by: Optional[Literal['dimension_id', 'data_tag_id']] = 'dimension_id' # 分组类型
    dimension_ids: Optional[list[str]] = None # 维度id列表
    data_tags: Optional[list[str]] = None # 数据标签列表


    def get_result_item_condition(self):
        condition = {"is_del": 0, "parent_id": ""}

        if self.task_result_id:
            condition["task_result_id"] = ObjectId(self.task_result_id)

        if self.dimension_ids and len(self.dimension_ids) > 0:
            condition["dimension_id"] = {"$in": [ObjectId(dimension_id) for dimension_id in self.dimension_ids]}

        if self.data_tags:
            regex_conditions = [{"data_tags": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in self.data_tags]
            condition["$or"] = regex_conditions

        return condition