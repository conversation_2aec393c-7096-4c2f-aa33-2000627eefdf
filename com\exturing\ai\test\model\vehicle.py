from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class VehicleModel(BaseDataModel):
    vehicle_name: str
    pid: str
    oem_code: str
    desc: Optional[str] = None
    
class VehicleQueryModel(BaseModel):
    oem_code: Optional[str] = None
    vehicle_name: Optional[str] = None
    pid: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}
        
        if self.oem_code:
            condition["oem_code"] = self.oem_code
        if self.vehicle_name:
            condition["vehicle_name"] = {"$regex": str(self.vehicle_name).strip(), "$options": "i"}
        if self.pid:
            condition["pid"] = self.pid
       
        return condition

    