import json
import traceback
from typing import Dict
import websockets
import time
import asyncio
from com.exturing.ai.test.comm.log_tool import et_log


async def dsk_prompt(input_json: Dict, url: str,dimension_name:str) -> (str, str, float, str, int, str):
    try:
        cmd = ''
        ack_time = None
        solvein_time = None
        info_time = None
        end_time = None
        first_res = False
        flag = False
        res_time = 0  # 初始化 res_time，确保在任何情况下都有默认值
        err_id = ""
        elapsed_time = 0
        asrend_time = 0
        run = 0
        require_link=""
        image_url = "N/A"
        async with websockets.connect(url, open_timeout=180, close_timeout=180) as websocket:
            start_time = time.time()
            et_log.info(f"dsk_prompt send data:{input_json}")
            await asyncio.wait_for(websocket.send(json.dumps(input_json)), 180)
            # await websocket.send(json.dumps({'_id': {'$oid': '678b652a89eb28b6f6db3297'}, 'create_time': '', 'create_by': 1, 'update_time': '', 'update_by': 1, 'is_del': 0, 'parent_id': '', 'task_id': {'$oid': '678b3f70a3bd2780819d0c3e'}, 'data_set_item_id': {'$oid': '678b3f71a3bd2780819d0c40'}, 'question': '我要听前卫音乐，有独特创新、让人意想不到的。', 'actual_task': '', 'actual_category': '', 'result_category': '', 'actual_answer': '', 'result_answer': 0, 'answer_score': 0, 'qa_recall': 0, 'qa_keywords': '', 'qa_use_time': '1.61', 'result_final': 0, 'ad_result_category': '', 'ad_result_answer': '', 'ad_result_final': '', 'is_last': 1, 'expected_category': '', 'expected_task': '', 'expected_answer': "\n在这个时代的音乐长河里，有这样一些作品，它们如同逆流而上的鲑鱼，充满了创意与不羁。我为你推荐《The Less I Know The Better》。这首歌巧妙地融合了合成器和人声，呈现出了既复古又前卫的听觉感受。 🌊\n\n \ncmd= {'url': 'nativecmd://DUI.Music.UseSemanticSlots', 'args': {'singer': 'Tame Impala', 'song': 'The Less I Know The Better', 'album': 'Currents', 'operate': '播放', 'useLocalNlg': False, 'useCloudNlg': True, 'customInnerFrom': 'llm', 'customInnerType': 'nativeCommand'}, 'runAfterSpeaking': False}", 'recall_id': '776baeb2-21f4-4ccf-af8e-dbf4ad471c42', 'task_result_id': {'$oid': '678b651c89eb28b6f6db3296'}, 'model_id': {'$oid': '678b3f25a3bd2780819d0bb0'}, 'dimension_id': {'$oid': '678b3f26a3bd2780819d0bb4'}, 'remark': '71ff2ba635fd46cb83b4724002984fbb|9b3925182c4f488d88639991f334ea2a', 're_url': ' ，呈现出了既复古又前卫的听觉感受。 🌊', 're_cmd': "{'url': 'nativecmd://DUI.Music.UseSemanticSlots', 'args': {'singer': 'Tame Impala', 'song': 'The Less I Know The Better', 'album': 'Currents', 'operate': '播放', 'useLocalNlg': False, 'useCloudNlg': True, 'customInnerFrom': 'llm', 'customInnerType': 'nativeCommand'}, 'runAfterSpeaking': False}", 're_error_id': 'N/A', 're_interval_time': 'N/A', 'is_websearch': 0}))
            while True:
                message = await asyncio.wait_for(websocket.recv(),180)
                # message = websocket.recv()
                res = json.loads(message)
                # print(f"res______:{res}")
                done = has_done(res)
                if 'ack' in str(res):
                    if ack_time is None:  # 只记录第一次出现 ack 的时间
                        ack_time = time.time()
                        flag = True
                    continue
                if '正在整理相关信息' in str(res):
                    if solvein_time is None:  # 只记录第一次出现“正在整理相关信息”的时间
                        solvein_time = time.time()
                        first_res = True
                        run = 1
                    continue
                if solvein_time is None and flag and 'streamType": "intermediate' in str(res):
                    end_time = time.time()
                    elapsed_time = (end_time - start_time) * 1.00
                    flag = False
                    continue
                if first_res:
                    if solvein_time is not None:
                        if solvein_time and solvein_time != info_time and '正在整理相关信息' not in str(res):
                            info_time = time.time()
                            first_res = False

                if 'response' in res and 'execute' in res['response']:
                    cmd += str(res['response']['execute'])

                if 'response' in res and 'error' in res['response'] and 'errId' in res['response']['error']:
                    err_id = res.get('response', {}).get('error', {}).get('errId', 'N/A')
                # record_id = get_record_id(res)
                if dimension_name == "百科出图":
                    if 'response' in res and 'widget' in res['response'] and 'llmOutputData' in res['response'][
                        'widget']:
                        llm_output_data = res['response']['widget']['llmOutputData']
                        # 查找 type 为 'image' 的项，并输出其对应的 text 值
                        for item in llm_output_data:
                            if item.get('type') == 'image':
                                image_url = item.get('text','N/A')
                                et_log.info(f"百科出图返回url:{url}")

                record_id = res['response']["recordId"]
                if done:
                    display = get_display_text(res)
                    et_log.info(f"dsk_prompt response res:{res}")
                    require_link = get_url_text(res)
                    et_log.info(f"dsk_prompt response res_________:{require_link}")
                    done_time = time.time()
                    asrend_time = (done_time - start_time) * 1.00
                    # 确保 res_time 在每次循环中都有值
                    if info_time is not None:
                        res_time = (info_time - start_time) * 1.00
                    else:
                        res_time = elapsed_time
                    if dimension_name == "百科出图":
                        require_link = image_url
                    break


        et_log.info(f"dsk_prompt return display:{display} cmd:{cmd} res_time:{res_time} err_id:{err_id} run:{run}")
        return display, cmd, res_time, err_id, run, require_link,asrend_time
    except websockets.exceptions.ConnectionClosedOK:
        et_log.error(f"dsk_prompt ConnectionClosedOK exception,\n{traceback.print_exc()}")
        return "", "", 0.00, "", 0, "", 0.00
    except Exception as e:
        et_log.error(f"dsk_prompt exception:{e},\n{traceback.print_exc()}")
        return "", "", 0.00, "", 0, "", 0.00


# 判断响应是否结束
def has_done(payload: Dict) -> bool:
    return '\'done\'' in str(payload)

# 获取多模态返回的url,多个之间以换行拼接
def get_url_text(res):
    """
    从 JSON 数据中提取 'llmOutputData' 中的 'text' 字段内容。
    要求：只有当 'type' 为 "image" 时才提取 'text'，否则返回 "N/A"。
    如果存在多个符合条件的 'text' 字段，将以换行拼接返回。
    """
    response = res.get('response', {})
    widget = response.get('widget', {})
    llm_output_data = widget.get('llmOutputData', [])
    text_values = []

    if llm_output_data:
        for item in llm_output_data:
            # 检查类型是否为 "image"
            if item.get('type') == 'image':
                text = item.get('text')
                if text:
                    text_values.append(text)
            else:
                # 如果类型不是 "image"，跳过或标记为 "N/A"
                continue

    # 如果没有符合条件的 "image" 类型，返回 "N/A"
    if not text_values:
        return "N/A"

    # 拼接多个符合条件的 'text' 字段内容
    url = '\n'.join(text_values)
    return url

# 获取返回中的实际回答
def get_display_text(res: Dict) -> str:
    response = res['response']
    caption = response.get('caption')
    widget = response.get('widget')
    display_text = ''
    if caption:
        display_text = caption.get('displayText')
    elif widget:
        display_text = widget.get('displayText')
    execute = response.get('execute')
    if execute:
        display_text += f'\ncmd= {execute}'
    if not display_text:
        # 拿speak
        display_text = response.get('speak',{}).get('text')
    return display_text

def convert_to_tree(data):
    # 创建一个字典，用于根据 _id 查找每个节点
    id_map = {item['_id']: item for item in data}
    
    # 创建一个空的列表，用于存储根节点（parent_id 为空的节点）
    root_nodes = []
    
    # 遍历所有数据，设置每个节点的子节点
    for item in data:
        # 如果 parent_id 为空，则为根节点，加入 root_nodes
        if not item['parent_id']:
            root_nodes.append(item)
        else:
            # 否则，把该节点添加到父节点的 children 列表中
            parent = id_map.get(item['parent_id'])
            if parent:
                # 确保父节点存在 children 列表
                if 'children' not in parent:
                    parent['children'] = []
                parent['children'].append(item)
            else:
                et_log.error(f"Parent not found for item: {item['_id']} with parent_id: {item['parent_id']}")
    
    return root_nodes

def extract_questions(data, result=None):
    if result is None:
        result = []  # 初始化空列表

    # 如果当前数据包含"question"字段，将其值添加到结果列表中
    if "question" in data:
        result.append(data["question"])

    # 如果当前数据包含"children"字段，递归遍历每一个子项
    if "children" in data:
        for child in data["children"]:
            extract_questions(child, result)

    return result

def process_tree(node, level=1):
    result = {}  # 每个树形结构都会生成一个新的字典
    An_expected = {}  # 存储 expected_answer 的字典
    
    key_question = f'Q{level}'
    key_answer = f'A{level}'
    
    # 获取当前节点的 question 和 actual_answer
    question = node.get('question', '').strip()
    actual_answer = node.get('expected_answer', '').strip()
    
    # 如果 question 不为空，则添加到字典中
    if question:
        result[key_question] = question
    # 如果 actual_answer 不为空，则添加到字典中
    if actual_answer:
        result[key_answer] = actual_answer
    
    # 如果有子节点，递归处理子节点
    if 'children' in node and node['children']:
        for child in node['children']:
            child_result, child_An_expected = process_tree(child, level + 1)  # 获取子节点的处理结果
            result.update(child_result)  # 合并子节点的结果
            An_expected.update(child_An_expected)  # 合并子节点的 expected_answer
    
    # 如果没有子节点，处理最后一级的 expected_answer
    else:
        expected_answer = node.get('qa_keywords', '').strip()
        if expected_answer:
            An_expected[f'A{level}expected'] = expected_answer
    return result, An_expected

def is_valid_format1(result,An_expected):
    err= ''
    key_count = len(result.keys())
    if key_count <3 and key_count % 2 == 0:
        err = 'Q、AA少于3个或者Q&A数量为奇数'
        return False , err
    
    if result:
        last_key = list(result.keys())[-1]
        if not last_key.startswith('Q'):
            err = '最后一个key应该以Q开头'
            return False , err

    empty_expected = [key for key, value in An_expected.items() if not value]
    if empty_expected:
        err = "Refer_keyword中应该有值"
        return False, err
    return True,err

def get_blocks(res:Dict):
    response = res['response']
    widget = response['widget']
    if widget['llmOutputType'] == 'flow':
        return widget['llmOutputData']

def get_speak(res: Dict) -> str:
    response = res['response']
    speak = response.get('speak')
    if speak:
        speak_text = speak.get('text')
        return speak_text
    return ''

def get_display(res: Dict) -> str:
    response = res['response']
    caption = response.get('widget')
    display_text = ''
    if caption:
        display_text = caption.get('displayText')
    return display_text

def get_cmd(res: Dict) -> str:
    response = res['response']
    cmd = ''
    execute = response.get('execute')
    if execute:
        cmd += f'{execute}'
    return cmd

def get_ins_imageurl(res: Dict)-> str:
    response = res.get('response', {})
    execute = response.get('execute',{})
    args = execute.get('args',{})
    url = ''
    url += args.get('imageUrl','')
    if url:
        url =url
    else:
        url+= "N/A"
    return url



def extract_dataitem_info(res):
    llm_output_data = res['response']['widget']['llmOutputData']
    result_list = []
    for item in llm_output_data:
        if item.get('type') == 'dataitem':
            params = item.get('params',{})
            payload = params.get('payload',{})
            #创建一个字典，存储提取的字段
            info_dict = {
                "sub_type": params.get('sub_type'),
                "name": payload.get('name'),
                "rating": payload.get('rating'),
                "cost": payload.get('cost'),
                "business_area": payload.get('business_area'),
                "distance": payload.get('distance'),
                "address": payload.get('address'),
                "image": payload.get('image')
            }
            result_list.append(info_dict)
    return result_list

def get_speak(res: Dict) -> str:
    response = res['response']
    speak = response.get('speak')
    if speak:
        speak_text = speak.get('text')
        return speak_text
    return ''

#
# def get_record_id(res: Dict) -> str:
#     response = res['response']
#     recordID = response.get('recordId')
#     return recordID


