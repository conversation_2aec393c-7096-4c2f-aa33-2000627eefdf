import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.dto.params.auto_dataset_dto import AutoDatasetDto
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.et_data_set import EtDataSet
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.service.origin_data_item_service import find_pk, find_child
from com.exturing.ai.test.comm.comm_util import list_to_tree
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil


# 数据集Service
class DataSetService:

    # 新增数据集
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one data_set post_data:{post_data}")
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ctx_oem_codes = CTX_OEM_CODES.get()
            return EtDataSet("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                             ctx_oem_codes, post_data["set_name"], post_data["set_types"], post_data["objective"], post_data["version"],
                             post_data["vm_id"], post_data["ds_status"], 0, post_data["test_scope"]).insert_one()
        except Exception as e:
            et_log.error(f"insert_one data_set exception")
            traceback.print_exc()
            return None

    # 根据主键查询数据集对象
    @classmethod
    def find_pk_dataset(cls, _id):
        dset = EtDataSet.find_by_pk(_id)
        if dset is not None:
            return EtDataSet(**dset)
        return None

    # 根据名称搜索数据集(用于名称校验)
    @classmethod
    def find_name(cls, name, create_by):
        return EtDataSet.find_page_condition(name, create_by, None, None)

    # 数据集 查询
    @classmethod
    def query_page(cls, name, create_by, page_num: int, page_size: int):
        # 查询匹配的数量
        total = EtDataSet.find_count(name, create_by)
        if total < 1: # 未匹配到结果
            return None
        page = PageResult(page_num, page_size, total)
        set_list = EtDataSet.find_page_condition(name, create_by, page_size, page.skip)
        if set_list is None or len(set_list) < 1:# {ResultCode.SUCCESS.name}
            et_log.error(f"query not found data error")
            return None
        set_json_list = []
        for set_data in set_list:# 返回集合转json格式
            set_json_list.append(set_data.to_json_str())
        page.page_data = set_json_list
        return page

    # 删除数据集
    @classmethod
    def delete(cls, set_id, do_user):
        et_log.info(f"data_set delete by set_id:{set_id} do_user:{do_user}")
        try:
            # 删除id对应的数据集记录
            EtDataSet.delete_by_id(set_id, do_user)
            # 删除数据集下面的数据项
            EtDataSetItem.del_by_set_id(set_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"data_set delete by set_id exception")
            traceback.print_exc()
            return False

    # 更新数据集
    @classmethod
    def update(cls, data_set:EtDataSet):
        et_log.info(f"data_set update by data_set:{EtDataSet}")
        try:
            # 检查数据是否重复
            items = EtDataSet.find_page_condition(data_set.name, -1, 100, 0)
            if items and len(items) > 0:
                for data_em in items:
                    if str(data_set.id) != str(data_em.id):# 检查表明有重复的问题
                        et_log.error(f"data_set update error, repeat name")
                        return False
            # 更新id对应的数据集记录
            update_num = EtDataSet.update_entity_json(data_set.to_json())
            if update_num.modified_count and update_num.modified_count > 0:
                return True
            else:
                return False
        except Exception as e:
            et_log.error(f"data_set update by data_set exception")
            traceback.print_exc()
            return False

    @classmethod
    def build_dataset_origin(cls, set_id, origins, do_user):
        et_log.info(f"build_dataset_origin set_id:{set_id} origins:{origins} do_user:{do_user}")
        try:
            if not origins or len(origins) == 0:
                et_log.error(f"build_dataset_origin origins is empty")
                return
            add_len = 0
            total_len = len(origins)
            for index, origin_id  in enumerate(origins):
                current = find_pk(origin_id)
                count = EtDataSetItem.find_comm_condition_count({"question": current.question, "data_set_id": ObjectId(set_id)})
                if count > 0:
                    et_log.error(f"build_dataset_origin question:{current.question} origin_id:{origin_id} is repeat")
                    continue
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cid = EtDataSetItem("", current_time, do_user, current_time, do_user, 0, CTX_OEM_CODES.get(),
                             "", ObjectId(set_id), "", current.model_id, current.dimension_id,
                                    current.item_type, "", current.question, current.expected_answer, current.expected_task,
                                    current.expected_category, current.data_tag_ids, current.is_last, current.real_time, current.qa_keywords,
                                    current.data_src).insert_one()
                if 0 == current.is_last:
                    cls.create_item_parent_origin(origin_id, set_id, cid, current_time, do_user)
                add_len += 1

            cls.update_dataset_count(set_id)
            return {"total": total_len, "success": add_len}
        except Exception as e:
            et_log.error(f"build_dataset_origin set_id:{set_id} origins:{origins} exception:{e}")
            traceback.print_exc()
            return {"total": len(origins), "success": 0, "error": e}

    @classmethod
    def create_item_parent_origin(cls, parent_oid, set_id, pid, current_time, do_user):
        child_item = find_child(parent_oid)
        if not child_item or len(child_item) == 0:
            et_log.error(f"create_item_parent_origin parent_oid:{parent_oid} not found child")
            return
        count = EtDataSetItem.find_comm_condition_count({"question": child_item.question, "data_set_id": ObjectId(set_id)})
        if count > 0:
            et_log.error(f"create_item_parent_origin question:{child_item.question} parent_oid:{parent_oid} is repeat")
            return
        # 插入原始语料的当前节点数据集数据项
        cid = EtDataSetItem("", current_time, do_user, current_time, do_user, 0, CTX_OEM_CODES.get(),
                            ObjectId(str(pid)), ObjectId(set_id), "", child_item.model_id, child_item.dimension_id,
                            child_item.item_type, "", child_item.question, child_item.expected_answer, child_item.expected_task,
                            child_item.expected_category, child_item.data_tag_ids, 1, child_item.real_time, child_item.qa_keywords,
                            child_item.data_src).insert_one()

        # 如果当前节点还有儿子节点，则继续
        if 0 == child_item.is_last:
            cls.create_item_parent_origin(child_item.id, set_id, cid, current_time, do_user)

    @classmethod
    def build_dataset_result(cls, set_id, task_result_id, result_item_ids):
        et_log.info(f"build_dataset_result set_id:{set_id} result_item_ids:{result_item_ids} ")
        try:
            set_items = MongoDBUtil.find_condition("et_data_set_item", {"data_set_id": ObjectId(set_id)})
            set_item_list = list(set_items or [])
            set_item_tree = list_to_tree(set_item_list)

            res = MongoDBUtil.find_condition("test_result", {"task_result_id": ObjectId(task_result_id)})
            result_list = list(res or [])
            result_tree = list_to_tree(result_list)

            filter_tree = cls.filter_list_by_question(result_tree, set_item_tree)

            total_len = len(result_item_ids)
            add_len = 0

            def add_item(current, parent_id = ""):
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cid = EtDataSetItem("", current_time, 0, current_time, 0, 0, CTX_OEM_CODES.get(),
                          parent_id, ObjectId(set_id), "", current.get("model_id", ""), current.get("dimension_id", ""),
                                0, "", current.get("question",""), current.get("expected_answer",""), current.get("expected_task", ""),
                                current.get("expected_category", ""), current.get("data_tags", ""), current.get("is_last", 1), current.get("real_time", ""), current.get("qa_keywords", ""),
                                current.get("data_src", "")).insert_one()
                if 0 == current["is_last"]:
                    for child in current["children"]:
                        add_item(child, cid)

            for item in filter_tree:
                if str(item["_id"]) in result_item_ids:
                    add_item(item)
                    add_len += 1

            cls.update_dataset_count(set_id)
            return {"total": total_len, "success": add_len}
        except Exception as e:
            et_log.error(f"build_dataset_result set_id:{set_id} result_item_ids:{result_item_ids} exception:{e},\n{traceback.print_exc()}")
            return {"total": total_len, "success": 0, "error": e}

    @classmethod
    def build_dataset_by_dataset(cls, set_id, base_set_id, data_set_item_ids):
        et_log.info(f"build_dataset_result set_id:{set_id} data_set_item_ids:{data_set_item_ids} ")
        try:
            set_items = MongoDBUtil.find_condition("et_data_set_item", {"data_set_id": ObjectId(set_id)})
            set_item_list = list(set_items or [])
            set_item_tree = list_to_tree(set_item_list)

            res = MongoDBUtil.find_condition("et_data_set_item", {"data_set_id": ObjectId(base_set_id)})
            result_list = list(res or [])
            result_tree = list_to_tree(result_list)

            filter_tree = cls.filter_list_by_question(result_tree, set_item_tree)

            total_len = len(data_set_item_ids)
            add_len = 0

            def add_item(current, parent_id = ""):
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cid = EtDataSetItem(
                    "",
                    current_time,
                    0,
                    current_time,
                    0,
                    0,
                    CTX_OEM_CODES.get(),
                    parent_id,
                    ObjectId(set_id),
                    current.get("metric_id", ""),
                    current.get("model_id", ""),
                    current.get("dimension_id", ""),
                    current.get("item_type", 0),
                    current.get("scene_id", ""),
                    current.get("question", ""),
                    current.get("expected_answer", ""),
                    current.get("expected_task", ""),
                    current.get("expected_category", ""),
                    current.get("item_tags", ""),
                    current.get("is_last", 1),
                    current.get("real_time", ""),
                    current.get("qa_keywords", ""),
                    current.get("data_src", ""),
                ).insert_one()
                if 0 == current["is_last"]:
                    for child in current["children"]:
                        add_item(child, cid)

            for item in filter_tree:
                if str(item["_id"]) in data_set_item_ids:
                    add_item(item)
                    add_len += 1

            cls.update_dataset_count(set_id)
            return {"total": total_len, "success": add_len}
        except Exception as e:
            et_log.error(f"build_dataset_result set_id:{set_id} data_set_item_ids:{data_set_item_ids} exception:{e},\n{traceback.print_exc()}")
            return {"total": total_len, "success": 0, "error": e}

    @classmethod
    def build_dataset_by_datalist(cls, set_id, data_list):
        et_log.info(f"build_dataset_result set_id:{set_id}")
        try:
            set_items = MongoDBUtil.find_condition(
                "et_data_set_item", {"data_set_id": ObjectId(set_id)}
            )
            set_item_list = list(set_items or [])
            set_item_tree = list_to_tree(set_item_list)

            filter_tree = cls.filter_list_by_question(data_list, set_item_tree)

            total_len = len(data_list)
            add_len = 0

            def add_item(current, parent_id=""):
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                EtDataSetItem(
                    "",
                    current_time,
                    0,
                    current_time,
                    0,
                    0,
                    CTX_OEM_CODES.get(),
                    parent_id,
                    ObjectId(set_id),
                    current.get("metric_id", ""),
                    current.get("model_id", ""),
                    current.get("dimension_id", ""),
                    current.get("item_type", 0),
                    current.get("scene_id", ""),
                    current.get("question", ""),
                    current.get("expected_answer", ""),
                    current.get("expected_task", ""),
                    current.get("expected_category", ""),
                    current.get("item_tags", ""),
                    current.get("is_last", 1),
                    current.get("real_time", ""),
                    current.get("qa_keywords", ""),
                    current.get("data_src", "自动生成"),
                ).insert_one()

            for item in filter_tree:
                add_item(item)
                add_len += 1

            cls.update_dataset_count(set_id)
            return {"total": total_len, "success": add_len}
        except Exception as e:
            et_log.error(
                f"build_dataset_result set_id:{set_id} exception:{e},\n{traceback.print_exc()}"
            )
            return {"total": total_len, "success": 0, "error": e}

    @classmethod
    def update_dataset_count(csl, set_id):
        if set_id is None:
            return 0
        count = EtDataSetItem.find_count_set_id(set_id)
        EtDataSet.update_set_total(set_id, count)
        return count  

    @classmethod
    def filter_list_by_question(cls, data_list: list = [], base_list: list = []):
        data_list = cls.get_list_unique_key(data_list)
        base_list = cls.get_list_unique_key(base_list)
        base_list_keys = [item.get("unique_key","") for item in base_list]

        filter_list = []
        for item in data_list:
            if item.get("unique_key") in base_list_keys:
                continue
            filter_list.append(item)
            base_list_keys.append(item.get("unique_key"))

        return filter_list

    @classmethod
    def get_list_unique_key(cls, data_list: list = []):
        def get_unique_key(item: dict = {}):
            unique_key = item.get("question", "")
            children = item.get("children", [])
            for child in children:
                unique_key += ("__" + get_unique_key(child))

            return unique_key

        for item in data_list:
            item['unique_key'] = get_unique_key(item)

        return data_list

    @classmethod
    def auto_build_dataset(cls, auto_params, task_name=""):
        """
        根据叁数auto_params数组，自动随机根据auto_params中的参数，抽取数据，组成一个数据集
        :param auto_params:自动随机构建参数列表
        :param task_name:任务名称
        :return:构建的数据集id
        """
        et_log.info(f"auto_build_dataset auto_params:{auto_params} task_name:{task_name}")
        try:
            time_tag = datetime.now().strftime('%Y%m%d%H%M%S')
            total_num = 0
            set_name = (task_name + "_auto_" + time_tag) if task_name and len(task_name) > 0 else "random_dataset_" + time_tag
            set_id = EtDataSet("", "", 0, "", 0, 0, "",
                      set_name, "", "", "", "", 1, total_num, "").insert_one()
            set_id = str(set_id)
            # 根据参数获取随机的原始语料List
            origin_ids = []
            for param in auto_params:
                auto_dataset_dto = AutoDatasetDto(**param)
                origin_ids = origin_ids + [item._id for item in OriginDataItemModel.random_origin_list(auto_dataset_dto)]

            # 根据原始语料ids构建数据集明细项
            result = cls.build_dataset_origin(set_id, origin_ids, 0)
            if not result:
                et_log.error(f"auto_build_dataset auto_params:{auto_params} fail")
                return ""

            # 根据明细项结果，更新数据集数据项总数
            EtDataSet.update_set_total(set_id, result.get("success", 0))

            return str(set_id)
        except Exception as e:
            et_log.error(f"auto_build_dataset auto_params:{auto_params} exception:{e}")
            traceback.print_exc()
            return ""


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))
