# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.workforce_climb import WorkforceClimbModel, WorkforceClimbQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.employee_service import query_list
from com.exturing.ai.test.service.workforce_climb_service import (
    create,
    query_page,
    query_list,
    update,
    delete,
    import_data,
)

workforce_climb = Blueprint("workforce_climb", __name__)


# 新增人力爬坡
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/create", methods=["POST"])
def workforce_climb_create():
    et_log.info("############workforce_climb_create################")
    req_data = request.get_json()
    workforce_climb_instance = WorkforceClimbModel(**req_data)

    try:
        workforce_climb_id = create(workforce_climb_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(workforce_climb_id)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 人力爬坡分页查询
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/page", methods=["POST"])
def workforce_climb_page():
    et_log.info("############workforce_climb_page################")
    data = request.get_json()
    query = WorkforceClimbQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 人力爬坡列表查询
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/list", methods=["POST"])
def workforce_climb_list():
    et_log.info("############workforce_climb_list################")
    data = request.get_json()
    query = WorkforceClimbQueryModel(**data)

    data_list = query_list(query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, data_list
    ).to_json()


# 修改人力爬坡
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/update", methods=["POST"])
def workforce_climb_update():
    et_log.info("############workforce_climb_update################")
    req_data = request.get_json()
    id = req_data.get("workforce_climb_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "workforce_climb_id is null", ""
        ).to_json()

    workforce_climb_instance = WorkforceClimbModel(**req_data)

    try:
        update_result = update(id, workforce_climb_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 删除人力爬坡
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/del", methods=["POST"])
def workforce_climb_delete():
    et_log.info("############workforce_climb_delete################")
    req_data = request.get_json()
    id = req_data.get("workforce_climb_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "workforce_climb_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()

# 导入人力爬坡数据
@workforce_climb.route(f"/{URL_PREFIX}/staff/workforce-climb/import", methods=["POST"])
def workforce_climb_import():
    et_log.info("############workforce_climb_import################")
    if "excel_file" in request.files:
        uploaded_file = request.files["excel_file"]
        project_id = request.form.get("project_id")

        if not project_id:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
            ).to_json()

        if uploaded_file.filename.endswith(".xlsx") or uploaded_file.filename.endswith(
            ".xls"
        ):
            result = import_data(project_id, uploaded_file)

            if result:
                return ApiResult(
                    ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result
                ).to_json()
            else:
                return ApiResult(
                    ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "导入失败"
                ).to_json()
        else:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "excel_file is not xlsx or xls", ""
            ).to_json()
    else:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "excel_file is null", ""
        ).to_json()
