import json
import requests


output_model = """{
    "data": {
        "model": "闲聊及百科大问答",
        "corpus": [
            {"question": "第三产业指的是哪些", "answer": "第三产业，也被称为服务业"},
            {"question": "浙K是哪个城市的车", "answer": "浙K是浙江省丽水市的车牌代码"},
            {"question": "历史上有甄嬛这个人吗", "answer": "历史上，甄嬛的原型是清朝的孝圣宪皇后钮祜禄氏。在文艺作品中，甄嬛是小说《后宫·甄嬛传》及其衍生作品中的女主角。"}
        ],
        "num": 3
    }
}"""

class CorpusGenerator:
    def __init__(self):
        pass

    def get_access_token(self):
        """
        获取 access_token
        """
        API_KEY = "LjdjpvXXrwsWmdOSPj1mxbeX"
        SECRET_KEY = "BgUbIQ47iUPmveroNxhTKsuydYbUcIFj"
        url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={API_KEY}&client_secret={SECRET_KEY}"
        response = requests.post(url)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            raise Exception(f"获取 access_token 失败：{response.text}")

    def generate_corpus(self, prompt):
        """
        根据 prompt 生成语料并返回 JSON 结果
        """
        access_token = self.get_access_token()
        url = f"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-128k?access_token={access_token}"

        # 解析 prompt 中的模块和数量
        # if "闲聊" in prompt and "百科大问答" in prompt:
        #     model = "闲聊及百科大问答"
        # elif "音乐自由说" in prompt:
        #     model = "音乐自由说"
        # else:
        #     model = "未知模块"

        # num = int(''.join(filter(str.isdigit, prompt)))  # 提取 prompt 中的数字

        # 构造生成语料的提示词

        payload = json.dumps({
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "stream": True
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.post(url, headers=headers, data=payload, stream=True)
        if response.status_code == 200:
            # 收集流式响应结果
            full_text = ""
            for line in response.iter_lines():
                if line:
                    try:
                        decoded_line = line.decode("UTF-8")
                        json_data = json.loads(decoded_line.replace("data: ", ""))
                        if "result" in json_data:
                            full_text += json_data["result"]
                    except json.JSONDecodeError:
                        continue
            return full_text
        else:
            raise Exception(f"API调用失败，HTTP状态码: {response.status_code}, 错误信息: {response.text}")

            # # 解析生成的语料
            # corpus = [line.strip() for line in full_text.split("\n") if line.strip()]
            # if len(corpus) > num:
            #     corpus = corpus[:num]  # 确保语料数量与要求一致
            #
            # # 构造返回的 JSON 体
            # result = {
            #     "data": {
            #         "model": model,
            #         "corpus": corpus,
            #         "num": len(corpus)
            #     }
            # }
    def analyze_corpus(self, prompt):
        """
        分析语料并返回JSON结果
        """
        prompt_data = (
            f"我有条语料：“帮我自动构建3条闲聊及百科大问答模块的语料” A数据是该预料生成json形式的模板,\n"
            f"A:{output_model}\n"
            f"现在我有一条新的语料数据，{prompt},请依据我上述模板，生成该预料的解析数据的json数据，模板中的key一一对应。corpus对应类型为dict的value中，每个对象的key为你生成的语料，value为该语料对应的期望回答\n"
            f"1. 输出必须是一个严格的 JSON 对象，不要包含任何额外的标记或解释。\n"
            f"2. JSON 的结构必须与 A 数据模板完全一致。\n"
            f"3. 只输出 JSON 数据，不要添加任何额外的文本。\n"
            f"4. num对应value与我语料要求的数量一致。\n"
            f"5. model对应value与我输入语料类型相关，eg'帮我自动构建3条闲聊及百科大问答模块的语料'对应的model为'闲聊及百科大问答','大家庭聚餐吃火锅，要能提供特色菌汤锅底和多种酱料的'对应的model为'美食助手','播放非洲的音乐，感受异域风情'对应的model为'音乐自由说'，'来首周杰伦的青花瓷'对应的model为'音乐自由说'，'播放凤凰传奇的歌'对应的model为'音乐自由说'\n"
            f"6. 输入prompt对应的model值根据上述我提供的样例，如果是其他场景，请您分析一下，给出合适的model值。\n"
            f"6. 最终需要在‘corpus’对应value中生成与之相关的语料以及该语料的期望回答，同时期望回答尽量简略且最好在30字之类,例如”我想听周杰伦的歌“对应的期望回答为”为你播放周杰伦的青花瓷“\n"
        )

        try:
            result = self.generate_corpus(prompt_data)  # 计算相似度
            if result.startswith('```json'):
                result = result[len('```json'):].strip()  # 去掉开头的 ```json
            if result.endswith('```'):
                result = result[:-3].strip()  # 去掉结尾的 ```
            result = result.replace('\n', '').replace(' ', '')  # 去掉换行符和空格
            result_json = json.loads(result)  # 转换为 Python 字典
            return result_json
        except Exception as e:
            return {"data": {"model": "N/A","corpus": {"N/A1":"N/A"},"num": "N/A"}}



# # 主函数调用示例
if __name__ == "__main__":
    generator = CorpusGenerator()
    prompt = "帮我生成10条音乐的语料"
    result = generator.analyze_corpus(prompt)
    print(json.dumps(result, indent=4, ensure_ascii=False))