from datetime import datetime
from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class ProjectModel(BaseDataModel):
    name: Optional[str] = None  # 项目名称
    code: Optional[str] = None  # 项目编码
    type: Optional[int] = None  # 项目类型 1 内部项目 2 外部项目 3 其他
    start_time: Optional[str] = None  # 项目开始时间
    end_time: Optional[str] = None  # 项目结束时间
    desc: Optional[str] = None  # 项目描述


class ProjectQueryModel(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None  # 项目编码
    type: Optional[int] = None
    is_valid: Optional[bool] = None  # 是否有效

    def get_query_condition(self):
        condition = {}
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}
        if self.code:
            condition["code"] = {"$regex": str(self.code).strip(), "$options": "i"}
        if self.type:
            condition["type"] = self.type

        if self.is_valid is not None:
            if self.is_valid:
                condition["start_time"] = {"$lte": current_time}
                condition["end_time"] = {"$gte": current_time}
            else:
                condition["start_time"] = {"$gt": current_time}
                condition["end_time"] = {"$lt": current_time}

        return condition
