import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 测试环境
class TestEnv(BaseModel):

    _doc = "test_env"
    env_name: str  # 环境名称
    env_code: str  # 环境代码
    env_type: int # 环境类型 1：模型，2：应用，3：云平台，4：端侧
    env_script: str  # 环境脚本(预留字段)
    env_uri: str  # 环境URI

    branch_name: str  # 分支名称
    env_desc: str  # 分支描述
    vm_id: str # 车型表id
    pid_val: str  # 车型产品id=product_id参数
    device_id: str  # deviceId参数
    version: str  # ..参数
    user_id: str  # ..userId参数

    adapter_code: str # 适配标识
    oem_code: str # 参数用

    tmp_id: str # 提示词模板id
    tmp_code: str # 提示词模板代码

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", env_name="", env_code="", env_type=None,
                 env_script="", env_uri="", branch_name="", env_desc="", vm_id="", pid_val="", device_id="", version="",
                 user_id="", adapter_code="", oem_code="", tmp_id="", tmp_code=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.env_name = env_name
        self.env_code = env_code
        self.env_type = env_type
        self.env_script = env_script
        self.env_uri = env_uri

        self.branch_name = branch_name
        self.env_desc = env_desc
        self.vm_id = vm_id
        self.pid_val = pid_val
        self.device_id = device_id
        self.version = version
        self.user_id = user_id

        self.adapter_code =adapter_code
        self.oem_code = oem_code
        self.oem_codes = oem_codes

        self.tmp_id = tmp_id
        self.tmp_code = tmp_code


    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = self._id
        base_json["env_name"] = self.env_name
        base_json["env_code"] = self.env_code
        base_json["env_type"] = self.env_type
        base_json["env_script"] = self.env_script
        base_json["env_uri"] = self.env_uri

        base_json["branch_name"] = self.branch_name
        base_json["env_desc"] = self.env_desc
        base_json["vm_id"] = self.vm_id
        base_json["pid_val"] = self.pid_val
        base_json["device_id"] = self.device_id
        base_json["version"] = self.version
        base_json["user_id"] = self.user_id

        base_json["adapter_code"] = self.adapter_code
        base_json["oem_code"] = self.oem_code

        base_json["tmp_id"] = self.tmp_id
        base_json["tmp_code"] = self.tmp_code
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["vm_id"] = str(self.vm_id) if self.vm_id and len(str(self.vm_id)) > 0 else ""
        return data_json

    @classmethod
    def query_by_env_code(cls, env_code):
        """
        根据环境代码env_code（代adapter_code）查询对应的环境信息对象
        :param env_code: 环境代码
        :return: 环境对象
        """
        et_log.info(f"query_by_env_code env_code:{env_code}")
        try:
            condition = {"env_code": env_code}
            env_list = cls.find_condition(condition, None, -1, -1)
            if env_list and len(env_list) > 0:
                return env_list[0]
            return None
        except Exception as e:
            et_log.error(f"query_by_env_code exception:{e}\n{traceback.print_stack()}")
            return None

# current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
# TestEnv("", current_time, 0, current_time, 0, 0, "五菱 中枢+Agent",
#         "wl_hub_agent", 2, "", "").insert_one()

# TestEnv("", current_time, 0, current_time, 0, 0, "长城 中枢+Agent",
#         "cc_hub_agent", 2, "", "").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "local agent llm",
#         "local_llm_agent", 2, "", "ws://127.0.0.1:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "dev agent llm",
#         "dev_llm_agent", 2, "", "ws://54.222.189.141:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "qa agent llm",
#         "qa_llm_agent", 2, "", "ws://71.132.22.84:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "uat agent llm",
#         "uat_llm_agent", 2, "", "ws://52.81.147.243:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "prd agent llm",
#         "prd_llm_agent", 2, "", "ws://52.80.91.189:8765/llm").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "ali dev agent llm",
#         "ali_dev_llm_agent", 2, "", "ws://112.124.1.251:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali qa agent llm",
#         "ali_qa_llm_agent", 2, "", "ws://112.124.1.251:8766/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali uat agent llm",
#         "ali_uat_llm_agent", 2, "", "ws://112.124.1.251:8767/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali prd agent llm",
#         "ali_prd_llm_agent", 2, "", "ws://121.43.174.222:8765/llm").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "local agent autotest",
#         "local_autotest_agent", 2, "", "ws://127.0.0.1:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "dev agent autotest",
#         "dev_autotest_agent", 2, "", "ws://54.222.189.141:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "qa agent autotest",
#         "qa_autotest_agent", 2, "", "ws://71.132.22.84:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "uat agent autotest",
#         "uat_autotest_agent", 2, "", "ws://52.81.147.243:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "prd agent autotest",
#         "prd_autotest_agent", 2, "", "ws://52.80.91.189:8765/llm").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "ali dev agent autotest",
#         "ali_dev_autotest_agent", 2, "", "ws://112.124.1.251:8765/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali qa agent autotest",
#         "ali_qa_autotest_agent", 2, "", "ws://112.124.1.251:8766/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali uat agent autotest",
#         "ali_uat_autotest_agent", 2, "", "ws://112.124.1.251:8767/llm").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "ali prd agent autotest",
#         "ali_prd_autotest_agent", 2, "", "ws://121.43.174.222:8765/llm").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "中枢分类-开发","hub-dev",
#         2, "", "http://api.aicar.aispeech.com/dsk-llm/classification/v1").insert_one()
# TestEnv("", current_time, 0, current_time, 0, 0, "中枢分类-测试","hub-qa",
#         2, "", "http://api.aicar.aispeech.com/dsk-llm-test/classification/v1").insert_one()
#
# TestEnv("", current_time, 0, current_time, 0, 0, "语义上下文","semantic-context",
#         2, "", "http://api.aicar.aispeech.com/dsk-llm/server/v1").insert_one()