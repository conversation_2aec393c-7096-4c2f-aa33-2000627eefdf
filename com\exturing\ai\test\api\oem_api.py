# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.oem import OemModel, OemQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.oem_service import create, query_page, update, delete

oem = Blueprint('oem', __name__)

# 新增OEM
@oem.route(f'/{URL_PREFIX}/oem/create', methods=['POST'])
def oem_create():
    et_log.info("############oem_create################")
    req_data = request.get_json()
    oem_instance = OemModel(**req_data)
    oem_id = create(oem_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(oem_id)).to_json()

# OEM分页查询
@oem.route(f'/{URL_PREFIX}/oem/page', methods=['POST'])
def oem_page():
    et_log.info("############oem_page################")
    data = request.get_json()
    query = OemQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改OEM
@oem.route(f'/{URL_PREFIX}/oem/update', methods=['POST'])
def oem_update():
    et_log.info("############oem_update################")
    req_data = request.get_json()
    id = req_data.get("oem_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "oem_id is null", "").to_json()

    oem_instance = OemModel(**req_data)
    update_result = update(id, oem_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除OEM
@oem.route(f'/{URL_PREFIX}/oem/del', methods=['POST'])
def oem_delete():
    et_log.info("############oem_delete################")
    req_data = request.get_json()
    id = req_data.get("oem_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "oem_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()
