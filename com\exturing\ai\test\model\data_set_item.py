from typing import Dict

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel
from com.exturing.ai.test.comm.condition_util import model_dimension_condition
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel


# 数据集数据项
class EtDataSetItem(BaseModel):
    _doc = "et_data_set_item"
    data_set_id: str  # 测试的数据集id
    parent_id: str # 上一轮id
    metric_id: ObjectId # 指标
    model_id: ObjectId # 模块 id
    dimension_id: ObjectId # 维度 id
    item_type: int  # 类型
    scene_id: ObjectId  # 场景
    question: str  # 问题
    expected_answer: str  # 期望回答
    expected_task: str  # 期望任务类型（excel值）
    expected_category: str  # 期望落域（excel值）
    item_tags: str  # 标签
    is_last: int # 是否终轮
    real_time: str  # 语料相关时间

    qa_keywords: str # 关键词
    data_src: str # 数据来源

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", parent_id="", data_set_id="",
                 metric_id="", model_id="", dimension_id="", item_type=0, scene_id="", question="", expected_answer="",
                 expected_task="", expected_category="", item_tags="", is_last=1, real_time="", qa_keywords="", data_src=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.parent_id = ObjectId(parent_id) if parent_id and len(str(parent_id)) > 0 else ""
        self.data_set_id = ObjectId(data_set_id) if data_set_id and len(str(data_set_id)) > 0 else ""
        self.metric_id = ObjectId(metric_id) if metric_id and len(str(metric_id)) > 0 else ""
        self.model_id = ObjectId(model_id) if model_id and len(str(model_id)) > 0 else ""
        self.dimension_id = ObjectId(dimension_id) if dimension_id and len(str(dimension_id)) > 0 else ""
        self.item_type = item_type
        self.scene_id = ObjectId(scene_id) if scene_id and len(str(scene_id)) > 0 else ""
        self.question = question
        self.expected_answer = expected_answer
        self.expected_task = expected_task
        self.expected_category = expected_category
        self.item_tags = item_tags
        self.is_last = is_last
        self.real_time = real_time
        self.qa_keywords = qa_keywords
        self.oem_codes = oem_codes
        self.data_src = data_src

    # 写数据使用
    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = self._id

        base_json["parent_id"] = ObjectId(self.parent_id) if self.parent_id and len(str(self.parent_id)) > 0 else ""
        base_json["data_set_id"] = ObjectId(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        base_json["metric_id"] = ObjectId(self.metric_id) if self.metric_id and len(str(self.metric_id)) > 0 else ""
        base_json["model_id"] = ObjectId(self.model_id) if self.model_id and len(str(self.model_id)) > 0 else ""
        base_json["dimension_id"] = ObjectId(self.dimension_id) if self.dimension_id and len(str(self.dimension_id)) > 0 else ""
        base_json["item_type"] = self.item_type if self.item_type else 0
        base_json["scene_id"] = ObjectId(self.scene_id) if self.scene_id and len(str(self.scene_id)) > 0 else ""
        base_json["question"] = self.question
        base_json["expected_answer"] = self.expected_answer
        base_json["expected_task"] = self.expected_task
        base_json["expected_category"] = self.expected_category
        base_json["item_tags"] = self.item_tags
        base_json["is_last"] = self.is_last
        base_json["real_time"] = self.real_time

        base_json["qa_keywords"] = self.qa_keywords
        base_json["data_src"] = self.data_src
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["parent_id"] = str(self.parent_id) if self.parent_id and len(str(self.parent_id)) > 0 else ""
        data_json["data_set_id"] = str(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        data_json["metric_id"] = str(self.metric_id) if self.metric_id and len(str(self.metric_id)) > 0 else ""
        data_json["model_id"] = str(self.model_id) if self.model_id and len(str(self.model_id)) > 0 else ""
        data_json["dimension_id"] = str(self.dimension_id) if self.dimension_id and len(str(self.dimension_id)) > 0 else ""
        data_json["scene_id"] = str(self.scene_id) if self.scene_id and len(str(self.scene_id)) > 0 else ""
        return data_json

    # 根据数据集id，查询数据项数量
    @classmethod
    def find_count_set_id(cls, set_id):
        condition = {"parent_id": "", "data_set_id": ObjectId(set_id)}
        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据数据集id，查询所有数据项
    @classmethod
    def find_all_by_set_id(cls, set_id):
        condition = {"data_set_id": ObjectId(set_id)}
        return MongoDBUtil.find_all_by_set_id(cls._doc, condition)



    # 根据数据集id删除其下的所有数据记录项
    @classmethod
    def del_by_set_id(cls, set_id, do_user):
        condition = {"data_set_id": ObjectId(set_id)}
        # del_pro = {"is_del": 1, "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        return MongoDBUtil.delete_by_many(cls._doc, condition, do_user)

    # 根据set_id数据集id、model_id模块、dimension_id维度、question数据项名称模糊查询数据集数据项匹配记录数
    @classmethod
    def find_page_count(cls, set_id, model_id, dimension_id, question: str, model_dimension, item_tags,
                        real_time_start="", real_time_end=""):
        condition = {}
        if set_id is None or len(set_id) == 0:
            et_log.error(f"find_condition set_id is null")
            return None
        if question and question.strip() != "":
            condition = {"question": {"$regex": str(question).strip(), "$options": "i"}, "is_del": 0}
        condition["data_set_id"] = ObjectId(set_id)
        if model_id is not None and len(model_id) > 0:
            condition["model_id"] = ObjectId(model_id)
        if dimension_id is not None and len(dimension_id) > 0:
            condition["dimension_id"] = ObjectId(dimension_id)
        if real_time_start is not None and len(real_time_start) > 0:
            # condition["real_time"]["$ne"] = "NaT"
            # condition["real_time"]["$gte"] = real_time_start
            condition["real_time"] = {"$ne":"NaT", "$gte": real_time_start}
        if real_time_end is not None and len(real_time_end) > 0:
            if "real_time" in condition:
                condition["real_time"]["$lte"] = real_time_end
            else:
                condition["real_time"] = {"$ne":"NaT", "$lte": real_time_end}
        condition["parent_id"] = "" # 分页只查一级数据项
        if item_tags and len(item_tags) > 0:
            regex_conditions = [{"item_tags": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in item_tags]
            condition["$or"] = regex_conditions

        model_dimension_condition(condition, model_dimension)

        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据set_id数据集id、model_id模块、dimension_id维度、question数据项名称模糊查询数据集数据项分页的集合
    @classmethod
    def find_page_condition(cls, set_id, model_id, dimension_id, question: str, model_dimension, item_tags, limit, skip,
                        real_time_start="", real_time_end=""):
        condition = {"is_del": 0}
        if set_id is None or len(set_id) == 0:
            et_log.error(f"find_condition set_id is null")
            return None
        if question and question.strip() != "":
            condition["question"] = {"$regex": str(question).strip(), "$options": "i"}
        condition["data_set_id"] = ObjectId(set_id)
        if model_id is not None and len(model_id) > 0:
            condition["model_id"] = ObjectId(model_id)
        if dimension_id is not None and len(dimension_id) > 0:
            condition["dimension_id"] = ObjectId(dimension_id)
        if real_time_start is not None and len(real_time_start) > 0:
            condition["real_time"] = {"$ne":"NaT", "$gte": real_time_start}
        if real_time_end is not None and len(real_time_end) > 0:
            if "real_time" in condition:
                condition["real_time"] = condition["real_time"]|{"$lte":real_time_end}
            else:
                condition["real_time"] = {"$ne":"NaT", "$lte": real_time_end}
        if dimension_id is not None and len(dimension_id) > 0:
            condition["dimension_id"] = ObjectId(dimension_id)
        condition["parent_id"] = "" # 分页只查一级数据项

        if item_tags and len(item_tags) > 0:
            regex_conditions = [{"item_tags": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in item_tags]
            condition["$or"] = regex_conditions

        model_dimension_condition(condition, model_dimension)

        result = MongoDBUtil.find_condition_page(cls._doc, condition, None, skip, limit)
        if result is not None:
            items = []
            for item in result:
                et_item = EtDataSetItem(**item)
                items.append(et_item)
            return items
        else:
            return None

    # 根据parent_id查询数据集数据项
    @classmethod
    def find_by_pid(cls, pid):
        condition = {"is_del": 0}
        if not pid or len(str(pid)) == 0:
            et_log.error(f"find_by_pid error, the parent_id parameter is empty")
            return None
        condition["parent_id"] = ObjectId(str(pid))
        count = cls.find_count_by_pid(pid)
        data_list = MongoDBUtil.find_condition_page(cls._doc, condition, None, 0, count)
        if data_list is not None:
            items = []
            for item in data_list:
                et_item = EtDataSetItem(**item)
                items.append(et_item)
            return items
        else:
            return None

    # 根据父id，查询其子节点个数
    @classmethod
    def find_count_by_pid(cls, pid):
        condition = {"is_del": 0}
        if not pid or len(str(pid)) == 0:
            et_log.error(f"find_by_pid error, the parent_id parameter is empty")
            return 0
        condition["parent_id"] = ObjectId(pid)
        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据条件查询数据匹配记录数
    @classmethod
    def find_comm_condition_count(cls, condition):
        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据条件查询数据的集合
    @classmethod
    def find_comm_condition(cls, condition, sorts, limit, skip):
        result = MongoDBUtil.find_condition_page(cls._doc, condition, sorts, skip, limit)
        if result is not None:
            items = []
            for item in result:
                plan = EtDataSetItem(**item)
                items.append(plan)
            return items
        else:
            return None

    # 根据set_id数据集id、model_id模块、dimension_id维度、question数据项名称模糊查询数据集数据项分页的集合
    @classmethod
    def find_nameondition(cls, set_id, model_id, dimension_id, question: str, limit, skip):
        condition = {"is_del": 0}
        if set_id is None or len(set_id) == 0:
            et_log.error(f"find_condition set_id is null")
            return None
        if question and question.strip() != "":
            condition["question"] = {"$regex": str(question).strip(), "$options": "i"}
        condition["data_set_id"] = ObjectId(set_id)
        if model_id is not None and len(model_id) > 0:
            condition["model_id"] = ObjectId(model_id)
        if dimension_id is not None and len(dimension_id) > 0:
            condition["dimension_id"] = ObjectId(dimension_id)
        condition["parent_id"] = "" # 分页只查一级数据项

        result = MongoDBUtil.find_condition_page(cls._doc, condition, None, skip, limit)
        if result is not None:
            items = []
            for item in result:
                et_item = EtDataSetItem(**item)
                items.append(et_item)
            return items
        else:
            return None

    @classmethod
    def update_batch_json(cls, condition = {}, entity_json = {}):
        return MongoDBUtil.update_many(cls._doc, condition, entity_json)

    @classmethod
    def revise_dataset_update_origin_item(cls, set_item_id, post_data: Dict):
        """
        根据set_item_id对应数据集数据项，检查数据项是否变更；若变更，且是对应原数据创建人相同，则同步修改原数据内容
        :param set_item_id: 数据集数据项id
        :param post_data: 待变更的数据字典对象 model_id|dimension_id|expected_task|expected_category|expected_answer
        :return:
        """
        origin_item_doc = "origin_data_item"
        # 检查参数是否是空值 空值=Ture
        model_empty = "model_id" not in post_data or str(post_data.get("model_id", "")).strip() == ""
        dimension_empty = "dimension_id" not in post_data or str(post_data.get("dimension_id", "")).strip() == ""
        exp_task_empty = "expected_task" not in post_data or post_data.get("expected_task", "").strip() == ""
        exp_category_empty = "expected_category" not in post_data or post_data.get("expected_category", "").strip() == ""
        exp_answer_empty = "expected_answer" not in post_data or post_data.get("expected_answer", "").strip() == ""
        if model_empty and dimension_empty and exp_task_empty and exp_category_empty and exp_answer_empty:
            et_log.error(f"revise_dataset_update_origin_item not change,model_empty:{model_empty} dimension_empty:{dimension_empty} "
                         f"exp_task_empty:{exp_task_empty} exp_category_empty:{exp_category_empty} exp_answer_empty:{exp_answer_empty}")
            return

        item_row = EtDataSetItem.find_by_pk(set_item_id)
        set_item = EtDataSetItem(**item_row)
        if not set_item:
            et_log.error(f"revise_dataset_update_origin_item set_item_id:{set_item_id} not found data")
            return
        # 检查当前操作人与原数据创建人是否相同，相同才可更新
        origin_row = MongoDBUtil.find_one(origin_item_doc, {"question": set_item.question})
        if not origin_row:
            et_log.error(f"revise_dataset_update_origin_item question:{set_item.question} not found origin data")
            return
        origin_item = OriginDataItemModel(**origin_row)
        if origin_item.create_by != CTX_USER_ID.get():
            et_log.error(f"revise_dataset_update_origin_item current user:{CTX_USER_ID.get()} not same origin_item create user")
            return

        set_data = {}
        if not model_empty and str(post_data.get("model_id")) != str("" if origin_item.model_id is None else origin_item.model_id):
            set_data["model_id"] = str(post_data.get("model_id"))
        if not dimension_empty and str(post_data.get("dimension_id")) != str("" if origin_item.dimension_id is None else origin_item.dimension_id):
            set_data["dimension_id"] = str(post_data.get("dimension_id"))
        if not exp_task_empty and str(post_data.get("expected_task")) != str("" if origin_item.expected_task is None else origin_item.expected_task):
            set_data["expected_task"] = post_data.get("expected_task")
        if not exp_category_empty and str(post_data.get("expected_category")) != str("" if origin_item.expected_category is None else origin_item.expected_category):
            set_data["expected_category"] = post_data.get("expected_category")
        if not exp_answer_empty and str(post_data.get("expected_answer")) != str("" if origin_item.expected_answer is None else origin_item.expected_answer):
            set_data["expected_answer"] = post_data.get("expected_answer")
        if not set_data:
            et_log.error(f"revise_dataset_update_origin_item up pro same now pro by OriginDataItemModel")
            return
        set_data["_id"] = origin_row["_id"]
        update_result = MongoDBUtil.update_one_pro(origin_item_doc, set_data)
        et_log.info(f"revise_dataset_update_origin_item update {update_result.modified_count} rows OriginDataItemModel")

    @classmethod
    def up_del_by_pk(cls, pk:str):
        """
        根据主键，向上删除，直到删除pid=""
        :param pk: 主键_id
        :return: True|False
        """
        row = cls.find_by_pk(pk)
        if not row:
            et_log.error(f"up_del_by_pk pk:{pk} is invalid")
            return False
        item = EtDataSetItem(**row)

        # 如果当前节点有父节点，则迭代向上删除
        if item.parent_id and len(str(item.parent_id)) > 0:
            cls.up_del_by_pk(str(item.parent_id))
            cls.delete_by_id(pk, CTX_USER_ID.get())
        return True


# print(EtDataSetItem.find_count_set_id("6780cd1ca8fd20700744a48b"))

