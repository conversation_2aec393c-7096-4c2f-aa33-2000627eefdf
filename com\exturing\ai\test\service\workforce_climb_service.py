# service.py
from com.exturing.ai.test.comm.comm_util import (
    float_format,
    get_id_from_tree_by_label,
    month_format,
)
from com.exturing.ai.test.model.project_requirement import ProjectRequirementQueryModel
from com.exturing.ai.test.service.project_requirement_service import query_tree
from com.exturing.ai.test.model.workforce_climb import (
    WorkforceClimbModel,
    WorkforceClimbQueryModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from werkzeug.datastructures import FileStorage
from com.exturing.ai.test.comm.log_tool import et_log
import pandas as pd


_doc = "workforce_climb"


# 新增人力爬坡
def create(data: WorkforceClimbModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create workforce_climb: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询人力爬坡
def query_page(
    page_num: int = 1, page_size: int = 10, query: WorkforceClimbQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询人力爬坡列表
def query_list(query: WorkforceClimbQueryModel = None):
    condition = query.get_query_condition()

    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    return json_list


# 修改人力爬坡
def update(id: str, data: WorkforceClimbModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update workforce_climb: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除人力爬坡
def delete(id: str) -> bool:
    et_log.info(f"delete workforce_climb by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 导入人力爬坡数据
def import_data(project_id, file: FileStorage):
    all_sheets = pd.read_excel(file, sheet_name=None)

    requirement_query = ProjectRequirementQueryModel(project_id=project_id)
    requirement_tree = query_tree(requirement_query)

    def append_insert_list(item: dict, insert_list: list = []):
        if not item:
            return insert_list

        requirement = item.get("需求/任务")
        requirement_id = get_id_from_tree_by_label(requirement, requirement_tree)
        role = item.get("角色")
        type = item.get("类型")
        base = item.get("Base地")

        for key in item.keys():
            insert_item = {
                "project_id": project_id,
                "requirement_id": requirement_id,
                "role": role,
                "type": type,
                "base": base,
            }

            month = month_format(key)
            if month:
                labor = float_format(key, item)
                if labor is not None:
                    insert_item["month"] = month
                    insert_item["labor"] = labor
                    insert_list.append(insert_item)

        return insert_list

    for sheet_name in all_sheets:
        df = all_sheets[sheet_name]
        sheet_list = df.to_dict(orient="records")

        insert_list = []

        for item in sheet_list:
            insert_list = append_insert_list(item, insert_list)

        if insert_list:
            MongoDBUtil.insert_many(_doc, insert_list)

    return True
