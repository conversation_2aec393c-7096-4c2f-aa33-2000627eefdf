
from datetime import datetime
import time

from flask import Blueprint, Response, request

from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.dto.params.chat_box_dto import ChatBoxParamDto
from com.exturing.ai.test.service.chat_box_service import ChatBoxService

chat_box=Blueprint('chat_box',__name__)
@chat_box.route(f'/{URL_PREFIX}/chat-box/query', methods=['POST'])
def query():
    """AI chat box query"""
    chat_param = ChatBoxParamDto(**request.get_json())
    return Response(ChatBoxService.query(chat_param), mimetype='text/event-stream')
    # return Response(ChatBoxService.query(chat_param), mimetype='application/json')