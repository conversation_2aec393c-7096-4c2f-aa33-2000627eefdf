import asyncio
import copy
import json
import time
import traceback
from datetime import datetime
from uuid import uuid4

import websockets
from websockets import ConnectionClosedError

from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import dsk_prompt
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import convert_to_tree, extract_questions






# adapter_code=agent-llm-hub agent中枢测试评估服务
class AgentLlmHubAdapter:

    # 针对ask进行同步
    music_async_template = {
        "async": {
            "task": "音乐",
            "widget": {},

        },
        "recordId": None,
        "sessionId": None,
        "topic": "dm.context.sync"
    }

    output_template = {
        ""
    }

    # 单条记录运行
    @classmethod
    async def run_agent_hub_item(cls, config_info: TestConfigInfoModel, item: EtDataSetItem, record_id, request_id, session_id, parent_id,
                                 task_id,
                                 task_result_id, do_user, children):
        et_log.info(f"run_agent_hub_item params config_info:{config_info} item:{item} record_id:{record_id} request_id:{request_id} "
                    f"session_id:{session_id} parent_id:{parent_id} task_id:{task_id} task_result_id:{task_result_id}")
        try:
            uri = config_info.env_uri
            uri_params = {"alias": "", "productId": "", "deviceId": "", "userId": ""}
            if config_info.branch_name and len(config_info.branch_name) > 0:
                uri_params["alias"] = config_info.branch_name
            if config_info.pid_val and len(config_info.pid_val) > 0:
                uri_params["productId"] = config_info.pid_val
            if config_info.device_id and len(config_info.device_id) > 0:
                uri_params["deviceId"] = config_info.device_id
            if config_info.user_id and len(config_info.user_id) > 0:
                uri_params["userId"] = config_info.user_id
            uri = uri.format(**uri_params)

            origin_data = [item.to_json_str()] + children
            et_log.info(f"uri:{uri} origin_data:{origin_data}")
            origin_tree_data = convert_to_tree(origin_data)
            et_log.info(f"origin_tree_data:{origin_tree_data}")
            prompts = extract_questions(origin_tree_data[0])
            dia_his = []
            et_log.info(f"prompts____________________________-:{prompts}")
            item_result_id = None  # Initialize item_result_id here, so that it can be updated throughout the loop
            for j in range(len(prompts)):
                if prompts[j] != '':
                    tmp_template = copy.deepcopy(json.loads(config_info.env_script)['template'])
                    print(f"tmp_template:{tmp_template}")
                    tmp_template['sessionId'] = session_id
                    tmp_template['recordId'] = uuid4().hex
                    tmp_template['refText'] = prompts[j]
                    tmp_template['topic'] = 'nlu.input.text'
                    start_now = time.time()
                    if config_info.version and len(config_info.version) > 0:
                        tmp_template['context']['attributes']['agent_protocal_version'] = config_info.version

                    if j == 0:
                        async with websockets.connect(uri, ping_interval=10, ping_timeout=100) as websocket:
                            response_json, first_token_time = await cls.send_websocket(uri, websocket, tmp_template)
                            end_time = time.time()
                            et_log.info(
                                f"run_agent_hub_item websockets response first_token_time:{first_token_time} data:{response_json}")
                    else:
                        tmp_async_template = copy.deepcopy(json.loads(config_info.env_script)['async_template'])
                        print(f"tmp_async_template:{tmp_async_template}")
                        for m in range(len(dia_his)):
                            print(f"tmp_async_template:{m}")
                            tmp_dialogHistory_template = copy.deepcopy(
                                json.loads(config_info.env_script)['dialogHistory_template'])
                            print(f"tmp_dialogHistory_template:{tmp_dialogHistory_template}")
                            tmp_dialogHistory_template['recordId'] = dia_his[m][2]
                            tmp_dialogHistory_template['input'] = dia_his[m][0]
                            tmp_dialogHistory_template['output']['widget']['displayText'] = dia_his[m][1]
                            tmp_dialogHistory_template['output']['speak'] = dia_his[m][1]
                            tmp_async_template['async']['dispatchEvent']['dialogHistory'].append(
                                copy.deepcopy(tmp_dialogHistory_template))
                        tmp_async_template['sessionId'] = session_id
                        tmp_async_template['recordId'] = uuid4().hex
                        tmp_template['context']['skill'] = {"skillId": str(dia_his[-1][4]), "task": dia_his[-1][3]}
                        async with websockets.connect(uri) as websocket:
                            response_json, first_token_time = await cls.async_and_send_messages(uri, websocket, (
                            tmp_async_template, tmp_template))
                        end_time = time.time()

                    actual_task, actual_category, actual_answer, record_id, skill, skill_id, error_id, re_cmd, image_url = (
                        cls.process_response(response_json))
                    dia_his.append((prompts[j], actual_answer, record_id, skill, skill_id))
                    print(f"dia_his:{dia_his}")
                    first_res_time = float(first_token_time - start_now)
                    first_res_time_divided = round(first_res_time, 2)
                    et_log.info(f"首次响应时间: {first_res_time_divided} 秒")
                    elapsed_time = float(end_time - start_now)
                    elapsed_time_divided = round(elapsed_time, 2)
                    et_log.info(f"该语料第{j+1}轮耗时: {elapsed_time_divided} 秒")
                    et_log.info(
                        f"run_agent_hub_item process_response actual_task:{uri} actual_category:{actual_category} "
                        f"actual_answer:{actual_answer} record_id:{record_id} skill:{skill} skill_id:{skill_id} "
                        f"re_cmd:{re_cmd} image_url:{image_url}")
                    if not actual_answer or len(actual_answer) == 0:
                        et_log.error(f"run_agent_hub_item actual_answer is null")
                        actual_answer = "N/A"
                    result_task = 0
                    result_category = 0
                    result_answer = 0
                    final_result = 0
                    if item.expected_answer and len(item.expected_answer) > 0 and str(item.expected_answer) in str(
                            actual_answer):
                        result_answer = 1
                    if item.expected_task and len(item.expected_task) > 0 and str(item.expected_task) == str(
                            actual_task):
                        result_task = 1
                    if item.expected_category and len(item.expected_category) > 0 and str(
                            item.expected_category) == str(actual_category):
                        result_category = 1
                    if result_answer == 1 and 1 == result_task and 1 == result_category:
                        final_result = 1
                    if j ==0:
                        itemid= item.id
                        parent_id = parent_id
                    else:
                        itemid = origin_data[j]["_id"]
                        parent_id = item_result_id

                    test_task: TestTask = TestTask(**TestTask.find_by_pk(task_id))
                    item_result = {
                        "parent_id": parent_id,
                        "data_set_item_id": str(itemid),
                        "task_id": task_id,
                        "task_result_id": str(task_result_id),
                        "expected_answer": origin_data[j]['expected_answer'],
                        "expected_category": origin_data[j]['expected_category'],
                        "expected_task": origin_data[j]['expected_task'],
                        "actual_category": actual_category,
                        "actual_task": actual_task,
                        "result_task": result_task,
                        "result_category": result_category,
                        "actual_answer": actual_answer,
                        "result_answer": result_answer,
                        "answer_score": 0,
                        "qa_recall": 0,
                        "qa_keywords": "",
                        "first_res_time": first_res_time,
                        "qa_use_time": elapsed_time_divided,
                        "eval_config_id": test_task.config_id,
                        "result_final": final_result,
                        "recall_id": record_id,
                        "remark": f"request_id:{request_id}|session_id:{session_id}",
                        "do_user": do_user,
                        "re_url": image_url,
                        "re_cmd": re_cmd,
                        "re_error_id": error_id,
                        "re_interval_time": 0,
                        "is_websearch": 0
                    }
                    # 记录每条数据的执行结果
                    item_result_id = TestItemResultService.insert_one(item_result)
                    et_log.info(f"run_agent_hub_item insert_one item_result_id:{item_result['data_set_item_id']}")

                    # Update parent_id with the current item_result_id after each round

            return item_result_id  # Return after all rounds are done

        except Exception as e:
            et_log.error(f"run_agent_hub_item item_id:{item.id} exception,\n{traceback.print_exc()}")
            return None

    # 发送websocket请求
    @classmethod
    async def send_websocket(cls, uri, websocket, message):
        et_log.info(f"send_websocket uri:{uri} websocket:{websocket} message:{message}")
        try:
            await websocket.send(json.dumps(message))
            return await asyncio.create_task(cls.response_handler(websocket))
        except ConnectionClosedError as e:
            et_log.error(f"send_websocket WebSocket connection closed: {e}")
            # 重试连接
            async with websockets.connect(uri, open_timeout=180, close_timeout=180, ping_interval=20,
                                          ping_timeout=60) as new_websocket:
                await new_websocket.send(json.dumps(message))
                return await asyncio.create_task(cls.response_handler(new_websocket))



    # 处理websocket响应
    @classmethod
    async def response_handler(cls, websocket):
        # 处理返回的结果
        answering_time = None
        first_res = False
        first_token_time = time.time()
        final_response = {}
        response_new = {}
        ask_type = 0
        while True:
            try:
                tmp_response = await asyncio.wait_for(websocket.recv(), timeout=180)
                et_log.info(f"response_handler tmp_response:{tmp_response}")
                if """streamType":"start""" in tmp_response:
                    if not answering_time:
                        answering_time = time.time()
                        first_res = True
                    continue
                if first_res:
                    if answering_time:
                        if """streamType":"intermediate""" in tmp_response:
                            first_token_time = time.time()
                            first_res = False
                if first_token_time == 0:
                    first_res = True
                    if """skill":"中枢大模型技能""" in tmp_response:
                        first_token_time = time.time()
                        first_res = False
                response_old = json.loads(tmp_response)
                api = response_old.get("dm", {}).get("command", {}).get("api", None)
                ask = response_old.get("dm", {}).get("command", {}).get("param", {}).get("customInnerQuery", None)

                # 1.1协议下音乐自由说
                if ask == 'ask':
                    response_new = response_old
                    ask_type = 1
                    tmp_widget = {"customInnnerType": "replyAsk", 'status': 200, 'extra': {}}
                    tmp_widget['extra']['currentMedia'] = {}
                    tmp_widget['extra']['currentMedia']['music'] = {}
                    tmp_widget['extra']['currentMedia']['music']['title'] = response_old.get("dm", {}).get("command", {}).get("param", {}).get("song", '')
                    tmp_widget['extra']['currentMedia']['music']['album'] = ""
                    tmp_widget['extra']['currentMedia']['music']['artist'] = response_old.get("dm", {}).get("command",{}).get("param", {}).get("singer", '')
                    tmp_music_async_template = copy.deepcopy(cls.music_async_template)
                    tmp_music_async_template['async']['widget'] = tmp_widget
                    tmp_music_async_template['skillId'] = response_old.get("nlu", {}).get("skillId", '')
                    tmp_music_async_template['skill'] = response_old.get("nlu", {}).get("skill", '')
                    tmp_music_async_template['recordId'] = uuid4().hex
                    tmp_music_async_template['sessionId'] = response_old.get("sessionId", None)
                    await websocket.send(json.dumps(tmp_music_async_template))

                if api and api == "discardResponse":# 拒识
                    final_response = response_old
                    break
                elif """streamType":"done""" in tmp_response:
                    if ask_type == 1:
                        response_new['dm']['widget'] = response_old.get("dm", {}).get("widget", {})
                        final_response = response_new
                    else:
                        final_response = response_old
                    break
                elif """streamType""" in tmp_response:
                    pass
                else:
                    if api:
                        final_response = response_old
                        break
                    else:
                        final_response = response_old
                        break
            except websockets.exceptions.ConnectionClosedOK:
                et_log.error(f"response_handler websocket connection closed")
                break
            except Exception as e:
                et_log.error(f"response_handler websocket response handler exception,\n{traceback.print_exc()}")
                break
        first_token_time = time.time() if first_token_time is None else first_token_time
        return final_response, first_token_time

    @classmethod
    async def async_and_send_messages(cls,uri,websocket, testcase):
        try:
            await websocket.send(json.dumps(testcase[0]))
            await websocket.send(json.dumps(testcase[1]))
            return await asyncio.create_task(cls.response_handler(websocket))
        except ConnectionClosedError as e:
            print(f"WebSocket connection closed: {e}")
            # 重试连接
            async with websockets.connect(uri, open_timeout=3000, close_timeout=3000, ping_interval=20,
                                          ping_timeout=60) as new_websocket:
                await websocket.send(json.dumps(testcase[0]))
                await websocket.send(json.dumps(testcase[1]))
                return await asyncio.create_task(cls.response_handler(new_websocket))

    @classmethod
    def process_response(cls, response):
        task = ''# 实际任务
        category = ''# 实际分类
        displayText = ''# 实际回答
        skill = response.get('skill')
        skillId = response.get('skillid')
        recordId = response.get('recordId')
        api_value = ''# cmd
        image_url = ''# re url
        errorId = 'N/A'
        if skill != '中枢大模型技能':
            task = skill
            displayText = response.get('dm', {}).get("widget", {}).get("displayText", '')
            if 'error' in response and 'errId' in response['error']:
                errorId = response.get('error', {}).get("errId", 'N/A')
            if 'command' in response.get('dm', {}):
                api_value = response.get('dm', {}).get("command", {}).get("api", '')
            if 'llmOutputData' in response.get('dm', {}).get("widget", {}):
                llm_output_data = response['dm']['widget']['llmOutputData']
                for item in llm_output_data:
                    if item['type'] == 'image':
                        image_url = item.get('text', '')
        else:
            api = response.get('dm', {}).get("command", {}).get("api", '')
            if api == "discardResponse":
                category = "拒识"
            else:
                category = response.get('dm', {}).get("widget", {}).get("llmTaskname", '')
                displayText = response.get('dm', {}).get("widget", {}).get("displayText", '')
                if 'error' in response and 'errId' in response['error']:
                    errorId = response.get('error', {}).get("errId", 'N/A')
                api_value = api
                if 'llmOutputData' in response.get('dm', {}).get("widget", {}):
                    llm_output_data = response['dm']['widget']['llmOutputData']
                    for item in llm_output_data:
                        if item['type'] == 'image':
                            image_url = item.get('text', '')
        return task, category, displayText, recordId, skill, skillId, errorId, api_value, image_url