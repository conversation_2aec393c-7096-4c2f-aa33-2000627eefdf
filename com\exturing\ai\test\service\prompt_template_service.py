# service.py
from com.exturing.ai.test.model.prompt_template import (
    PromptTemplateModel,
    PromptTemplateQueryModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.tool.data_auto_generate import CorpusGenerator

from com.exturing.ai.test.comm.log_tool import et_log

_doc = "prompt_template"


# 新增语料生成模板
def create(data: PromptTemplateModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create prompt_template: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询语料生成模板
def query_page(
    page_num: int = 1, page_size: int = 10, query: PromptTemplateQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 修改语料生成模板
def update(id: str, data: PromptTemplateModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update prompt_template: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除语料生成模板
def delete(id: str) -> bool:
    et_log.info(f"delete prompt_template by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 根据ID获取语料生成模板
def find_by_id(id: str) -> dict:
    et_log.info(f"find prompt_template by id:{id}")
    return MongoDBUtil.find_by_id(_doc, id)


# 大模型生成语料
def generate(id: str) -> list:
    et_log.info(f"generate prompt by prompt_template: {id}")

    prompt_template = find_by_id(id)
    if not prompt_template:
        et_log.error(f"prompt_template not found by id: {id}")
        return []

    models = DicService.query_list(None, None, "dm-model") or []
    dimensions = DicService.query_list(None, None, "dm-dimension") or []

    model_list = list(models or [])
    dimension_list = list(dimensions or [])
    dimension_id_list = (prompt_template.get("dimension_ids") or "").split(",")

    model_names = [
        item.get("name")
        for item in model_list
        if item.get("_id") == prompt_template.get("model_id")
    ]
    dimension_names = [
        item.get("name")
        for item in dimension_list
        if item.get("_id") in dimension_id_list
    ]

    data_dict = {
        "model": model_names[0] if model_names else "",
        "dimensions": ",".join(dimension_names) if dimension_names else "",
        "prompt": prompt_template.get("prompt", ""),
        "count": prompt_template.get("count", 10),
    }

    generator = CorpusGenerator()
    result = generator.analyze_corpus(data_dict.get("prompt"))
    data = result.get("data")
    model = data.get("model")
    corpus = data.get("corpus")

    et_log.info(f"generate prompt: {data_dict.get('prompt')}, data: {data}")

    res = [
        {
            "question": item.get("question", ""),
            "expected_answer": item.get("answer", ""),
            "model": model,
            "dimension": "",
        }
        for item in corpus
    ]

    def find_id_by_name(name, data_list):
        for item in data_list:
            if item.get("name") == name:
                return str(item.get("_id"))
        return None

    data_list = [
        {
            "question": item.get("question"),
            "expected_answer": item.get("expected_answer"),
            "model_id": find_id_by_name(item.get("model"), model_list),
            "dimension_id": find_id_by_name(item.get("dimension"), dimension_list),
        }
        for item in res
    ]

    return data_list
