from bson import ObjectId

def model_dimension_condition(condition, model_dimension):
    if model_dimension:
        model_ids = model_dimension.get("model_ids", [])
        dimension_ids = model_dimension.get("dimension_ids", [])

        model_ids = [ObjectId(model_id) for model_id in model_ids] + model_ids
        dimension_ids = [ObjectId(dimension_id) for dimension_id in dimension_ids] + dimension_ids

        if model_ids and dimension_ids:
            condition["$or"] = [{"model_id": {"$in": model_ids}}, {"dimension_id": {"$in": dimension_ids}}]
        elif model_ids:
            condition["model_id"] = {"$in": model_ids}
        elif dimension_ids:
            condition["dimension_id"] = {"$in": dimension_ids}