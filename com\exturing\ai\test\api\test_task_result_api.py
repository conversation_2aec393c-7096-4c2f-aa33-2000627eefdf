import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_task_result_service import TaskResultService

test_task_result=Blueprint('test_task_result',__name__)

# 新增
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/create', methods=['POST'])
def test_task_result_create():
    et_log.info("############test_task_result_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_result_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_task_create error, param is null", "").to_json()
        if "task_id" not in data or len(str(data["task_id"]).strip()) == 0:
            et_log.error(f"test_task_result_create error, task_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_id in param is null ", "").to_json()
        data["subjective_metric_id"] = str(data["subjective_metric_id"]).strip() if "subjective_metric_id" in data and len(str(data["subjective_metric_id"])) > 0 else ""
        data["objective_metric_id"] = str(data["objective_metric_id"]).strip() if "objective_metric_id" in data and len(str(data["objective_metric_id"])) > 0 else ""
        data["rate_final"] = data["rate_final"] if ("rate_final" in data and data["rate_final"] is not None) else float(0.0000)
        data["rate_answer"] = data["rate_answer"] if ("rate_answer" in data and data["rate_answer"] is not None) else float(0.0000)
        data["rate_category"] = data["rate_category"] if ("rate_category" in data and data["rate_category"] is not None) else float(0.0000)
        data["rate_ad_final"] = data["rate_ad_final"] if ("rate_ad_final" in data and data["rate_ad_final"] is not None) else float(0.0000)
        data["rate_ad_answer"] = data["rate_ad_answer"] if ("rate_ad_answer" in data and data["rate_ad_answer"] is not None) else float(0.0000)
        data["rate_ad_category"] = data["rate_ad_category"] if ("rate_ad_category" in data and data["rate_ad_category"] is not None) else float(0.0000)
        data["rate_recall"] = data["rate_recall"] if ("rate_recall" in data and data["rate_recall"] is not None) else float(0.0000)
        data["satisfactory"] = data["satisfactory"] if ("satisfactory" in data and data["satisfactory"] is not None) else int(1)
        data["total_use_time"] = data["total_use_time"] if ("total_use_time" in data and data["total_use_time"] is not None) else float(0.0000)
        data["avg_score"] = data["avg_score"] if ("avg_score" in data and data["avg_score"] is not None) else float(0.0000)
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        data["result_status"] = 0
        insert_result = TaskResultService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"test_task_result_create create test_task exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 查询
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/query', methods=['POST'])
def test_task_result_query():
    et_log.info("############test_task_result_query################")
    data = request.get_json()
    # if not data or "task_id" not in data or len(str(data["task_id"]).strip()) == 0:
    #     et_log.error(f"test_task_result_query query error, task_id is null")
    #     return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id of param is null").to_json()
    # data["task_id"] = str(data["task_id"]).strip()
    if not data or "task_id" not in data or data["task_id"] is None or len(str(data["task_id"]).strip()) == 0:
        data["task_id"] = ""
    if not data or "result_status" not in data or data["result_status"] is None or data["result_status"] < 0:
        data["result_status"] = None
    if not data or "create_from_time" not in data or data["create_from_time"] is None or len(str(data["create_from_time"]).strip()) == 0:
        data["create_from_time"] = ""
    if not data or "create_to_time" not in data or data["create_to_time"] is None or len(str(data["create_to_time"]).strip()) == 0:
        data["create_to_time"] = ""
    if not data or "page_num" not in data or data["page_num"] is None or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] is None or data["page_size"] < 1:
        data["page_size"] = 10
    page = TaskResultService.query_page(data["task_id"], data["result_status"], data["create_from_time"],
                                      data["create_to_time"], data["page_num"], data["page_size"])
    page = "" if page is None else page.to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 删除
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/del', methods=['POST'])   # 数据集删除
def test_task_result_del():
    et_log.info("############test_task_result_del################")
    data = request.get_json()
    if not data or "task_result_id" not in data or data["task_result_id"] is None:
        et_log.error(f"test_task_result_del delete error, task_result_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_result_id is null").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TaskResultService.delete(data["task_result_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SYSTEM_INNER_ERROR.msg, "delete test plan error").to_json()

# 更新
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/update', methods=['POST'])
def test_task_result_update():
    et_log.info("############test_task_result_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_result_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "task_result_id" not in data or data["task_result_id"] is None:
            et_log.error(f"test_task_result_update error, task_result_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_result_id is null").to_json()
        result_pk = TaskResultService.find_pk(data["task_result_id"])
        if result_pk is None:
            et_log.error(f"test_task_result_update error, task_result_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "task_result_id id is invalid", "").to_json()
        result_pk.subjective_metric_id = str(data["subjective_metric_id"]).strip() \
            if ("subjective_metric_id" in data and data["subjective_metric_id"] is not None
                and len(str(data["subjective_metric_id"])) > 0) else ""
        result_pk.objective_metric_id = str(data["objective_metric_id"]).strip() \
            if ("objective_metric_id" in data and data["objective_metric_id"] is not None
                and len(str(data["objective_metric_id"])) > 0) else ""
        result_pk.rate_final = data["rate_final"] if ("rate_final" in data and data["rate_final"] is not None) else None
        result_pk.rate_answer = data["rate_answer"] if ("rate_answer" in data and data["rate_answer"] is not None) else None
        result_pk.rate_category = data["rate_category"] if ("rate_category" in data and data["rate_category"] is not None) else None
        result_pk.rate_ad_final = data["rate_ad_final"] if ("rate_ad_final" in data and data["rate_ad_final"] is not None) else None
        result_pk.rate_ad_answer = data["rate_ad_answer"] if ("rate_ad_answer" in data and data["rate_ad_answer"] is not None) else None
        result_pk.rate_ad_category = data["rate_ad_category"] if ("rate_ad_category" in data and data["rate_ad_category"] is not None) else None
        result_pk.rate_recall = data["rate_recall"] if ("rate_recall" in data and data["rate_recall"] is not None) else None
        result_pk.satisfactory = data["satisfactory"] if ("satisfactory" in data and data["satisfactory"] is not None) else None
        result_pk.total_use_time = data["total_use_time"] if ("total_use_time" in data and data["total_use_time"] is not None) else None
        result_pk.avg_score = data["avg_score"] if ("avg_score" in data and data["avg_score"] is not None) else None
        result_pk.result_status = data["result_status"] if ("result_status" in data and data["result_status"] is not None) else None
        result_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        update_result = TaskResultService.update(result_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"test_task_result_update update test_task_result exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update exception").to_json()

# 更新任务结果相关指标
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/rebuild', methods=['POST'])
def test_task_result_rebuild():
    et_log.info("############test_task_result_rebuild################")
    data = request.get_json()
    if not data or "task_result_id" not in data or data["task_result_id"] is None:
        et_log.error(f"test_task_result_rebuild error, task_result_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_result_id is null").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    TaskResultService.rebuild_task_result(data["task_result_id"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()

# 根据选择的任务结果比较任务明细
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/compare-list', methods=['POST'])
def compare_list():
    et_log.info("############compare_list################")
    data = request.get_json()
    if not data or "result_ids" not in data or data["result_ids"] is None:
        et_log.error(f"test_task_result compare error, result_ids is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "result_ids is null").to_json()
    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = TaskResultService.compare_page(data["result_ids"], page_num, page_size)
    page = "" if page is None else page.to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 根据任务id比较最近的5次任务结果
@test_task_result.route(f'/{URL_PREFIX}/test-task-result/compare', methods=['POST'])
def compare():
    et_log.info("############compare################")
    data = request.get_json()
    if not data or "task_id" not in data or data["task_id"] is None:
        et_log.error(f"test_task_result compare error, task_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id is null").to_json()
    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = TaskResultService.compare(data["task_id"], page_num, page_size)
    page = "" if page is None else page.to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()
