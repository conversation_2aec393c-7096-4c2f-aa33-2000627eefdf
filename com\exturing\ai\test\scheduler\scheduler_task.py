from datetime import time, datetime

from com.exturing.ai.test.comm.comm_constant import TASK_CYCLE_WEEK, TASK_CYCLE_MONTH, TASK_CYCLE_DAY, \
    TASK_CYCLE_CUSTOM, SCHEDULER_TRIGGER_CRON, SCHEDULER_TRIGGER_DATE
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.enums.week_enum import WeekEnum


class SchedulerTaskEntity:
    """
    定时任务参数对象
    """
    # {"id": "task1", "name": "任务1", "trigger": "interval", "seconds": 10}
    # {"cycle": "week", "cycle_value": 2, "time_value": "05:00:00", "custom_times": ["2025-03-26 06:00:00","2025-03-27 06:00:00"]}
    # scheduler.reschedule_job(task_id, trigger=trigger, seconds=seconds)
    # scheduler.add_job(task_function, trigger=trigger, seconds=seconds, args=[task_name], id=task_id)
    # scheduler.add_job(task_function, id=task_id, trigger=trigger, seconds=seconds, args=[task_name])
    # day scheduler.add_job(task_function, id=task_id, trigger="cron", hour=11, minute=10, seconds=seconds, args=[task_name])
    # week scheduler.add_job(task_function, id=task_id, trigger="cron", day_of_week="mon" hour=11, minute=10, seconds=seconds, args=[task_name])
    # month scheduler.add_job(task_function, id=task_id, trigger="cron", day=27 hour=11, minute=10, seconds=seconds, args=[task_name])
    # custom scheduler.add_job(task_function, id=task_id, trigger="date", run_date=datetime(2025, 3, 15, 12, 0, 0), args=[task_name])
    task_id: str # 任务id=test_task_id+cycle+cycle_value+time_value|test_task_id+cycle+custom_times
    trigger: str # 触发模式 interval | cron | date
    cycle_type: str # trigger对应的type
    cycle_week: str # 周 简英 周模式中的
    cycle_day: int # 月模式的 天
    cycle_time: time # 天、周、月模式中的时、分、秒值
    cycle_date: datetime # custom 的日期时间

    @classmethod
    def build_st_list(cls, test_task_id, execute_frequency:list):
        """
        构建
        :param test_task_id:
        :param execute_frequency:
        :return:
        """
        et_log.info(f"build_st_list test_task_id:{test_task_id} execute_frequency:{execute_frequency}")
        if not execute_frequency or len(execute_frequency) == 0:
            et_log.error(f"build_st_list execute_frequency is empty")
            return None
        st_list = []
        for data in execute_frequency:
            item = cls()
            item.task_id = test_task_id + "_" + data.cycle + "_"
            if TASK_CYCLE_DAY == data.cycle:# 天
                item.task_id += data.time_value
                item.trigger = SCHEDULER_TRIGGER_CRON
                item.cycle_type = data.cycle
                if not data.time_value or len(data.time_value) == 0:
                    continue
                item.cycle_time = datetime.strptime(data.time_value, "%H:%M:%S").time()
                st_list.append(item)
            elif TASK_CYCLE_WEEK == data.cycle:# 周
                week_en_value = WeekEnum.get_element_by_value(data.cycle_value).en_value
                item.task_id += week_en_value + "_" + data.time_value
                item.trigger = SCHEDULER_TRIGGER_CRON
                item.cycle_type = data.cycle
                item.cycle_week = week_en_value
                if not data.time_value or len(data.time_value) == 0:
                    continue
                item.cycle_time = datetime.strptime(data.time_value, "%H:%M:%S").time()
                st_list.append(item)
            elif TASK_CYCLE_MONTH == data.cycle:# 月
                item.task_id += str(data.cycle_value) + "_" + data.time_value
                item.trigger = SCHEDULER_TRIGGER_CRON
                item.cycle_type = data.cycle
                item.cycle_day = data.cycle_value
                if not data.time_value or len(data.time_value) == 0:
                    continue
                item.cycle_time = datetime.strptime(data.time_value, "%H:%M:%S").time()
                st_list.append(item)
            elif TASK_CYCLE_CUSTOM == data.cycle:# 自定义
                if not data.custom_times or len(data.custom_times) == 0:
                    continue
                for ct_time in data.custom_times:
                    if not ct_time or len(ct_time) == 0:
                        continue
                    ct_item = cls()
                    ct_item.task_id = item.task_id + ct_time
                    ct_item.trigger = SCHEDULER_TRIGGER_DATE
                    ct_item.cycle_type = data.cycle
                    ct_item.cycle_date = datetime.strptime(ct_time, "%Y-%m-%d %H:%M:%S")
                    st_list.append(ct_item)
            else:
                continue
        return st_list


