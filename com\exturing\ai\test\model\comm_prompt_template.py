import traceback
from typing import Optional

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import PROMPT_TMP_STATUS_DISABLE
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.base_pydantic_model import BasePydanticModel


class CommPromptTemplateModel(BasePydanticModel):
    """
    公用 提示词模板集合表对象
    """
    _doc = "comm_prompt_template"

    tmp_name: Optional[str] = None    # 提示词模板名称
    tmp_code: Optional[str] = None    # 提示词模板代码
    tmp_content: Optional[str] = None    # 提示词模板内容
    tmp_desc: Optional[str] = None    # 提示词模板描述
    tmp_status: Optional[int] = 0    # 提示词模板状态[1=使用中、0=待使用、-1=禁用]

    @classmethod
    def check_fk(cls, tmp_id:str,tmp_name, tmp_code) -> bool:
        """
        校验检查模板名称tmp_name和模板代码tmp_code的唯一性
        :param tmp_id: 模板id
        :param tmp_name: 模板名称
        :param tmp_code: 模板代码
        :return: Ture=唯一|False=名称或代码不唯一
        """
        et_log.info(f"comm_prompt_template check_fk tmp_id:{tmp_id} tmp_name:{tmp_name} tmp_code:{tmp_code}")
        try:
            condition = {"tmp_name": tmp_name}
            if tmp_id is not None and len(tmp_id) > 0:# 更新时排除掉自已id
                condition["_id"] = {"$ne":ObjectId(str(tmp_id))}
            count = cls.find_condition_count(condition)
            if count > 0:
                return False
            del condition["tmp_name"]
            condition["tmp_code"] = tmp_code
            count = cls.find_condition_count(condition)
            return False if count > 0 else True
        except Exception as e:
            et_log.error(f"check_name exception:{e},\n{traceback.print_exc()}")
            return False

    @classmethod
    def query_by_tmp_code(cls, tmp_code):
        """
        根据模板代码，获取提示词模板对象
        :param tmp_code: 模板代码
        :return: 模板对象
        """
        et_log.info(f"query_by_tmp_code tmp_code:{tmp_code}")
        try:
            condition = {"tmp_code": tmp_code, "tmp_status":{"$ne":PROMPT_TMP_STATUS_DISABLE}}
            tmp_list = cls.find_condition(condition)
            if tmp_list and len(tmp_list) > 0:
                return tmp_list[0]
            return None
        except Exception as e:
            et_log.error(f"check_name exception:{e},\n{traceback.print_exc()}")
            return None
