from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class DataTagModel(BaseDataModel):
    tag_name: str
    parent_id: Optional[str] = None
    type: Optional[int] = None
    level: Optional[int] = None
    status: Optional[int] = 1
    desc: Optional[str] = None
    sort: Optional[int] = 0
    
class DataTagQueryModel(BaseModel):
    tag_name: Optional[str] = None
    parent_id: Optional[str] = None
    type: Optional[int] = None
    level: Optional[int] = None
    status: Optional[int] = None    

    def get_query_condition(self):
        condition = {"is_del": 0}
        
        if self.tag_name:
            condition["tag_name"] = {"$regex": str(self.tag_name).strip(), "$options": "i"}
        if self.parent_id:
            condition["parent_id"] = self.parent_id
        if self.type:
            condition["type"] = self.type
        if self.level:
            condition["level"] = self.level
        if self.status:
            condition["status"] = self.status
        return condition

    