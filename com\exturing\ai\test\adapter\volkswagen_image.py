from uuid import uuid4
import time
import random
import json
import requests
from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID, CTX_OEM_CODES, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
import hashlib
import hmac
from typing import Optional
from hashlib import sha256
import base64
import re


class Volkswagen_image:
    """大众意图识别"""

    def __init__(self, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        model_params_str = config_info.model_params
        model_params_dict = json.loads(model_params_str)
        device_id = model_params_dict.get("Device_Id")
        vehicle_type = model_params_dict.get("Vehicle_Type")
        plugin_url = model_params_dict.get("plugin_url")
        self.device_id = device_id
        self.vehicle_type = vehicle_type
        self.plugin_url = plugin_url
        self.ak = config_info.model_ak
        self.sk = config_info.model_sk
        self.intent_url = config_info.model_url
        self.prompt = item.question
        self.task_id = task_id
        self.result_id = result_id
        self.conf_id = conf_id
        self.item = item
        self.do_user = do_user
        self.oem_codes = oem_codes



    def image_intent_reject(self):
        """
        大众适配=意图识别
        """
        query = self.prompt
        et_log.info(f"Volkswagen_context:intention task_id:{self.task_id} result_id:{self.result_id} conf_id:{self.conf_id}")
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        chat_id = uuid4().hex
        question_id = uuid4().hex
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
        et_log.info(f"query{self.prompt}")
        params = {
            "query": self.prompt,
            "llm_params": {
                "intent-custom-intent": {
                    "system_prompt": [
                        "角色\n你是汽车智能助手，你擅长将用户的指令转换为汽车功能的操作。\n\n任务\n我会给定一个函数列表，每个函数代表一种汽车控制功能，你需要结合对话上下文语境，理解用户当前的指令，在支持的汽车功能函数列表中选出一个函数，并输出函数名。\n\n输入格式\n输入为一段 user 与 assistant 的对话，其中 assistant 的回复会被选择性省略。\n\n输出要求\n你要输出汽车功能函数的名称\n当没有对应的功能函数可用时，则函数名为 '其他'\n\n支持的汽车功能函数列表\n1. 行程规划\n该函数用于帮助用户推荐指定地区的旅游路线和玩法规划。\n使用条件：\n用户必须明确请求助手进行行程规划，不能只表达自己的想法、诉求或行动，也不能只要求推荐地点\n用户明确要求规划的必须是长途旅游路线或者旅游计划\n正例输入示例：\nuser：我想去北京旅行，帮我做一个三天的行程规划\n输出：trip_planning\n负例输入示例列表：\nuser：我下个月要去云南祭拜祖先\nuser：有哪里适合露营吗，推荐一个\n\n2. 沿途感知 \n该函数用于帮助用户查询附近指定方位的 POI 信息。\n使用条件：\n用户只有提到以下类别才可以查询：河流、湖泊、山、风景名胜、交通地名（路、桥、隧道、立交桥、环岛）、楼宇、住宅、商场、医院、学校\n用户需要明确表达地点的相对位置信息，包括的范围有：当前位置、前后左右、东南西北\n正例输入示例列表：\nuser：右边的小区多少钱一平？\nuser：东边的河是什么河？\nuser：左前方是什么大楼？\n负例输入示例列表：\nuser：前面的车是什么车？\nuser：帮我推荐一个附近的景点\nuser：前面这个咖啡店还不错"],
                    "extra": {
                        "custom_intent_items": "[\"trip_planning\",\"沿途感知\"]"
                    }
                }
            },
            "history": [
                {
                    "q": "eu exercitation consectetur enim quis",
                    "a": "enim mollit sit adipisicing"
                }
            ],
            "car_info": {
                "vehicle_name": "掀背车",
                "sound_zone": "consequat culpa veniam ullamco deserunt",
                "audio_name": "荤熙成",
                "audio_producer": "adipisicing Lorem occaecat enim dolor",
                "current_location": "nulla elit est dolor aliqua",
                "nav_dest_name": "由伟",
                "nav_dest_address": "广西壮族自治区 宁原市 饶平县 仁巷59号 23室",
                "battery": 86,
                "tts_language": "voluptate in dolore ipsum"
            }
        }
        body = json.dumps(params, ensure_ascii=False)
        sign = self.generate_signature(self.sk, method, querystr, body)
        et_log.info(f"签名生成成功{sign}")
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
            "Content-Type": "application/json"
        }
        try:
            start_time = time.time()
            response = requests.post(
                f'{self.intent_url}?{querystr}',
                headers=headers,
                data=body.encode('utf-8'),
                timeout=20,
                stream=True  # 启用流式响应以处理SSE格式数据
            )
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(data)
                except Exception as e:
                    print(f"intent post response  get body_info error: {e}")
            if data:
                question_id, intent_log_id, intent_type = self.extract_fields_from_intent_response(data)
                et_log.info(f"获取结果{question_id}{intent_log_id}{intent_type}")
                if question_id and intent_log_id and  intent_type == "draw":
                    filled_template = self.fill_image_template(query,chat_id,question_id, intent_log_id, intent_type)
                    et_log.info(f"请求filled_template:{filled_template}")
                    time.sleep(4)
                    doubaoimage,end_time,first_res_time = self.plugin(filled_template)
                    if doubaoimage:
                        et_log.info(f"生图结果{doubaoimage}/n{end_time}/n{first_res_time}")
                        first_res_time = round(float(first_res_time-start_time),2)
                        res_time = round(float(end_time-start_time),2)
                    else:
                        doubaoimage = "N/A"
                        first_res_time = "N/A"
                        res_time = "N/A"
                    item_result = self.build_result_item(str(self.item._id), self.task_id, self.result_id, "",
                                                        "",
                                                        (self.item).expected_answer,doubaoimage ,
                                                        first_res_time, res_time, question_id,
                                                        self.do_user, self.oem_codes, self.conf_id)
                    item_result["result_answer"] = 0  # 回答结果
                    if doubaoimage:
                        item_result["result_answer"] = 1
                    item_result["result_final"] = item_result["result_answer"]  # 最终结果
                    et_log.info(f"Volkswagen_context_item_result:{item_result}")
                    # 记录每条数据的执行结果
                    item_result_id = TestItemResultService.insert_one(item_result)
                    et_log.info(f"ByteDanceAdapter:reject insert_one item_result_id:{item_result_id}")
                return item_result_id
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
        except Exception as e:
            et_log.error(f"API调用失败: {e}")
            return None, None, None


    def plugin(self, body):
        flag = False
        first_info_time = ""
        end_time = ""
        result_list = []
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}"
        if isinstance(body, dict):
            body_str = json.dumps(body, ensure_ascii=False)
        else:
            body_str = str(body) if body is not None else ""
        sign = self.generate_signature(self.sk,method, querystr, body_str)
        headers = {
            "X-Signature": f'{self.ak}:{sign}',
            "Device-Id": f"{self.device_id}",
            "Vehicle-Type": f"{self.vehicle_type}",
        }
        print("111112222")
        try:
            response = requests.post(
                f'{self.plugin_url}?{querystr}',
                headers=headers,
                data=body_str.encode('utf-8'),
                stream=True  # 启用流式响应以处理SSE格式数据
            )
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        print(decoded_line)
                        if decoded_line.startswith('data:'):
                            try:
                                event_data = json.loads(decoded_line[5:])
                                if 'observation' in event_data:
                                    observation = json.loads(event_data['observation'])
                                    if observation.get('response_type') == 'image':
                                        image_data = observation.get('image', {})
                                        urls = image_data.get('url', [])
                                        if urls and isinstance(urls, list) and len(urls) > 0:
                                            if not flag:
                                                first_res_time = time.time()
                                                flag = True
                                            doubaoimage = urls[0]
                                            if '.png' in str(doubaoimage):
                                                et_log.info(f"找到PNG图片URL: {doubaoimage}")
                                                end_time = time.time()
                            except Exception as e:
                                et_log.error(f"图片查找失败{e}")
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}")
            return doubaoimage,end_time,first_res_time
        except Exception as e:
            et_log.error(f"大众API生图失败: {e}")
            return None,None,None


    def generate_signature(self,sk: str, method: str, querystr: str, body: Optional[str]) -> str:
        """生成请求签名"""
        try:
            # 对查询参数按字典序排序
            a = querystr.split("&")
            a.sort()
            sortedquerystr = "&".join(a)

            # 构建待签名字符串
            strtosign = method + "\n" + sortedquerystr + "\n"
            if body is not None and len(body) > 0:
                m = hashlib.md5()
                m.update(body.encode("utf8"))
                strtosign += m.hexdigest() + "\n"

            # 计算HMAC-SHA256签名
            h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
            signature = base64.b64encode(h).decode()
            et_log.info("Generated signature successfully")
            return signature
        except Exception as e:
            error_msg = f"Signature generation failed: {e}"
            et_log.error(error_msg, exc_info=True)

    def find_tts(self,obj):
        if isinstance(obj, dict):
            if 'tts_text' in obj:
                return obj['tts_text']
            for v in obj.values():
                res = self.find_tts(v)
                if res is not None:
                    return res
        elif isinstance(obj, list):
            for item in obj:
                res = self.find_tts(item)
                if res is not None:
                    return res
        return None

    def extract_fields_from_intent_response(self, response_json):
        """
        从API响应JSON中提取特定字段
        返回格式: (question_id, top_level_log_id, intent_type)
        """
        try:
            if isinstance(response_json, str):
                data = json.loads(response_json)
            else:
                data = response_json

            # 初始化默认值
            question_id = None
            intent_log_id = None
            intent_type = None

            # 直接从data字典中安全获取值
            if data.get("data"):
                question_id = data["data"].get("question_id")
                intent_log_id = data["data"].get("debug_info", {}).get("log_id","N/A")

                if data["data"].get("intent"):
                    intent_type = data["data"]["intent"].get("intent_type","N/A")

            return question_id, intent_log_id, intent_type

        except json.JSONDecodeError:
            print("Error: 无效的JSON格式")
            return None, None, None
        except Exception as e:
            print(f"Error: 解析过程中发生意外错误 - {str(e)}")
            return None, None, None

    def fill_image_template(self,query,chat_id,question_id, intent_log_id, intent_type, template=None):

        """
        填充图片生成模板

        :param question_id: 问题ID (str)
        :param intent_log_id: 意图日志ID (str)
        :param intent_type: 意图类型 (str)
        :param template: 可选的自定义模板 (dict)
        :return: 填充后的模板字典 (dict)
        """
        image_template = template or {
            "query": None,
            "chat_id": None,
            "question_id": None,
            "intent_type": "draw",
            "intent_log_id": None,
            "history": []
        }
        try:
            if not all(isinstance(param, str) for param in [question_id, intent_log_id, intent_type]):
                raise ValueError("question_id, intent_log_id, intent_type 必须为字符串")
            if not (question_id and intent_log_id and intent_type == "draw"):
                raise ValueError("缺少必要字段或意图类型不匹配")
            filled_template = image_template.copy()
            filled_template.update({
                "query":query,
                "chat_id":chat_id,
                "question_id":question_id,
                "intent_log_id":intent_log_id,
                "intent_type":intent_type,
            })
            return filled_template
        except ValueError as ve:
            print(f"参数验证失败: {ve}")
            return None
        except Exception as e:
            print(f"填充模板时发生意外错误: {e}")
            return None




    def build_result_item(self,item_id, task_id, task_result_id, expected_category, actual_category, expected_answer, actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(f"ByteDanceAdapter:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
                    f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
                    f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
                    f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id":"", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}









