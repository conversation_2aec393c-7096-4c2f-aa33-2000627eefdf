import traceback
from flask import Blueprint, request
from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService

test_item_result=Blueprint('test_item_result',__name__)
test_task=Blueprint('test_task',__name__)

# 新增
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/create', methods=['POST'])
def test_item_result_create():
    et_log.info("############test_item_result_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_item_result_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_task_create error, param is null", "").to_json()
        if "task_id" not in data or len(str(data["task_id"]).strip()) == 0:
            et_log.error(f"test_item_result_create error, task_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_id in param is null ", "").to_json()
        if "data_set_item_id" not in data or len(str(data["data_set_item_id"]).strip()) == 0:
            et_log.error(f"test_item_result_create error, data_set_item_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "data_set_item_id in param is null ", "").to_json()
        if "task_result_id" not in data or len(str(data["task_result_id"]).strip()) == 0:
            et_log.error(f"test_item_result_create error, task_result_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id in param is null ", "").to_json()

        data["parent_id"] = str(data["parent_id"]).strip() if "parent_id" in data and len(str(data["parent_id"])) > 0 else ""
        data["task_id"] = str(data["task_id"]).strip()
        data["data_set_item_id"] = str(data["data_set_item_id"]).strip()
        data["task_result_id"] = str(data["task_result_id"]).strip()
        data["actual_task"] = str(data["actual_task"]).strip() if "actual_task" in data and len(str(data["actual_task"])) > 0 else ""
        data["actual_category"] = str(data["actual_category"]).strip() if "actual_category" in data and len(str(data["actual_category"])) > 0 else ""
        data["result_category"] = data["result_category"] if ("result_category" in data and data["result_category"] is not None) else 0
        data["actual_answer"] = str(data["actual_answer"]).strip() if "actual_answer" in data and len(str(data["actual_answer"])) > 0 else ""
        data["result_answer"] = data["result_answer"] if ("result_answer" in data and data["result_answer"] is not None) else 0
        data["answer_score"] = data["answer_score"] if ("answer_score" in data and data["answer_score"] is not None) else float(0.0000)
        data["qa_recall"] = data["qa_recall"] if ("qa_recall" in data and data["qa_recall"] is not None) else float(0.0000)
        # data["qa_keywords"] = str(data["qa_keywords"]).strip() if "qa_keywords" in data and len(str(data["qa_keywords"])) > 0 else ""
        data["qa_use_time"] = data["qa_use_time"] if ("qa_use_time" in data and data["qa_use_time"] is not None) else float(0.0000)
        data["result_final"] = data["result_final"] if ("result_final" in data and data["result_final"] is not None) else 0
        data["recall_id"] = str(data["recall_id"]).strip() if "recall_id" in data and len(str(data["recall_id"])) > 0 else ""
        data["remark"] = str(data["remark"]).strip() if "remark" in data and len(str(data["remark"])) > 0 else ""
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        data["re_url"] = str(data["re_url"]).strip() if "re_url" in data and len(str(data["re_url"])) > 0 else ""
        data["re_cmd"] = str(data["re_cmd"]).strip() if "re_cmd" in data and len(str(data["re_cmd"])) > 0 else ""
        data["re_error_id"] = str(data["re_error_id"]).strip() if "re_error_id" in data and len(str(data["re_error_id"])) > 0 else ""
        data["re_interval_time"] = data["re_interval_time"] if "re_interval_time" in data and data["re_interval_time"] is not None else 0

        data["is_websearch"] = data["is_websearch"] if "is_websearch" in data and data["is_websearch"] is not None else 0
        insert_result = TestItemResultService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.FAILURE.code, "insert error", "").to_json()
    except Exception as e:
        et_log.error(f"test_item_result_create create test_task exception")
        traceback.print_exc()
        return ApiResult(ResultCode.FAILURE.code, "insert exception", "").to_json()

# 查询
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/query', methods=['POST'])
def test_item_result_query():
    et_log.info("############test_item_result_query################")
    data = request.get_json()
    if not data or "task_result_id" not in data or len(str(data["task_result_id"]).strip()) == 0:
        et_log.error(f"test_item_result_query query error, task_result_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id is null", "").to_json()
    data["task_result_id"] = str(data["task_result_id"]).strip()
    if not data or "question" not in data or data["question"] is None or len(str(data["question"]).strip()) == 0:
        data["question"] = ""
    if not data or "result_final" not in data or data["result_final"] is None or data["result_final"] < 0:
        data["result_final"] = None
    if not data or "ad_result_final" not in data or data["ad_result_final"] is None or data["ad_result_final"] < 0:
        data["ad_result_final"] = None
    # if not data or "create_from_time" not in data or data["create_from_time"] is None or len(str(data["create_from_time"]).strip()) == 0:
    #     data["create_from_time"] = ""
    # if not data or "create_to_time" not in data or data["create_to_time"] is None or len(str(data["create_to_time"]).strip()) == 0:
    #     data["create_to_time"] = ""
    if not data or "model_id" not in data or data["model_id"] is None or len(str(data["model_id"]).strip()) == 0:
        data["model_id"] = ""
    if not data or "dimension_id" not in data or data["dimension_id"] is None or len(str(data["dimension_id"]).strip()) == 0:
        data["dimension_id"] = ""
    if not data or "page_num" not in data or data["page_num"] is None or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] is None or data["page_size"] < 1:
        data["page_size"] = 10
    if not data or "is_websearch" not in data or data["is_websearch"] is None:
        data["is_websearch"] = None
    if not data or "data_tags" not in data or data["data_tags"] is None:
        data["data_tags"] = []

    model_dimension = data.get("model_dimension", {})

    page = TestItemResultService.query_page(data["task_result_id"], data["question"], data["result_final"], data["ad_result_final"],
                                            data["model_id"], data["dimension_id"], model_dimension, data["is_websearch"],
                                            data["data_tags"], data["page_num"], data["page_size"])
    page = "" if page is None else page.to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 导出
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/export', methods=['POST'])
def test_item_result_query_all():
    et_log.info("############test-item-adapter_export################")
    data = request.get_json()
    if not data or "task_result_id" not in data or len(str(data["task_result_id"]).strip()) == 0:
        et_log.error(f"test_item_result_query query error, task_result_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id is null", "").to_json()
    
    export_keys = data.get("export_keys", [])
    
    excel_binary = TestItemResultService.export_data(data["task_result_id"], export_keys)

    if excel_binary is None:
        return ApiResult(ResultCode.FAILURE.code, "exported data is null", "").to_json()

    return excel_binary

# 删除
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/del', methods=['POST'])   # 数据集删除
def test_item_result_del():
    et_log.info("############test_item_result_del################")
    data = request.get_json()
    if not data or "result_item_id" not in data or data["result_item_id"] is None:
        et_log.error(f"test_task_result_del delete error, result_item_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "result_item_id is null").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestItemResultService.delete(data["result_item_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.FAILURE.code, "delete test set_item adapter error", "").to_json()

# 更新
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/update', methods=['POST'])
def test_item_result_update():
    et_log.info("############test_item_result_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_item_result_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "result_item_id" not in data or data["result_item_id"] is None:
            et_log.error(f"test_item_result_update error, result_item_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "result_item_id is null", "").to_json()
        result_pk = TestItemResultService.find_pk(data["result_item_id"])
        if result_pk is None:
            et_log.error(f"test_item_result_update error, result_item_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "result_item_id id is invalid", "").to_json()

        result_pk.actual_task = str(data["actual_task"]).strip() if "actual_task" in data and len(str(data["actual_task"])) > 0 else ""
        result_pk.actual_category = str(data["actual_category"]).strip() if "actual_category" in data and len(str(data["actual_category"])) > 0 else ""
        result_pk.result_category = data["result_category"] if ("result_category" in data and data["result_category"] is not None) else None
        result_pk.actual_answer = str(data["actual_answer"]).strip() if "actual_answer" in data and len(str(data["actual_answer"])) > 0 else ""
        result_pk.result_answer = data["result_answer"] if ("result_answer" in data and data["result_answer"] is not None) else None
        result_pk.answer_score = data["answer_score"] if ("answer_score" in data and data["answer_score"] is not None) else None
        result_pk.qa_recall = data["qa_recall"] if ("qa_recall" in data and data["qa_recall"] is not None) else None
        # result_pk.qa_keywords = str(data["qa_keywords"]).strip() if "qa_keywords" in data and len(str(data["qa_keywords"])) > 0 else ""
        result_pk.qa_use_time = data["qa_use_time"] if ("qa_use_time" in data and data["qa_use_time"] is not None) else None
        result_pk.result_final = data["result_final"] if ("result_final" in data and data["result_final"] is not None) else None
        result_pk.recall_id = str(data["recall_id"]).strip() if "recall_id" in data and len(str(data["recall_id"])) > 0 else ""
        result_pk.ad_result_category = data["ad_result_category"] if ("ad_result_category" in data and data["ad_result_category"] is not None) else None
        result_pk.ad_result_answer = data["ad_result_answer"] if ("ad_result_answer" in data and data["ad_result_answer"] is not None) else None
        result_pk.ad_result_task = data["ad_result_task"] if ("ad_result_task" in data and data["ad_result_task"] is not None) else None
        result_pk.ad_result_final = data["ad_result_final"] if ("ad_result_final" in data and data["ad_result_final"] is not None) else None
        result_pk.remark= str(data["remark"]).strip() if "remark" in data and len(str(data["remark"])) > 0 else ""

        result_pk.expected_task= str(data["expected_task"]).strip() if "expected_task" in data and len(str(data["expected_task"])) > 0 else ""
        result_pk.expected_category= str(data["expected_category"]).strip() if "expected_category" in data and len(str(data["expected_category"])) > 0 else ""
        result_pk.expected_answer= str(data["expected_answer"]).strip() if "expected_answer" in data and len(str(data["expected_answer"])) > 0 else ""
        result_pk.model_id = str(data["model_id"]).strip() if "model_id" in data and len(str(data["model_id"])) > 0 else ""
        result_pk.dimension_id = str(data["dimension_id"]).strip() if "dimension_id" in data and len(str(data["dimension_id"])) > 0 else ""
        result_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        result_pk.re_url = str(data["re_url"]).strip() if "re_url" in data and len(str(data["re_url"])) > 0 else ""
        result_pk.re_cmd = str(data["re_cmd"]).strip() if "re_cmd" in data and len(str(data["re_cmd"])) > 0 else ""
        result_pk.re_error_id = str(data["re_error_id"]).strip() if "re_error_id" in data and len(str(data["re_error_id"])) > 0 else ""
        result_pk.data_tags = str(data["data_tags"]).strip() if "data_tags" in data and len(str(data["data_tags"])) > 0 else ""
        result_pk.re_interval_time = data["re_interval_time"] if "re_interval_time" in data and data["re_interval_time"] is not None else 0
        result_pk.is_websearch = data["is_websearch"] if "is_websearch" in data and data["is_websearch"] is not None else 0
        result_pk.metric_calib = data["metric_calib"] if "metric_calib" in data and data["metric_calib"] is not None else []
        update_result = TestItemResultService.update(result_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.FAILURE.code, "update test_task_result error", "").to_json()
    except Exception as e:
        et_log.error(f"test_item_result_update update test_task_result exception")
        traceback.print_exc()
        return ApiResult(ResultCode.FAILURE.code, "update test_task_result exception", "").to_json()

# 批量更新
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/update-batch', methods=['POST'])
def test_item_result_update_batch():
    et_log.info("############test_item_result_update_batch################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_item_result_update_batch error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "result_item_ids" not in data or data["result_item_ids"] is None or len(data["result_item_ids"]) == 0:
            et_log.error(f"test_item_result_update_batch error, result_item_ids is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "result_item_ids is null", "").to_json()
        
        result_item_ids = data.get("result_item_ids")
        update_data = {
            'expected_category': data.get("expected_category"),
            'ad_result_category': data.get("ad_result_category"),
            'expected_task': data.get("expected_task"),
            'ad_result_task': data.get("ad_result_task"),
            'expected_answer': data.get("expected_answer"),
            'ad_result_answer': data.get("ad_result_answer"),
            'model_id': data.get("model_id"),
            'dimension_id': data.get("dimension_id"),
            'ad_result_final': data.get("ad_result_final"),
            'remark': data.get("remark"),
            "data_tags": data.get("data_tags"),
            "metric_calib" : data["metric_calib"] if "metric_calib" in data and data["metric_calib"] is not None else []
        }

        update_result = TestItemResultService.update_batch(result_item_ids, update_data)
        
        if update_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.FAILURE.code, "update test_task_result error", "").to_json()
    except Exception as e:
        et_log.error(f"test_item_result_update_batch update test_task_result exception")
        traceback.print_exc()
        return ApiResult(ResultCode.FAILURE.code, "update test_task_result exception", "").to_json()
    
# 获取所有结果子数据项的列表
@test_item_result.route(f'/{URL_PREFIX}/test-item-adapter/query-child', methods=['POST'])
def query_child():
    et_log.info("############query_child################")
    try:
        data = request.get_json()
        if not data or "item_pid" not in data or len(str(data["item_pid"])) == 0:
            et_log.error(f"query_child error, item_pid is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "item_pid is null", "").to_json()
        result_list = TestItemResultService.query_child_list(str(data["item_pid"]))
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result_list).to_json()
    except Exception as e:
        et_log.error(f"query_child query test_task_item_result child list exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, f"query_child exception", "").to_json()