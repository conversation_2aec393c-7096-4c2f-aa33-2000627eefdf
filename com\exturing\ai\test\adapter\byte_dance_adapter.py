from datetime import datetime
from uuid import uuid4

from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID, CTX_OEM_CODES, SYS_USER
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
from com.exturing.ai.test.tool.exturing_agent import HwVolcanoTool
from com.exturing.ai.test.tool.volcano_tool import VolcanoTool


class ByteDanceAdapter:
    """ 字节能力适配 """

    @classmethod
    def intention(cls, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        """
        字节适配=意图识别
        :param config_info: 通道对象
        :param item: 评测集-评测项
        :param task_id: 任务id
        :param result_id: 任务结果id
        :param conf_id: 通道id
        :param do_user: 执行人id
        :param oem_code: oem代码
        :return:评测集-评测项-结果id
        """
        et_log.info(f"ByteDanceAdapter:intention task_id:{task_id} result_id:{result_id} conf_id:{conf_id}")
        tool = VolcanoTool(item.question, config_info.model_url, config_info.model_ak, config_info.model_sk)
        origin_intent_type, final_intent_type, model_res_time, full_link_time, log_id, question_id = tool.intent_rec()
        et_log.info(f"ByteDanceAdapter:intention origin_intent_type:{origin_intent_type} final_intent_type:{final_intent_type} "
                    f"model_res_time:{model_res_time} full_link_time:{full_link_time} log_id:{log_id} question_id:{question_id}")
        item_result = cls.build_result_item(str(item._id), task_id, result_id, item.expected_category, final_intent_type,
                                            item.expected_answer, f"{origin_intent_type}|{final_intent_type}",
                                            model_res_time*0.001, full_link_time*0.001, log_id, question_id, do_user, oem_codes, conf_id)
        item_result["result_category"] = 0# 落域结果
        if item.expected_category and len(item.expected_category) > 0 and item.expected_category == final_intent_type:
            item_result["result_category"] = 1
        item_result["result_final"] = item_result["result_category"]# 最终结果
        et_log.info(f"ByteDanceAdapter:intention item_result:{item_result}")
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"ByteDanceAdapter:intention insert_one item_result_id:{item_result_id}")
        return item_result_id

    @classmethod
    def reject(cls, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        """
        字节适配=拒识
        :param config_info: 通道对象
        :param item: 评测集-评测项
        :param task_id: 任务id
        :param result_id: 任务结果id
        :param conf_id: 通道id
        :param do_user: 执行人id
        :param oem_code: oem代码
        :return:评测集-评测项-结果id
        """
        et_log.info(f"ByteDanceAdapter:reject task_id:{task_id} result_id:{result_id} conf_id:{conf_id}")
        tool = VolcanoTool(item.question, config_info.model_url, config_info.model_ak, config_info.model_sk)
        full_link_time, model_res_time, reject_result, log_id, question_id = tool.reject_rec()
        et_log.info(f"ByteDanceAdapter:reject full_link_time:{full_link_time} model_res_time:{model_res_time} "
                    f"reject_result:{reject_result} log_id:{log_id} question_id:{question_id}")
        item_result = cls.build_result_item(str(item._id), task_id, result_id, item.expected_category, "",
                                            item.expected_answer, str(reject_result), model_res_time*0.001,
                                            full_link_time*0.001, log_id, question_id, do_user, oem_codes, conf_id)
        item_result["result_answer"] = 0  # 回答结果
        if item.expected_answer and len(item.expected_answer) > 0 and item.expected_answer.upper() == str(reject_result).upper():
            item_result["result_answer"] = 1
        item_result["result_final"] = item_result["result_answer"]  # 最终结果
        et_log.info(f"ByteDanceAdapter:reject item_result:{item_result}")
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"ByteDanceAdapter:reject insert_one item_result_id:{item_result_id}")
        return item_result_id



    @classmethod
    def huawei_intention(cls, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        """
        字节适配=意图识别(对私-华为)
        :param config_info: 通道对象
        :param item: 评测集-评测项
        :param task_id: 任务id
        :param result_id: 任务结果id
        :param conf_id: 通道id
        :param do_user: 执行人id
        :param oem_code: oem代码
        :return:评测集-评测项-结果id
        """
        et_log.info(f"ByteDanceAdapter:huawei_intention task_id:{task_id} result_id:{result_id} conf_id:{conf_id}")
        tool = HwVolcanoTool(item.question, config_info.model_url, config_info.model_ak, config_info.model_sk)
        start_time = int(datetime.now().timestamp() * 1000)
        bd_result = tool.intent_rec()
        et_log.info(f"ByteDanceAdapter:huawei_intention bd_result:{bd_result}")
        if not bd_result or "success" != bd_result.get("msg", ""):
            et_log.error(f"ByteDanceAdapter:huawei_intention HwVolcanoTool.intent_rec response error")
            return None
        end_time = int(datetime.now().timestamp() * 1000)
        use_time = end_time - start_time
        intent_type = bd_result["data"]["intent"]["intent_type"]
        question_id = bd_result["data"]["question_id"]
        item_result = cls.build_result_item(str(item._id), task_id, result_id, item.expected_category, intent_type,
                                            item.expected_answer, intent_type,
                                            use_time*0.001, use_time*0.001, uuid4().hex, question_id, do_user, oem_codes, conf_id)
        item_result["result_category"] = 0# 落域结果
        if item.expected_category and len(item.expected_category) > 0 and item.expected_category.upper() == intent_type.upper():
            item_result["result_category"] = 1
        item_result["result_final"] = item_result["result_category"]# 最终结果
        et_log.info(f"ByteDanceAdapter:huawei_intention item_result:{item_result}")
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"ByteDanceAdapter:huawei_intention insert_one item_result_id:{item_result_id}")
        return item_result_id

    @classmethod
    def huawei_reject(cls, config_info: TestConfigInfoModel, item:EtDataSetItem, task_id, result_id, conf_id, do_user, oem_codes=""):
        """
        字节适配=拒识(对私-华为)
        :param config_info: 通道对象
        :param item: 评测集-评测项
        :param task_id: 任务id
        :param result_id: 任务结果id
        :param conf_id: 通道id
        :param do_user: 执行人id
        :param oem_code: oem代码
        :return:评测集-评测项-结果id
        """
        et_log.info(f"ByteDanceAdapter:huawei_reject task_id:{task_id} result_id:{result_id} conf_id:{conf_id}")
        tool = HwVolcanoTool(item.question, config_info.model_url, config_info.model_ak, config_info.model_sk)
        start_time = int(datetime.now().timestamp() * 1000)
        bd_result = tool.intent_rec()
        et_log.info(f"ByteDanceAdapter:huawei_reject bd_result:{bd_result}")
        if not bd_result or "success" != bd_result.get("msg", ""):
            et_log.error(f"ByteDanceAdapter:huawei_reject HwVolcanoTool.intent_rec response error")
            return None
        end_time = int(datetime.now().timestamp() * 1000)
        use_time = end_time - start_time
        reject = bd_result["data"]["reject"]
        question_id = bd_result["data"]["question_id"]
        item_result = cls.build_result_item(str(item._id), task_id, result_id, item.expected_category, "",
                                            item.expected_answer, str(reject),
                                            use_time*0.001, use_time*0.001, uuid4().hex, question_id, do_user, oem_codes, conf_id)
        item_result["result_answer"] = 0  # 回答结果
        if item.expected_answer and len(item.expected_answer) > 0 and item.expected_answer.upper() == str(reject).upper():
            item_result["result_answer"] = 1
        item_result["result_final"] = item_result["result_answer"]  # 最终结果
        et_log.info(f"ByteDanceAdapter:huawei_reject item_result:{item_result}")
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"ByteDanceAdapter:huawei_reject insert_one item_result_id:{item_result_id}")
        return item_result_id

    # 获取模型结果中的数据项结果json对象
    @classmethod
    def build_result_item(cls, item_id, task_id, task_result_id, expected_category, actual_category, expected_answer, actual_answer,
                          first_res_time, qa_use_time, log_id, question_id, do_user=SYS_USER, oem_codes="", conf_id=""):
        et_log.info(f"ByteDanceAdapter:build_result_item item_id:{item_id} task_id:{task_id} task_result_id:{task_result_id} "
                    f"conf_id:{conf_id} expected_category:{expected_category} actual_category:{actual_category} "
                    f"expected_answer:{expected_answer} actual_answer:{actual_answer} "
                    f"first_res_time:{first_res_time} qa_use_time:{qa_use_time} log_id:{log_id} question_id:{question_id}")
        if not CTX_OEM_CODES.get() and len(oem_codes) > 0:
            CTX_OEM_CODES.set(oem_codes)
        if not CTX_USER_ID.get():
            CTX_OEM_CODES.set(do_user)
        return {"parent_id":"", "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_use_time": qa_use_time, "recall_id": log_id,
                "remark": f"log_id:{log_id}|question_id:{question_id}", "first_res_time": first_res_time,
                "do_user": do_user, "re_interval_time": 0, "is_websearch": 0}