#!/bin/sh
#env="$1"
#echo "start ENV $env"
echo "start exturing_ai_test"
#source "/var/profile/.$env.bash.profile"
app_path=/var/exturing_ai_test
# 检查路径是否存在
#if [ ! -d "$path" ]; then
#  # 路径不存在，创建它
#  mkdir -p "$path"
#  echo "目录已创建: $path"
#else
#  # 路径已存在
#  echo "目录已存在: $path"
#fi
export LOG_PATH="/var/logs/ai_test"
export SENDER_MAIL="<EMAIL>"
export SENDER_PW="y1YnhLLbE4dqSYY4"
export SMTP="smtp.feishu.cn"
export MONGODB_URL="mongodb://ai-test-prd:X3C6TBZFFe^d&!<EMAIL>:3717,dds-bp1a3913b312a1143.mongodb.rds.aliyuncs.com:3717,dds-bp1a3913b312a1141.mongodb.rds.aliyuncs.com:3717/et-ai-eval?readPreference=secondary&replicaSet=mgset-85971949"
export MONGODB_DB="et-ai-eval"

export OSS_REGION="cn-hangzhou"
export OSS_BUCKET="extour-test-bucket"
export OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"
export OSS_ACCESS_KEY_ID="LTAI5t614c5JFmsTX4bcdbSx"
export OSS_ACCESS_KEY_SECRET="******************************"
export DASHSCOPE_API_KEY="sk-8e313fcceedb4abb8ad079608d5097fe"
exec nohup python3 $app_path/main_app.py > /var/logs/ai_test/nohup.out 2>&1


