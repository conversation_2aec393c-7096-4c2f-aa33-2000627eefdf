from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class TestMetricModel(BaseDataModel):
    metric_name: str
    type: Optional[int] = None
    classify: Optional[int] = None # 分类 1: 数据指标 2: 工具指标
    dimension: Optional[str] = None
    dimension_2: Optional[str] = None
    method: Optional[str] = None
    desc: Optional[str] = None
    
class TestMetricQueryModel(BaseModel):
    metric_name: Optional[str] = None
    classify: Optional[int] = None

    def get_query_condition(self):
        condition = {"is_del": 0}
        
        if self.metric_name:
            condition["metric_name"] = {"$regex": str(self.metric_name).strip(), "$options": "i"}
        if self.classify:
            condition["classify"] = self.classify
       
        return condition

    