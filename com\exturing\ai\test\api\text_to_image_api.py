from flask import Blueprint, request
import asyncio

from com.exturing.ai.test.agent.eval_txtimage_test_agent import ImageTextAP<PERSON>
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.tool.genimage_test import GenImageTest

text_to_image = Blueprint("text_to_image", __name__)


@text_to_image.route(f"/{URL_PREFIX}/text-to-image/testing", methods=["POST"])
def text_to_image_testing():
    data = request.get_json()

    prompt = data.get("prompt", "")
    image_paths = data.get("image_paths", [])
    text_data = [("texts", prompt)]

    image_text_api = ImageTextAPI(image_paths, text_data)
    result = image_text_api.send_request()

    if result is None:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, []).to_json()

    scores = result.get("scores", [])
    scores = [item[0] if len(item) > 0 else item for item in scores]

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, scores).to_json()


@text_to_image.route(f"/{URL_PREFIX}/text-to-image/auto", methods=["POST"])
def text_to_image_auto():
    data = request.get_json()

    prompt = data.get("prompt", "")
    models = data.get("models", [])

    result = asyncio.run(GenImageTest.main(models, prompt))

    if result is None:
        return ApiResult(ResultCode.SUCCESS, ResultCode.SUCCESS.msg, []).to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()
