import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.category_info import CategoryInfo

# 落域管理Service
class CategoryService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            category_name = post_data.get("category_name", "")
            category_code = post_data.get("category_code", "")
            category_type = post_data.get("category_type", None)
            pids = post_data.get("pids", "")
            memo = post_data.get("memo", "")
            bol_val = cls.check_category_name(category_name)
            if bol_val:
                et_log.error(f"insert_one category_name:{category_name} has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = CategoryInfo("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                             ctx_oem_codes, category_name, category_code, category_type, pids, memo).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"insert_one exception,\n{traceback.print_exc()}")
            return None

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = CategoryInfo.find_by_pk(_id)
        if data_item is not None:
            return CategoryInfo(**data_item)
        return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, category_name, category_code, category_type, create_from_time, create_to_time, page_num: int, page_size: int):
        et_log.info(f"query_page params category_name:{category_name} category_code:{category_code} "
                    f"category_type:{category_type} create_from_time:{create_from_time} create_to_time:{create_to_time} "
                    f"page_num:{page_num} page_size:{page_size}")
        try:
            condition = {}
            if category_name and len(str(category_name).strip()) > 0:
                condition["category_name"] = {"$regex": str(category_name).strip(), "$options": "i"}
            if category_code and len(str(category_code).strip()) > 0:
                condition["category_code"] = category_code
            if category_type:
                condition["category_type"] = category_type
            create_time_filter = {}
            if create_from_time and len(str(create_from_time).strip()) > 0:
                create_time_filter["$gte"] = str(create_from_time).strip()
            if create_to_time and len(str(create_to_time).strip()) > 0:
                create_time_filter["$lte"] = str(create_to_time).strip()
            if create_time_filter is not None and len(create_time_filter) > 0:
                condition["create_time"] = create_time_filter
            # 查询匹配的数量
            total = CategoryInfo.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            category_list = CategoryInfo.find_condition(condition, None, page.page_size, page.skip)
            if category_list is None or len(category_list) < 1:
                et_log.error(f"query_page not found data error")
                return None
            item_json_list = []
            for item in category_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"query_page exception,\n{traceback.print_exc()}")
            return None

    # 删除数据
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"delete by _id:{_id} do_user:{do_user}")
        try:
            del_item = CategoryInfo.find_by_pk(_id)
            if del_item is None:
                et_log.error(f"delete by _id error, _id not found data")
                return False

            # 删除id对应的数据记录
            CategoryInfo.delete_by_id(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"delete by _id exception,\n{traceback.print_exc()}")
            return False

    # 更新
    @classmethod
    def update(cls, post_data:CategoryInfo):
        et_log.info(f"update by post_data:{post_data}")
        try:
            # 更新-主键检查
            task:CategoryInfo = CategoryInfo.find_by_pk(post_data.id)
            if task is None:
                et_log.error(f"update error, _id not found data")
                return 0
            # 更新-任务名称检查
            condition = {"_id": {"$ne": ObjectId(post_data.id)}}
            if post_data and post_data.category_name and len(str(post_data.category_name).strip()) > 0:
                condition["category_name"] = str(post_data.category_name).strip()
            # 检查数据是否重复
            count = CategoryInfo.find_condition_count(condition)
            if count > 0:# 检查表明有重复的问题
                et_log.error(f"update error, repeat category_name")
                return 0
            # 更新请求的数据项数据
            return CategoryInfo.update_entity_json(post_data.to_json())
        except Exception as e:
            et_log.error(f"update exception,\n{traceback.print_exc()}")
            return 0

    # 检查落域名称是否重复
    @classmethod
    def check_category_name(cls, category_name):
        condition = {"category_name": str(category_name).strip()}
        total = CategoryInfo.find_condition_count(condition)
        if total > 0:
            return True
        else:
            return False

