from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.dto.params.auto_dataset_dto import AutoDatasetDto
from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class OriginDataItemModel(BaseDataModel):
    parent_id: Optional[str] = None # 上一轮id
    model_id: Optional[str] = None # 模块 id
    dimension_id: Optional[str] = None # 维度 id
    item_type: Optional[int] = 0 # 类型
    question: Optional[str] = None # 问题
    expected_answer: Optional[str] = None # 期望回答
    expected_task: Optional[str] = None # 期望任务类型
    expected_category: Optional[str] = None # 期望落域
    expected_url: Optional[int] = None # 期望url 1是 0否
    expected_cmd: Optional[int] = None # 期望cmd 1是 0否
    expected_error_id: Optional[str] = None # 期望error_id
    is_last: Optional[int] = 1 # 是否终轮
    qa_keywords: Optional[str] = None # 关键词
    data_tag_ids: Optional[str] = None # 数据标签
    exec_user_id: Optional[int] = None # 执行人id
    data_src: Optional[str] = None # 数据来源
    real_time: Optional[str] = None # 语料时间
    # effective_time: Optional[str] = None # 语料有效时间

    @classmethod
    def oem_codes_condition(cls):
        ctx_oem_codes = CTX_OEM_CODES.get()
        if not ctx_oem_codes or len(ctx_oem_codes) == 0:
            return None
        match_arr = ctx_oem_codes.split(",")
        if not match_arr or len(match_arr) == 0:
            return None
        return {
            "$regex": "|".join(match_arr), "$options": "i"  # 忽略大小写
        }

    @classmethod
    def random_origin_list(cls, param:AutoDatasetDto):
        """
        根据参数条件随机筛选原始语料List
        :param param: 随机参数条件
        :return: 随机到的原始语料List
        """
        et_log.info(f"random_origin_list param:{param.model_dump()}")
        if param.size is None or param.size < 1:
            et_log.error(f"random_origin_list error, size:{param.size} is invalid")
            return []

        sample = {"$sample": {"size": param.size}}

        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        # TODO 这里需要考虑是否多轮，如果是多轮可采用AutoDatasetDto增加标识来处理，这样可以通过在condition中增加is_last标识来判定
        match = {"oem_codes": match_oem_codes, "is_del": 0}
        if param.tags and len(param.tags) > 0:
            match_tag_arr = param.tags.split(",")
            if match_tag_arr and len(match_tag_arr) > 0:
                match["data_tag_ids"] = {"$regex": "|".join(match_tag_arr), "$options": "i"}
        if param.model_id and len(param.model_id) > 0:
            match["model_id"] = str(param.model_id)
        if param.dimension_id and len(param.dimension_id) > 0:
            match["dimension_id"] = str(param.dimension_id)

        if not match:
            et_log.error(f"random_origin_list error, match:{match} is empty")
            return []

        pipeline = [{"$match":match}, sample]
        # 执行聚合操作
        result = MongoDBUtil.aggregate("origin_data_item", pipeline)
        # return [OriginDataItemModel(**doc) for doc in result]
        origin_items = []
        for doc in result:
            item = OriginDataItemModel(**doc)
            item._id = str(doc.get("_id", ""))
            origin_items.append(item)
        return origin_items

class OriginDataItemQueryModel(BaseModel):
    model_id: Optional[str] = None  
    dimension_id: Optional[str] = None
    model_dimension: Optional[dict] = None
    data_tag_ids: Optional[list[str]] = None
    question: Optional[str] = None
    is_last: Optional[int] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    exec_user_id: Optional[int] = None
    create_by: Optional[int] = None
    real_time_start: Optional[str] = None
    real_time_end: Optional[str] = None
    expected_category: Optional[str] = None


    def get_query_condition(self):
        condition = {"is_del": 0, "parent_id": {"$in": [None, ""]}}
        
        if self.model_id:
            condition["model_id"] = self.model_id
        if self.dimension_id:
            condition["dimension_id"] = self.dimension_id
        if self.exec_user_id:
            condition["exec_user_id"] = self.exec_user_id
        if self.create_by:
            condition["create_by"] = self.create_by

        if self.model_dimension:
            model_ids = self.model_dimension.get("model_ids", [])
            dimension_ids = self.model_dimension.get("dimension_ids", [])

            if model_ids and dimension_ids:
                condition["$or"] = [{"model_id": {"$in": model_ids}}, {"dimension_id": {"$in": dimension_ids}}]
            elif model_ids:
                condition["model_id"] = {"$in": model_ids}
            elif dimension_ids:
                condition["dimension_id"] = {"$in": dimension_ids}

        if self.data_tag_ids:
            regex_conditions = [{"data_tag_ids": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in self.data_tag_ids]
            condition["$or"] = regex_conditions
   
        if self.question:
            condition["question"] = {"$regex": str(self.question).strip(), "$options": "i"}
        if self.is_last is not None:
            condition["is_last"] = self.is_last
        if self.start_time and self.end_time:
            condition["create_time"] = {"$gte": self.start_time, "$lte": self.end_time}
        if self.real_time_start and self.real_time_end:
            condition["real_time"] = {"$gte": self.real_time_start, "$lte": self.real_time_end}
        if self.expected_category:
            condition["expected_category"] = self.expected_category
            
        return condition


    