from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class BusinessModel(BaseDataModel):
    type: Optional[str] = None  # 业务类别
    module: Optional[str] = None  # 业务模块
    customer: Optional[str] = None  # 客户
    project: Optional[str] = None  # 项目
    code: Optional[str] = None  # 项目编号
    role: Optional[str] = None  # 角色
    owner: Optional[str] = None  # 负责人
    scope: Optional[str] = None  # 业务范围
    status: Optional[str] = None  # 状态
    detail: Optional[str] = None  # 业务详情
    nre_estimate: Optional[float] = None  # NRE预估金额
    lic_estimate: Optional[float] = None  # License预估金额
    quantity_estimate: Optional[float] = None  # 预估数量
    nre_quotation: Optional[float] = None  # NRE报价金额
    lic_quotation: Optional[float] = None  # License报价金额
    nre_contract: Optional[float] = None  # NRE合同金额
    lic_contract: Optional[float] = None  # License合同金额
    quantity_contract: Optional[float] = None  # 合同数量
    complete_precent: Optional[float] = None  # 年度合同完成度
    target_settlement: Optional[float] = None  # 目标结算金额
    actual_settlement: Optional[float] = None  # 实际结算金额


class BusinessQueryModel(BaseModel):
    type: Optional[str] = None
    module: Optional[str] = None
    customer: Optional[str] = None
    project: Optional[str] = None
    owner: Optional[str] = None
    status: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}

        if self.type:
            condition["type"] = self.type
        if self.module:
            condition["module"] = self.module
        if self.customer:
            condition["customer"] = self.customer
        if self.project:
            condition["project"] = self.project
        if self.owner:
            condition["owner"] = self.owner
        if self.status:
            condition["status"] = self.status

        return condition
