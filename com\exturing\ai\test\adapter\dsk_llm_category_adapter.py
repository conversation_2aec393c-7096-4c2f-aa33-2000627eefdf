import copy
import json
import traceback
from datetime import datetime

import requests

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


# adapter_code=dsk-llm-category 中枢分类评估服务
class DskLlmCategoryAdapter:

    # 单条记录运行
    @classmethod
    def run_item(cls, config_info:TestConfigInfoModel, item:EtDataSetItem, record_id, session_id, parent_id, task_id, task_result_id, do_user):
        et_log.info(f"run_item params config_info:{config_info} item:{item} record_id:{record_id} session_id:{session_id} "
                    f"parent_id:{parent_id} task_id:{task_id} task_result_id:{task_result_id}")
        try:
            message = copy.deepcopy(json.loads(config_info.env_script))
            message['request']['inputs'][0]['input'] = item.question
            message['request']['recordId'] = record_id
            message['session']['sessionId'] = session_id
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            payload = json.dumps(message, ensure_ascii=False).encode('utf-8')
            uri = config_info.env_uri
            if config_info.branch_name and len(config_info.branch_name) > 0:
                uri = uri.format(alias=config_info.branch_name)
            if config_info.pid_val and len(config_info.pid_val) > 0:
                uri = uri.format(productId=config_info.pid_val)
            if config_info.device_id and len(config_info.device_id) > 0:
                uri = uri.format(deviceId=config_info.device_id)
            if config_info.user_id and len(config_info.user_id) > 0:
                uri = uri.format(userId=config_info.user_id)
            if config_info.version and len(config_info.version) > 0:
                uri = uri.format(version=config_info.version)
            start_now = datetime.now()
            response = requests.post(uri, headers=headers, data=payload)
            end_now = datetime.now()
            diff_dt = end_now-start_now
            use_time = diff_dt.seconds + diff_dt.microseconds*0.000001
            response_data = response.json()
            et_log.info(f"run_item uri:{uri} "
                         f"data:{payload} "
                         f"response_data:{response_data}")
            if not response_data or 200 != response_data["code"] or "success" != response_data["codeMsg"]:
                et_log.error(f"run_item response_data is error")
                return None
            item_result = cls.build_result_item(response_data, parent_id, item.id, task_id, task_result_id,
                                                item.expected_category, item.expected_task, use_time, do_user)
            # 记录每条数据的执行结果
            item_result_id = TestItemResultService.insert_one(item_result)
            et_log.info(f"run_item insert_one item_result_id:{item_result_id}")
            return item_result_id
        except Exception as e:
            et_log.error(f"run_item item_id:{item.id} exception,\n{traceback.print_exc()}")
            return None

    # 获取模型结果中的数据项结果json对象
    @classmethod
    def build_result_item(cls, response_data, parent_id, item_id, task_id, task_result_id, expected_category,
                          expected_task, use_time, do_user):
        record_id = response_data["recordId"]
        session_id = response_data["sessionId"]
        # actual_category = response_data["data"]["category"][0]["name"]
        actual_category = response_data.get("data", {}).get("category", [{}])[0].get("name", "")
        result_category = 0
        if expected_category and len(expected_category) > 0:
            result_category = 1 if actual_category == expected_category else 0
        elif expected_task and len(expected_task) > 0:
            result_category = 1 if actual_category == expected_task else 0

        test_task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
        return {"parent_id":parent_id, "data_set_item_id": str(item_id), "task_id": task_id,
                "task_result_id": str(task_result_id), "actual_task": "", "actual_category": actual_category,
                "result_category": result_category, "actual_answer": "", "result_answer": 0, "answer_score": 0,
                "qa_recall": 0, "qa_keywords": "", "qa_use_time": use_time, "result_final": result_category,
                "recall_id": record_id, "remark": f"session_id:{session_id}|record_id:{record_id}",
                "do_user": do_user, "re_url": "", "re_cmd": "","eval_config_id": test_task.config_id,
                "re_error_id": "", "re_interval_time": 0, "is_websearch": 0}