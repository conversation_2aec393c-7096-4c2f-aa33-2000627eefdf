from pydantic import BaseModel
from typing import Optional, Dict


class ChatBoxResultDto(BaseModel):
    """
    闲聊ChatBox tool层返回的对象结构
    """
    chat_id: Optional[str] = ""     # 会话Id
    full_answer: Optional[str] = "" # 全部回答内容
    first_time: Optional[int] = 0   # 首次响应耗时
    full_time: Optional[int] = 0    # 全部回答耗时
    answer_score: Optional[int] = 0 # 回答评分
    answer_score_reason: Optional[str] = "" # 回答评分原因
    chunk_answer: Optional[Dict] = None # 回答的每次chunk内容+时间



