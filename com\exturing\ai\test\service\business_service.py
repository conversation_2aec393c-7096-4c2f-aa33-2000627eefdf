# service.py
from com.exturing.ai.test.comm.comm_util import float_format, str_format
from com.exturing.ai.test.model.business import BusinessModel, BusinessQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log
from werkzeug.datastructures import FileStorage
import pandas as pd

_doc = "business"


# 新增业务
def create(data: BusinessModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create business: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询业务
def query_page(
    page_num: int = 1, page_size: int = 10, query: BusinessQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 修改业务
def update(id: str, data: BusinessModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update business: {data_dict}")

    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除业务
def delete(id: str) -> bool:
    et_log.info(f"delete business by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 业务导入
def file_import(file: FileStorage) -> bool:
    df = pd.read_excel(file)
    records = df.to_dict(orient="records")
    business_list = []

    for record in records[::-1]:
        business = {}
        business["type"] = str_format("类别", record)
        business["module"] = str_format("模块", record)
        business["customer"] = str_format("客户", record)
        business["project"] = str_format("项目", record)
        business["code"] = str_format("项目编号", record)
        business["role"] = str_format("角色", record)
        business["owner"] = str_format("责任人", record)
        business["scope"] = str_format("产品范围", record)
        business["status"] = str_format("状态", record)
        business["detail"] = str_format("业务详情", record)
        business["nre_estimate"] = float_format("NRE预估金额", record)
        business["lic_estimate"] = float_format("LIC预估金额", record)
        business["quantity_estimate"] = float_format("预估台套数", record)
        business["nre_quotation"] = float_format("NRE报价金额", record)
        business["lic_quotation"] = float_format("LIC报价金额", record)
        business["nre_contract"] = float_format("NRE合同金额", record)
        business["lic_contract"] = float_format("LIC合同金额", record)
        business["quantity_contract"] = float_format("合同台套数", record)
        business["complete_precent"] = float_format("年度合同额完成度", record)
        business["target_settlement"] = float_format("结算目标", record)
        business["actual_settlement"] = float_format("实际结算", record)

        business_list.append(BusinessModel(**business).model_dump())

    inserted_ids = MongoDBUtil.insert_many(_doc, business_list)

    return inserted_ids
