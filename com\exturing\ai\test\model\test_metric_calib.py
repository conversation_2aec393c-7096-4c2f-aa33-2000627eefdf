import traceback
from typing import Optional

from bson import ObjectId

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.base_pydantic_model import BasePydanticModel


class TestMetricCalibModel(BasePydanticModel):
    """
    重构测试指标标定
    """
    _doc = "test_metric_calib"

    metric_name: str    # 指标名称
    metric_app: Optional[str] = None    # 指标应用场景
    metric_pid: Optional[str] = ""    # 父级指标id
    metric_type: Optional[int] = None   # 指标类型 1=判定|2=打分|3=公式|等
    metric_status: Optional[int] = 0    # 指标状态 （预留字段） 0=初始
    metric_desc: Optional[str] = None   # 指标描述
    metric_weight: Optional[int] = 0      # 指标值权重
    metric_formula: Optional[str] = None    # 公式（判定类型）统计总数|平均值|占比|与|或

    @classmethod
    def check_name(cls, metric_id, metric_name, metric_pid):
        """
        根据指标名称和父id，校验数据是否重复
        :param metric_id: 指标id
        :param metric_name: 指标名称
        :param metric_pid: 指标父id
        :return: 只有返回Ture时，代码验证通过
        """
        et_log.info(f"check_name metric_id:{metric_id} metric_name:{metric_name} metric_pid:{metric_pid}")
        try:
            if metric_name is None or len(metric_name) == 0:
                return False
            condition = {"metric_name": metric_name}
            if metric_id is not None and len(metric_id) > 0:# 更新时排除掉自已id
                condition["_id"] = {"$ne":ObjectId(str(metric_id))}
            if metric_pid is not None and len(metric_pid) > 0:
                condition["metric_pid"] = ObjectId(str(metric_pid))
            count = cls.find_condition_count(condition)
            return False if count > 0 else True
        except Exception as e:
            et_log.error(f"check_name exception:{e},\n{traceback.print_exc()}")
            return False
