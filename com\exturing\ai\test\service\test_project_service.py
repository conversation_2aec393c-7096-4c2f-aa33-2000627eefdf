import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.test_plan import TestPlan
from com.exturing.ai.test.model.test_project import TestProject
from com.exturing.ai.test.model.test_task import TestTask


# 测试 项目Service
class TestProjectService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one test_project post_data:{post_data}")
        try:
            # 插入当前请求数据
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            project_name = post_data["project_name"]
            project_code = post_data["project_code"]
            bol_val = cls.check_name_code(project_name, project_code)
            if bol_val:
                et_log.error(f"insert_one test_project error, project_name or project_code has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestProject("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                    ctx_oem_codes, post_data.get("project_pid", ""), project_name, project_code, post_data.get("oem_code", ""),
                                    post_data.get("ome_name", ""), post_data.get("vehicle_model", ""),
                                    post_data.get("project_desc", ""), post_data.get("is_last", 1)).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"insert_one test_project exception")
            traceback.print_exc()
            return None

    # 检查名称、（重复性检查）
    @classmethod
    def check_name_code(cls, project_name, project_code):
        try:
            condition = {"project_name": str(project_name).strip()}
            total = TestProject.find_condition_count(condition)
            if total > 0:
                return True
            condition = {"project_code": str(project_code).strip()}
            total = TestProject.find_condition_count(condition)
            if total > 0:
                return True
            return False
        except Exception as e:
            et_log.error(f"test_project check_name_code exception,\n{traceback.print_exc()}")
            return False

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        date_pk = TestProject.find_by_pk(_id)
        if date_pk is not None:
            return TestProject(**date_pk)
        return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, project_name, project_code, oem_code, vehicle_model, create_from_time, create_to_time, page_num: int, page_size: int):
        et_log.info(f"test_project query_page params project_name:{project_name} project_code:{project_code} "
                    f"oem_code:{oem_code} vehicle_model:{vehicle_model} create_to_time:{create_to_time} "
                    f"page_num:{page_num} page_size:{page_size}")
        try:
            condition = {}
            if project_name and len(str(project_name).strip()) > 0:
                condition["project_name"] = {"$regex": str(project_name).strip(), "$options": "i"}
            if project_code and len(str(project_code).strip()) > 0:
                condition["project_code"] = project_code
            if oem_code and len(str(oem_code).strip()) > 0:
                condition["oem_code"] = oem_code
            if vehicle_model and len(str(vehicle_model).strip()) > 0:
                condition["vehicle_model"] = vehicle_model
            create_time_filter = {}
            if create_from_time and len(str(create_from_time).strip()) > 0:
                create_time_filter["$gte"] = str(create_from_time).strip()
            if create_to_time and len(str(create_to_time).strip()) > 0:
                create_time_filter["$lte"] = str(create_to_time).strip()
            if create_time_filter is not None and len(create_time_filter) > 0:
                condition["create_time"] = create_time_filter
            # 查询匹配的数量
            total = TestProject.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            data_list = TestProject.find_condition(condition, None, page_size, page.skip)
            if data_list is None or len(data_list) < 1:
                et_log.error(f"test_project query_page not found data error")
                return None
            item_json_list = []
            for item in data_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_project query_page exception,\n{traceback.print_exc()}")
            return None

    # 删除
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_project delete by _id:{_id} do_user:{do_user}")
        try:
            del_data = TestProject.find_by_pk(_id)
            if del_data is None:
                et_log.error(f"test_project delete by _id error, _id not found data")
                return False
            # 删除id对应的数据
            TestProject.delete_by_id(_id, do_user)

            # 根据id检查，该数据项是否有子节点，若有则需更新=所有子节点都逻辑删除
            if 0 == del_data.get("is_last", 1):
                cls.delete_by_pid(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"test_project delete by _id exception,\n{traceback.print_exc()}")
            return False

    # 根据父节点，删除其对应的子节点项目
    @classmethod
    def delete_by_pid(cls, pid, do_user):
        et_log.info(f"test_project delete by pid:{pid} do_user:{do_user}")
        try:
            parents = TestProject.find_by_pid(pid)
            if parents is None or len(parents) == 0:
                et_log.error(f"test_project delete by pid:{pid} error, pid not found child data")
                return False
            # 删除id对应的数据,子项删除
            for parent_project in parents:
                if parent_project.pid and 0 == parent_project.is_last:# 子节点还有下级数据，删除下级
                    cls.delete(parent_project.id, do_user)
        except Exception as e:
            et_log.error(f"test_project delete by pid:{pid} exception,\n{traceback.print_exc()}")
            return False

    # 更新数据
    @classmethod
    def update(cls, post_data:TestProject):
        et_log.info(f"test_project update by post_data:{post_data}")
        try:
            # 更新-主键检查
            data:TestProject = cls.find_pk(post_data.id)
            if data is None:
                et_log.error(f"update test_project error, _id not found data")
                return 0
            # 更新-名称和代码检查
            if cls.check_update_project_repeat(post_data.project_name, post_data.project_code, data.id):
                # 更新请求的数据项数据
                return TestProject.update_entity_json(post_data.to_json())
            else:
                return 0
        except Exception as e:
            et_log.error(f"test_project update exception,\n{traceback.print_exc()}")
            return 0

    # 更新环境时，检查名称和代码的重复性
    @classmethod
    def check_update_project_repeat(cls, project_name, project_code, up_id):
        et_log.info(f"test_project check_update_project_repeat params project_name:{project_name} project_code:{project_code} "
                    f"up_id:{up_id}")
        try:
            condition = {"_id": {"$ne": ObjectId(up_id)}}
            if project_name and len(str(project_name).strip()) > 0:
                condition["project_name"] = str(project_name).strip()
                count = TestProject.find_condition_count(condition)
                if count > 0:
                    return False
            if project_code and len(str(project_code).strip()) > 0:
                condition["project_code"] = str(project_code).strip()
                count = TestProject.find_condition_count(condition)
                if count > 0:
                    return False
            return True
        except Exception as e:
            et_log.error(f"check_update_project_repeat test_project exception,\n{traceback.print_exc()}")
            return False



# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))

