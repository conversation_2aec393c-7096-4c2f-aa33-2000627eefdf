import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 测试任务
class TestTask(BaseModel):

    _doc = "test_task"

    plan_id: str # 测试计划id
    env_id: str # 环境id
    config_id: str # 测试通道id
    branch_id: str # 车型分支id(对应字典表id)
    data_set_id: str  # 测试的数据集id
    task_name: str  # 任务名称
    task_type: int  # 测试类型 1=指标 2=性能 3=安全 ...
    task_status: int  # 任务状态    0=待执行 1=执行中 99=执行成功 -1=执行失败
    data_set_type: str # 数据集类型 select=选择 build=构建
    auto_run: bool # 自动执行标识 true=自动定时执行 false=手动触发执行
    data_set_builds: [] # 自动构建参数数组
    execute_frequency: [] # 执行频率数组

    eval_type: int  # 评测类型 默认0=单通道 1=多通道
    eval_multi: str # 评测模型，多通道模型id之间“,”分割

    answer_pt_code: str # 问题回答提示词模板代码
    eval_pt_code: str   # 问题回答评分提示词模板代码

    def __init__(
        self,
        _id,
        create_time,
        create_by,
        update_time,
        update_by,
        is_del,
        oem_codes="",
        plan_id="",
        env_id="",
        data_set_id="",
        task_name="",
        task_type=1,
        task_status=0,
        branch_id="",
        execute_frequency=None,
        data_set_builds=None,
        auto_run=False,
        data_set_type="",
        config_id="",
        eval_type=0,
        eval_multi="",
        answer_pt_code:str = "",
        eval_pt_code=""
    ):

        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.plan_id = plan_id
        self.env_id = env_id
        self.config_id = config_id
        self.data_set_id = data_set_id
        self.task_name = task_name
        self.task_type = task_type
        self.task_status = task_status
        self.branch_id = branch_id
        self.oem_codes = oem_codes
        self.execute_frequency = execute_frequency
        self.data_set_type = data_set_type
        self.data_set_builds = data_set_builds
        self.auto_run = auto_run

        self.eval_type = eval_type
        self.eval_multi = eval_multi

        self.answer_pt_code = answer_pt_code
        self.eval_pt_code = eval_pt_code

    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = str(self._id)
        base_json["plan_id"] = ObjectId(self.plan_id) if self.plan_id and len(str(self.plan_id)) > 0 else ""
        base_json["env_id"] = ObjectId(self.env_id) if self.env_id and len(str(self.env_id)) > 0 else ""
        base_json["config_id"] = ObjectId(self.config_id) if self.config_id and len(str(self.config_id)) > 0 else ""
        base_json["data_set_id"] = ObjectId(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        base_json["task_name"] = self.task_name
        base_json["task_type"] = self.task_type
        base_json["task_status"] = self.task_status
        base_json["execute_frequency"] = self.execute_frequency
        base_json["data_set_builds"] = self.data_set_builds
        base_json["auto_run"] = self.auto_run
        base_json["data_set_type"] = self.data_set_type
        base_json["branch_id"] = ObjectId(self.branch_id) if self.branch_id and len(str(self.branch_id)) > 0 else ""

        base_json["eval_type"] = self.eval_type
        base_json["eval_multi"] = self.eval_multi

        base_json["answer_pt_code"] = self.answer_pt_code
        base_json["eval_pt_code"] = self.eval_pt_code
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["plan_id"] = str(self.plan_id) if self.plan_id and len(str(self.plan_id)) > 0 else ""
        data_json["env_id"] = str(self.env_id) if self.env_id and len(str(self.env_id)) > 0 else ""
        data_json["config_id"] = str(self.config_id) if self.config_id and len(str(self.config_id)) > 0 else ""
        data_json["data_set_id"] = str(self.data_set_id) if self.data_set_id and len(str(self.data_set_id)) > 0 else ""
        data_json["branch_id"] = str(self.branch_id) if self.branch_id and len(str(self.branch_id)) > 0 else ""
        return data_json

    # 根据计划id，删除其下所有非执行中的数据
    @classmethod
    def del_by_pid(cls, pid, do_user):
        condition = {"plan_id": ObjectId(pid), "task_status": {"$in": [0, 99, -1]}}
        # del_pro = {"is_del": 1, "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        return MongoDBUtil.delete_by_many(cls._doc, condition, do_user)

    @classmethod
    def query_new_id_by_dataset(cls, plan_id):
        """
        根据评测计划id，按评测集分组获取最新的评测任务
        :param plan_id: 评测计划id
        :return: 测集分组后，每组最新的任务id str、评测集id str;[{"data_set_id":"评测集id", "task_id":"最新的任务id"}]
        """
        et_log.info(f"query_new_id_by_dataset plan_id:{plan_id}")
        try:
            pipeline = [
                {"$match": {"plan_id": ObjectId(plan_id), "is_del":0}},
                {"$sort": {"create_time": -1}},
                {"$group": {
                    "_id": "$data_set_id",
                    "new_id": {"$first": "$_id" },
                    "latest_time": {"$first": "$create_time" },
                    "data_set_id": {"$first": "$data_set_id"}  # 获取data_set_id（与_id相同，但转为普通字段）
                }
                }
            ]
            et_log.info(f"query_new_id_by_dataset pipeline:{pipeline}")
            result = []
            group_data = list(cls.aggregate(pipeline))
            if group_data:
                for data in group_data:
                    item = {"data_set_id":str(data.get("data_set_id", "")), "task_id":str(data.get("new_id", ""))}
                    result.append(item)
            return result
        except Exception as e:
            et_log.error(f"query_new_id_by_dataset exception:{e}\n{traceback.format_exc()}")
            return []
