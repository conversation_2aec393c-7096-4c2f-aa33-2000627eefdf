from com.exturing.ai.test.model.base_model import BaseModel


class CategoryInfo(BaseModel):
    """
    落域信息对象
    """
    _doc = "category_info"
    category_name: str  # 名称
    category_code: str  # 代码
    category_type: int  # 1=大模型、2=技能型
    pids: str # 车型id组合，以“,”分割
    memo: str  # 备忘

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes, category_name="", category_code="",
                 category_type=None, pids="", memo=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.category_name = category_name
        self.category_code = category_code
        self.category_type = category_type
        self.pids = pids
        self.memo = memo

    def to_json(self):
        base_json = super().to_json()
        base_json["category_name"] = self.category_name
        base_json["category_code"] = self.category_code
        base_json["category_type"] = self.category_type
        base_json["pids"] = self.pids
        base_json["memo"] = self.memo
        return base_json

    def to_json_str(self):
        json_data = self.to_json()
        json_data["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        return json_data