import asyncio
import concurrent.futures
import traceback
from datetime import datetime
from uuid import uuid4

from bson import ObjectId

from com.exturing.ai.test.adapter.agent_llm_hub_adapter import Agent<PERSON><PERSON>HubAdapter
from com.exturing.ai.test.adapter.byte_dance_adapter import ByteDanceAdapter
from com.exturing.ai.test.comm.comm_constant import ENV_ADAPTER_CODE_CATEGORY, ENV_ADAPTER_CODE_AUTOTEST, \
    ENV_ADAPTER_CODE_HUB, ENV_ADAPTER_CODE_CONTEXT, CTX_OEM_CODES, CTX_USER_ID, ENV_mind2, ENV_mind2agent, \
    TASK_DATASET_TYPE_BUILD, ALL_OEM_CODES, SYS_USER, DIC_CODE_MAX_RUN_ITEM, DIC_GROUP_SYS_CONF, EVAL_TYPE_MULTI, \
    EVAL_TYPE_DEF, ENV_ADAPTER_CODE_QIANFAN3_5, ENV_ADAPTER_CODE_QIANFAN4_0, ENV_ADAPTER_CODE_QIANWEN2_5, \
    ENV_ADAPTER_CODE_DOUBAO, ENV_ADAPTER_CODE_DSR1, ENV_ADAPTER_CODE_DSV3, ENV_ADAPTER_CODE_QIANWEN3_32B, \
    ENV_ADAPTER_CODE_QIANWENA3B, ENV_ADAPTER_CODE_QIANWENQwQ_32B, ENV_ADAPTER_CODE_BD_INTENT, \
    ENV_ADAPTER_CODE_BD_REJECT, ENV_ADAPTER_CODE_BD4HW_REJECT, ENV_ADAPTER_CODE_BD4HW_INTENT, \
    ENV_ADAPTER_CODE_HUAWEI_CONTEXT,ENV_ADAPTER_CODE_VOLKSWAGEN_CONTEXT,ENV_ADAPTER_CODE_VOLKSWAGEN_IMAGE
from com.exturing.ai.test.comm.comm_doc_cont import TEST_TASK_RESULT_STATUS_INIT
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.dto.task_execution_frequency_dto import TaskExecutionFrequencyDto
from com.exturing.ai.test.dto.test_task_dto import TestTaskDto, TestTaskQueryDto
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.dic_info import DicInfo
from com.exturing.ai.test.model.test_config import TestConfigInfoModel, TestConfigInfoReModel
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_ai_result import TestTaskAiResultModel
from com.exturing.ai.test.model.test_task_result import TestTaskResult
from com.exturing.ai.test.adapter.agent_llm_autotest_adapter import AgentLlmTestAdapter
from com.exturing.ai.test.adapter.agent_context import Semantic_context_service
from com.exturing.ai.test.scheduler.scheduler_main import SchedulerMain
from com.exturing.ai.test.scheduler.scheduler_task import SchedulerTaskEntity
from com.exturing.ai.test.service.data_item_service import DataItemService
from com.exturing.ai.test.adapter.dsk_llm_category_adapter import DskLlmCategoryAdapter
from com.exturing.ai.test.service.data_set_service import DataSetService
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.service.test_config_service import find_config_info, find_config_info_re
from com.exturing.ai.test.service.test_env_service import TestEnvService
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
from com.exturing.ai.test.service.test_task_result_service import TaskResultService
from com.exturing.ai.test.agent.mind2_agent import AgentllmmindV2
from com.exturing.ai.test.agent.ytmind2_model import AgentllmytmindV2
from com.exturing.ai.test.tool.deepseek_R1 import ds_r1_chat_speak
from com.exturing.ai.test.tool.deepseek_v3 import ds_v3_chat_speak
from com.exturing.ai.test.tool.doubao_model import EvalDoubaoAgent
from com.exturing.ai.test.tool.qianfan3_5 import QianFan_MODEL_3
from com.exturing.ai.test.tool.qianfan4 import QianFan_MODEL_4
from com.exturing.ai.test.tool.qianwen2_5 import qw_chat_speak
from com.exturing.ai.test.adapter.huawei_agent_context import Huawei_Context_service
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.adapter.volkswagen_context import Volkswagen_context
from com.exturing.ai.test.adapter.volkswagen_image import Volkswagen_image

# 测试任务Service
class TestTaskService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"test_task insert_one post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            plan_id = post_data["plan_id"]
            env_id = post_data["env_id"]
            config_id = post_data["config_id"]
            data_set_id = post_data["data_set_id"]
            task_name = post_data["task_name"]
            task_type = post_data["task_type"]
            eval_type = post_data.get("eval_type", 0)
            eval_multi = post_data.get("eval_multi", "")
            if EVAL_TYPE_MULTI == eval_type and len(eval_multi) == 0:
                et_log.error(f"test_task insert_one plan_id:{plan_id} task_name:{task_name} eval_multi is empty")
                return None

            # task_status = post_data["task_status"]
            bol_val = cls.check_task_name(plan_id, task_name)
            if bol_val:
                et_log.error(f"test_task insert_one plan_id:{plan_id} task_name:{task_name} has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()

            answer_pt_code = post_data.get("answer_pt_code", "")
            eval_pt_code = post_data.get("eval_pt_code", "")
            insert_id = TestTask("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                 ctx_oem_codes, plan_id, env_id, data_set_id, task_name, task_type, 0, "",
                                 config_id, eval_type, eval_multi, answer_pt_code, eval_pt_code).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"test_task insert_one exception")
            traceback.print_exc()
            return None

    @classmethod
    def insert_one_dto(cls, param_dto: TestTaskDto):
        et_log.info(f"test_task insert_one_dto paramDto:{param_dto}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            plan_id = param_dto.plan_id
            data_set_id = param_dto.data_set_id
            task_name = param_dto.task_name
            eval_type = param_dto.eval_type
            eval_multi = param_dto.eval_multi
            if EVAL_TYPE_MULTI == eval_type and len(eval_multi) == 0:
                et_log.error(f"test_task insert_one_dto plan_id:{plan_id} task_name:{task_name} eval_multi is empty")
                return None
            # task_status = post_data["task_status"]
            bol_val = cls.check_task_name(plan_id, task_name)
            if bol_val:
                et_log.error(f"test_task insert_one_dto plan_id:{plan_id} task_name:{task_name} has already been used")
                return None

            # # 根据参数自动随机构建数据集
            # if TASK_DATASET_TYPE_BUILD == param_dto.data_set_type and param_dto.data_set_builds and len(param_dto.data_set_builds) > 0:
            #     data_set_id = DataSetService.auto_build_dataset(param_dto.data_set_builds, task_name)
            #     if not data_set_id or len(data_set_id) == 0:
            #         et_log.error(f"test_task insert_one_dto auto build dataset fail")
            #         return None

            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestTask("", current_time, 0, current_time, 0, 0,
                                 ctx_oem_codes, plan_id, param_dto.env_id, data_set_id, task_name, param_dto.task_type,
                                 0, "", param_dto.get_auto_run_json(), param_dto.get_dataset_builds_json(),
                                 param_dto.auto_run, param_dto.data_set_type, param_dto.config_id, param_dto.eval_type,
                                 param_dto.eval_multi, param_dto.answer_pt_code, param_dto.eval_pt_code).insert_one()
            # 塞入任务自动执行的定时任务策略
            task_id = str(insert_id)
            user_id = CTX_USER_ID.get()
            task_list = SchedulerTaskEntity.build_st_list(task_id, param_dto.execute_frequency)
            SchedulerMain.get_instance().save_task_job(cls.run_task, [task_id, user_id, ctx_oem_codes, user_id],
                                                       task_list)
            return task_id
        except Exception as e:
            et_log.error(f"test_task insert_one_dto exception:{e}")
            traceback.print_exc()
            return None

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = TestTask.find_by_pk(_id)
        if data_item is not None:
            return TestTask(**data_item)
        return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, query: TestTaskQueryDto, page_num: int, page_size: int):
        et_log.info(f"test_task query_page params:{query} "
                    f"page_num:{page_num} page_size:{page_size}")
        try:
            condition = query.get_query_condition()

            # 查询匹配的数量
            total = TestTask.find_condition_count(condition)
            if total < 1:  # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            task_list = TestTask.find_condition(condition, None, page_size, page.skip)
            if task_list is None or len(task_list) < 1:
                et_log.error(f"test_task query_page not found data error")
                return None
            item_json_list = []
            for item in task_list:  # 返回集合转json格式
                task_result_dic = item.to_json_str()
                if query.has_run_count:
                    task_result_condition = {"result_status": TEST_TASK_RESULT_STATUS_INIT, "task_id": ObjectId(item.id)}
                    run_task_result_count = TestTaskResult.find_condition_count(task_result_condition)
                    task_result_dic["run_count"] = run_task_result_count
                item_json_list.append(task_result_dic)
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_task query_page exception:{e},{traceback.print_exc()}")
            return None

    # 删除数据
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_task delete by _id:{_id} do_user:{do_user}")
        try:
            del_item = TestTask.find_by_pk(_id)
            if del_item is None:
                et_log.error(f"test_task delete by _id error, _id not found data")
                return False
            elif 1 == del_item["task_status"]:  # 1=执行中的任务，不可删除
                et_log.error(f"test_task delete by _id error, test_task is running")
                return False

            # 删除id对应的数据记录
            TestTask.delete_by_id(_id, do_user)

            # 删除测试任务对应的测试任务结果记录 <TODO 待确定是否需删除，建议不动>
            TestTaskResult.del_by_tid(_id, do_user)

            # 清除原对象上的所有定时任务
            item = TestTask(**del_item)
            if item.execute_frequency and len(item.execute_frequency) > 0:
                exec_dto_list = [TaskExecutionFrequencyDto(**ex_item) for ex_item in item.execute_frequency]
                task_list = SchedulerTaskEntity.build_st_list(_id, exec_dto_list)
                if task_list and len(task_list) > 0:
                    for job_param in task_list:
                        if job_param.task_id and len(
                                job_param.task_id) > 0 and SchedulerMain.get_instance().scheduler.get_job(
                                job_param.task_id):
                            SchedulerMain.get_instance().scheduler.remove_job(job_param.task_id)

            return True
        except Exception as e:
            et_log.error(f"test_task delete by _id exception")
            traceback.print_exc()
            return False

    # 更新
    @classmethod
    def update(cls, post_data: TestTask):
        et_log.info(f"test_task update by post_data:{post_data}")
        try:
            # 更新-主键检查
            task: TestTask = TestTask.find_by_pk(post_data.id)
            if task is None:
                et_log.error(f"update test_task error, _id not found data")
                return 0
            # 更新-任务名称检查
            condition = {}
            if post_data and post_data.task_name and len(str(post_data.task_name).strip()) > 0:
                condition["task_name"] = str(post_data.task_name).strip()
            condition["plan_id"] = post_data.plan_id
            # 检查数据是否重复
            items = TestTask.find_condition(condition, None, 100, 0)
            if items and len(items) > 0:
                for data_em in items:
                    if str(post_data.id) != str(data_em.id):  # 检查表明有重复的问题
                        et_log.error(f"test_task error, repeat question")
                        return 0
            # 更新请求的数据项数据
            return TestTask.update_entity_json(post_data.to_json())
        except Exception as e:
            et_log.error(f"update by test_task exception")
            traceback.print_exc()
            return 0

    # 更新 dto
    @classmethod
    def update_by_dto(cls, post_dto: TestTaskDto):
        et_log.info(f"test_task update_by_dto by post_dto:{post_dto}")
        try:
            # 更新-主键检查
            task = TestTask.find_by_pk(post_dto.task_id)
            if task is None:
                et_log.error(f"update_by_dto test_task error, _id not found data")
                return 0
            # 更新-任务名称检查
            condition = {}
            if post_dto.task_name and len(str(post_dto.task_name).strip()) > 0:
                condition["task_name"] = str(post_dto.task_name).strip()
            condition["plan_id"] = post_dto.plan_id
            # 检查数据是否重复
            items = TestTask.find_condition(condition, None, 100, 0)
            if items and len(items) > 0:
                for data_em in items:
                    if str(post_dto.task_id) != str(data_em.id):  # 检查表明有重复的问题
                        et_log.error(f"update_by_dto test_task error, repeat question")
                        return 0

            # # 根据参数自动随机构建数据集
            # data_set_id = post_dto.data_set_id
            # if TASK_DATASET_TYPE_BUILD == post_dto.data_set_type and post_dto.data_set_builds and len(post_dto.data_set_builds) > 0:
            #     data_set_id = DataSetService.auto_build_dataset(post_dto.data_set_builds, post_dto.task_name)
            #     if not data_set_id or len(data_set_id) == 0:
            #         et_log.error(f"test_task update_by_dto auto build dataset fail")
            #         return None

            # 清除原对象上的所有定时任务
            task_model = TestTask(**task)
            if task_model.execute_frequency and len(task_model.execute_frequency) > 0:
                exec_dto_list = [TaskExecutionFrequencyDto(**ex_item) for ex_item in task_model.execute_frequency]
                task_list = SchedulerTaskEntity.build_st_list(post_dto.task_id, exec_dto_list)
                if task_list and len(task_list) > 0:
                    for job_param in task_list:
                        if job_param.task_id and len(
                                job_param.task_id) > 0 and SchedulerMain.get_instance().scheduler.get_job(
                                job_param.task_id):
                            SchedulerMain.get_instance().scheduler.remove_job(job_param.task_id)

            # 塞入更新后的任务自动执行定时任务策略
            task_id = str(post_dto.task_id)
            user_id = CTX_USER_ID.get()
            task_list = SchedulerTaskEntity.build_st_list(task_id, post_dto.execute_frequency)
            if task_list and len(task_list) > 0:
                SchedulerMain.get_instance().save_task_job(cls.run_task,
                                                           [task_id, user_id, task_model.oem_codes, user_id],
                                                           task_list)

            update_task = TestTask(post_dto.task_id, None, None, None, None, None,
                                   None, post_dto.plan_id, post_dto.env_id, post_dto.data_set_id, post_dto.task_name,
                                   post_dto.task_type,
                                   None, None, post_dto.get_auto_run_json(), post_dto.get_dataset_builds_json(),
                                   post_dto.auto_run, post_dto.data_set_type, post_dto.config_id, post_dto.eval_type,
                                   post_dto.eval_multi,
                                   post_dto.answer_pt_code, post_dto.eval_pt_code)
            # 更新请求的数据项数据
            return TestTask.update_entity_json(update_task.to_json())
        except Exception as e:
            et_log.error(f"update_by_dto by test_task exception:{e}")
            traceback.print_exc()
            return 0

    # 检查数据集中的测试任务名称是否重复
    @classmethod
    def check_task_name(cls, plan_id, task_name):
        condition = {"plan_name": str(task_name).strip()}
        if plan_id and len(str(plan_id)) > 0:
            condition["plan_id"] = ObjectId(plan_id)
        total = TestTask.find_condition_count(condition)
        if total > 0:
            return True
        else:
            return False

    # 执行测试任务
    @classmethod
    def run_task(cls, task_id, do_user, oems, user_id):
        et_log.info(f"run_task param task_id:{task_id}")
        CTX_OEM_CODES.set(oems)
        CTX_USER_ID.set(user_id)

        try:
            task = cls.find_pk(task_id)
            if task is None:
                et_log.error(f"run_task task_id is invalid")
                return None

            # 获取执行任务环境（环境检查）
            config_info = None
            if task.eval_type is None or EVAL_TYPE_DEF == task.eval_type:
                if task.config_id:
                    config_info = find_config_info(task.config_id)
                elif task.env_id:
                    env = TestEnvService.find_pk(str(task.env_id))
                    config_info = TestConfigInfoModel(**env.to_json())

                if config_info is None:
                    et_log.error(f"run_task config_info is null")
                    return None
            else:
                if task.eval_multi is None or len(task.eval_multi) == 0:
                    et_log.error(f"run_task eval_multi is empty")
                    return None

            # 根据参数自动随机构建数据集
            data_set_id = ""
            if TASK_DATASET_TYPE_BUILD == task.data_set_type and task.data_set_builds and len(task.data_set_builds) > 0:
                data_set_id = DataSetService.auto_build_dataset(task.data_set_builds, task.task_name)
                if not data_set_id or len(data_set_id) == 0:
                    et_log.error(f"test_task insert_one_dto auto build dataset fail")
                    return None
                else:
                    # 自动生成的数据集，更新至任务上
                    task.data_set_id = data_set_id
                    cls.update(task)

            # 获取任务数据集（数据检查）
            if task.data_set_id is None or len(str(task.data_set_id)) == 0:
                et_log.error(f"run_task task.data_set_id is null")
                return None
            # 每日执行数限制检查
            if not cls.check_run_max_len(str(task.data_set_id)):
                et_log.error(f"run_task check run max length fail")
                return None
            data_list = DataItemService.query_by_set_id(str(task.data_set_id))
            if data_list is None:
                et_log.error(f"run_task task.data_set_id not found items")
                return None

            if EVAL_TYPE_MULTI == task.eval_type:  # 多模型评测
                et_log.info(f"run_task create test_task_result fail")
                cls.run_multi_task(task, data_list, oems, user_id)
            else:  # 应用评测
                # 插入当前请求数据项
                do_user = do_user if do_user is not None else 0
                result_data = {"task_id": task_id, "subjective_metric_id": "", "objective_metric_id": "",
                               "rate_final": float(0.0000),
                               "rate_answer": float(0.0000), "rate_category": float(0.0000),
                               "rate_ad_final": float(0.0000),
                               "rate_ad_answer": float(0.0000), "rate_ad_category": float(0.0000),
                               "rate_recall": float(0.0000),
                               "satisfactory": 1, "total_use_time": float(0.0000), "avg_score": float(0.0000),
                               "do_user": do_user,
                               "eval_config_id": str(task.config_id),
                               "data_set_id": str(task.data_set_id)}
                # 创建任务结果记录，状态task_status=1执行中
                result_id = TaskResultService.insert_one(result_data)
                if result_id is None:
                    et_log.error(f"run_task create test_task_result fail")
                    return None
                start_now = datetime.now()
                # 创建线程池，设置最大线程数
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    # 提交任务到线程池
                    futures = [
                        executor.submit(cls.run_item_answer, config_info, item, task_id, result_id, do_user, oems,
                                        user_id) for item in data_list]

                    # 获取任务结果
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            # 指定线程执行超时时间为300秒
                            result = future.result(timeout=300)
                            et_log.info(f"run_item_answer result: {result}")
                        except Exception as e:
                            et_log.error(f"run_item_answer exception: {e}")

                end_now = datetime.now()
                diff_dt = end_now - start_now
                use_time = diff_dt.seconds + diff_dt.microseconds * 0.000001
                et_log.info(f"run_task use_time:{use_time}")
                # 异步计算准确率、召回率、F1、平均分、总耗时等指标
                asyncio.run(TaskResultService.calculate_task_result(result_id, config_info.adapter_code, use_time, do_user, oems))
                return result_id
            return None
        except Exception as e:
            et_log.error(f"run_task task_id:{task_id} exception:{e},\ntraceback.print_exc()")
            return None

    @classmethod
    def run_item_answer(cls, config_info: TestConfigInfoModel, item: EtDataSetItem, task_id, result_id, do_user, oems,
                        user_id):
        """
        迭代数据集数据项，使用环境数据执行每条数据的任务，这里仅执行的是一级语料
        :param config_info: 问题运行的环境
        :param item: 数据集数据项对象
        :param task_id: 任务id
        :param result_id: 任务结果id
        :param do_user: 执行人id
        :return:
        """
        et_log.info(
            f"run_item_answer config_info:{config_info} item:{item} task_id:{task_id} result_id:{result_id} do_user:{do_user}"
        )
        try:
            CTX_OEM_CODES.set(oems)
            CTX_USER_ID.set(user_id)
            record_id = uuid4().hex
            request_id = uuid4().hex
            session_id = uuid4().hex
            # TODO 此处还需考虑多轮情况
            if ENV_ADAPTER_CODE_CATEGORY == config_info.adapter_code:  # 中枢分类
                response_data = DskLlmCategoryAdapter.run_item(config_info, item, record_id, session_id, "", task_id,
                                                               result_id,
                                                               do_user)
            elif ENV_ADAPTER_CODE_AUTOTEST == config_info.adapter_code:  # auto agent
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    # 在事件循环中运行协程
                    loop.run_until_complete(
                        AgentLlmTestAdapter.run_agent_autotest_item(
                            config_info,
                            item,
                            record_id,
                            request_id,
                            session_id,
                            "",
                            task_id,
                            result_id,
                            do_user,
                        )
                    )
                finally:
                    # 关闭事件循环
                    loop.close()
                # response_data = AgentLlmTestAdapter.run_agent_autotest_item(config_info, item, record_id, request_id,
                #                                                             session_id, "", task_id,
                #                                                             result_id, do_user)

            elif ENV_ADAPTER_CODE_HUB == config_info.adapter_code:  # 中枢agent
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                children = DataItemService.query_child_list(item._id)
                try:
                    # 在事件循环中运行协程
                    loop.run_until_complete(
                        AgentLlmHubAdapter.run_agent_hub_item(
                            config_info,
                            item,
                            record_id,
                            request_id,
                            session_id,
                            "",
                            task_id,
                            result_id,
                            do_user,
                            children,
                        )
                    )

                finally:
                    # 关闭事件循环
                    loop.close()
            elif ENV_mind2 == config_info.adapter_code:
                responce = AgentllmmindV2.run_chat(
                    config_info,
                    item,
                    record_id,
                    request_id,
                    session_id,
                    "",
                    task_id,
                    result_id,
                    do_user,
                )
            elif ENV_mind2agent == config_info.adapter_code:
                responce = AgentllmytmindV2.run_chat(config_info, item, record_id,
                                                     request_id, session_id, "", task_id,
                                                     result_id, do_user)
            elif ENV_ADAPTER_CODE_HUAWEI_CONTEXT == config_info.adapter_code:
                children = DataItemService.query_child_list(item._id)
                et_log.info(f"children{children}")
                Huawei_Context_service.run_huawei_agent_context(config_info, item, children, record_id, request_id,
                                                                session_id, "", task_id,
                                                                result_id, do_user)

            elif ENV_ADAPTER_CODE_CONTEXT == config_info.adapter_code:
                children = DataItemService.query_child_list(item._id)
                et_log.info(f"children{children}")
                Semantic_context_service.run_agent_context_item(
                    config_info,
                    item,
                    children,
                    record_id,
                    request_id,
                    session_id,
                    "",
                    task_id,
                    result_id,
                    do_user,
                )
            else:  # 模型能力通道评测
                task: TestTask = cls.find_pk(task_id)
                if not task or not task.config_id or len(str(task.config_id)) == 0:
                    et_log.error(f"run_item_answer not match test config")
                    return False
                if ENV_ADAPTER_CODE_BD_INTENT == config_info.adapter_code:  # 字节 意图识别
                    ByteDanceAdapter.intention(config_info, item, task_id, result_id, str(task.config_id), do_user,
                                               oems)
                elif ENV_ADAPTER_CODE_BD_REJECT == config_info.adapter_code:  # 字节 拒识
                    ByteDanceAdapter.reject(config_info, item, task_id, result_id, str(task.config_id), do_user, oems)
                elif ENV_ADAPTER_CODE_BD4HW_INTENT == config_info.adapter_code:  # 字节-华为 意图识别
                    ByteDanceAdapter.huawei_intention(config_info, item, task_id, result_id, str(task.config_id),
                                                      do_user, oems)
                elif ENV_ADAPTER_CODE_BD4HW_REJECT == config_info.adapter_code:  # 字节-华为 拒识
                    ByteDanceAdapter.huawei_reject(config_info, item, task_id, result_id, str(task.config_id), do_user,
                                                   oems)
                elif ENV_ADAPTER_CODE_VOLKSWAGEN_CONTEXT == config_info.adapter_code:
                    tool = Volkswagen_context(config_info, item, task_id, result_id, str(task.config_id), do_user,
                                                   oems)
                    tool.intent_reject()
                elif ENV_ADAPTER_CODE_VOLKSWAGEN_IMAGE == config_info.adapter_code:
                    tool = Volkswagen_image(config_info, item, task_id, result_id, str(task.config_id), do_user,
                                              oems)
                    tool.image_intent_reject()

                else:  # 通用模型评测
                    cls.run_multi_item(config_info, str(task.config_id), item, task_id, result_id, oems, do_user)

            return True
        except Exception as e:
            et_log.error(f"run_item_answer task_id:{task_id} exception:{e},\n{traceback.print_exc()}")
            return False

    # 根据task_id返回name
    @classmethod
    def query_name_by_task_id(cls, task_id):
        et_log.info(f"query_name_by_task_id params task_id:{task_id}")
        try:
            condition = {}
            if task_id and len(str(task_id).strip()) > 0:
                condition["_id"] = ObjectId(str(task_id).strip())
            # 查询匹配的数量
            total = TestTask.find_condition_count(condition)
            if total < 1:  # 未匹配到结果
                return None
            # 查询任务
            task_list = TestTask.find_condition(condition, None, -1, 0)

            # # 提取任务名称和创建时间
            # if task_list:
            #     result_list = [{"task_name": task.task_name, "create_time": task.create_time} for task in task_list]
            #     et_log.info(f"task_result_id    result: {result_list}")  # 记录日志
            #     return result_list
            # else:
            #     et_log.info("task_result_id    result: []")  # 处理空列表情况
            #     return []
            # 提取单个任务的 task_name 和 create_time
            if task_list and len(task_list) > 0:
                task = task_list[0]  # 只取第一个任务
                result_str = f"{task.task_name}-{task.create_time}"
                et_log.info(f"task_result_id result: {result_str}")  # 记录日志
                return result_str
            else:
                et_log.info("task_result_id result: None")  # 处理空结果情况
                return None

        except Exception as e:
            et_log.error(f"query_name_by_task_id exception")
            traceback.print_exc()
            return None

    @classmethod
    def init_task_jobs(cls):
        et_log.info(f"#######init_task_job#######")
        try:
            CTX_OEM_CODES.set(ALL_OEM_CODES)
            CTX_USER_ID.set(SYS_USER)
            condition = {"execute_frequency": {"$exists": True, "$ne": []}, "auto_run": True}
            # 查询任务
            task_list = TestTask.find_condition(condition, None, -1, 0)
            # 提取单个任务的 task_name 和 create_time
            if task_list and len(task_list) > 0:
                for task in task_list:
                    task_id = str(task.id)
                    user_id = CTX_USER_ID.get()
                    if not task.execute_frequency or len(task.execute_frequency) == 0:
                        continue
                    run_dtos = [TaskExecutionFrequencyDto(**doc) for doc in task.execute_frequency]
                    ste_list = SchedulerTaskEntity.build_st_list(task_id, run_dtos)
                    if ste_list and len(ste_list) > 0:
                        SchedulerMain.get_instance().save_task_job(cls.run_task,
                                                                   [task_id, user_id, task.oem_codes, user_id],
                                                                   ste_list)
                et_log.info(f"init_task_jobs success")  # 记录日志
                return True
            else:
                et_log.info("init_task_jobs auto task is empty")  # 处理空结果情况
                return None
        except Exception as e:
            et_log.error(f"init_task_jobs exception:{e}")
            traceback.print_exc()
            return None

    @classmethod
    def check_run_max_len(cls, set_id):
        """
        检查当天执行评测语料总数限制 task_id.set_data.count+current_user.test_result.count(创建人=当前人，创建时间=当天)
        :param set_id: 任务评测集id
        :return: Ture 未超限制|False 超限
        """
        et_log.info(f"check_run_max_len set_id:{set_id}")
        try:
            if not CTX_USER_ID.get() or SYS_USER == CTX_USER_ID.get():  # 系统帐号不做限制
                et_log.info(f"check_run_max_len sys_user don`t limit max length")
                return True
            # 获取限制数
            dic_list = DicInfo.find_condition({"code": DIC_CODE_MAX_RUN_ITEM, "group_code": DIC_GROUP_SYS_CONF}, None,
                                              None, None)
            if not dic_list or len(dic_list) == 0:
                et_log.error(f"check_run_max_len error, not found max_dic")
                return False
            max_count = dic_list[0].order_by

            # 待执行数 获取task_id对应的数据集语料数
            dataset_count = EtDataSetItem.find_comm_condition_count({"data_set_id": ObjectId(str(set_id))})

            # 已执行数 获取当前用户已执行的记录数
            cur_time_min = datetime.now().strftime("%Y-%m-%d") + " 00:00:00"
            cur_time_max = datetime.now().strftime("%Y-%m-%d") + " 23:59:59"
            result_run_count = TestResult.find_condition_count({"create_by": CTX_USER_ID.get(),
                                                                "create_time": {"$gte": cur_time_min,
                                                                                "$lte": cur_time_max}})

            if max_count > (dataset_count + result_run_count):
                return True
            else:
                return False
        except Exception as e:
            et_log.error(f"check_run_max_len exception:{e}")
            traceback.print_exc()
            return False

    @classmethod
    def run_multi_task(cls, task: TestTask, dataset_items: list, oems, user_id):
        """
        执行多模型对比任务
        :param task: 任务对象
        :param dataset_items: 数据集明细清单
        :param oems: 所属oem
        :param user_id: 操作人
        :return:
        """
        et_log.info(
            f"run_multi_task task:{task.to_json_str()} config_info:{dataset_items} oems:{oems} user_id:{user_id}")
        CTX_OEM_CODES.set(oems)
        CTX_USER_ID.set(user_id)
        configs = task.eval_multi.split(",")
        start_now = datetime.now()
        for conf_id in configs:  # 迭代通道模型
            config_info = find_config_info(conf_id)
            # 插入当前请求数据项
            result_data = {"task_id": task.id, "subjective_metric_id": "", "objective_metric_id": "",
                           "rate_final": float(0.0000),
                           "rate_answer": float(0.0000), "rate_category": float(0.0000), "rate_ad_final": float(0.0000),
                           "rate_ad_answer": float(0.0000), "rate_ad_category": float(0.0000),
                           "rate_recall": float(0.0000),
                           "satisfactory": 1, "total_use_time": float(0.0000), "avg_score": float(0.0000),
                           "do_user": user_id, "eval_config_id": conf_id, "data_set_id": str(task.data_set_id)}
            # 创建任务结果记录，状态task_status=1执行中
            task_result_id = TaskResultService.insert_one(result_data)
            if task_result_id is None:
                et_log.error(f"run_multi_task conf_id:{conf_id} create test_task_result fail")
                return None
            # 迭代任务评测集
            # 创建线程池，设置最大线程数
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                # 提交任务到线程池
                futures = [
                    executor.submit(cls.run_multi_item, config_info, conf_id, item, task.id, task_result_id, oems,
                                    user_id)
                    for item in dataset_items]

                # 获取任务结果
                for future in concurrent.futures.as_completed(futures):
                    try:
                        # 指定线程执行超时时间为300秒
                        result = future.result(timeout=300)
                        et_log.info(f"run_multi_task result: {result}")
                    except Exception as e:
                        et_log.error(f"run_multi_task exception: {e}")
            # 异步语料模型返回结果评分
            asyncio.run(TaskResultService.eval_answer_score(task_result_id, config_info.adapter_code))

            end_now = datetime.now()
            diff_dt = end_now - start_now
            use_time = diff_dt.seconds + diff_dt.microseconds * 0.000001
            et_log.info(f"run_task use_time:{use_time}")
            # 异步计算准确率、召回率、F1、平均分、总耗时等指标
            asyncio.run(TaskResultService.calculate_task_result(task_result_id, "", use_time))
        # 插入模型对比明细记录
        batch_id = datetime.now().strftime("%Y%m%d%H%M%S")
        cls.insert_multi_item_result(task.id, batch_id, oems, user_id)

    @classmethod
    def run_multi_item(cls, config_info: TestConfigInfoModel, conf_id, item, task_id, task_result_id, oems, user_id):
        """
        模型对比，单语料获取对应模型的回答
        :param config_info: 模型配置
        :param conf_id: 通道id
        :param item: 语料记录对象
        :param task_id: 任务id
        :param task_result_id: 任务结果id
        :param oems:
        :param user_id:
        :return:
        """
        CTX_OEM_CODES.set(oems)
        CTX_USER_ID.set(user_id)

        item: EtDataSetItem = item
        actual_answer = ""
        start_now = datetime.now()

        model_url = config_info.model_url
        model_ak = config_info.model_ak
        model_sk = config_info.model_sk
        model_bot = config_info.model_bot
        model_name = None
        if item.model_id:
            model_data = DicService.find_pk(item.model_id).to_json_str()
            model_name = model_data.get("name")
        # if model_name == "智能座舱与交互":
        hint_word = None
        task: TestTask = TestTask(**TestTask.find_by_pk(task_id))
        if task and task.answer_pt_code and len(task.answer_pt_code.strip()) > 0:
            comm_pt:CommPromptTemplateModel = CommPromptTemplateModel.query_by_tmp_code(task.answer_pt_code)
            if comm_pt and comm_pt.tmp_content and len(comm_pt.tmp_content) > 0:
                hint_word = comm_pt.tmp_content
        if ENV_ADAPTER_CODE_QIANFAN3_5 == config_info.adapter_code:  # 千帆3.5
            actual_answer = QianFan_MODEL_3.qianfan_model_35(item.question,hint_word)
        elif ENV_ADAPTER_CODE_QIANFAN4_0 == config_info.adapter_code:  # 千帆4.0
            actual_answer = QianFan_MODEL_4.qianfan_model_4(item.question,hint_word)
        elif ENV_ADAPTER_CODE_QIANWEN2_5 == config_info.adapter_code:  # 千问2.5
            actual_answer = qw_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        elif ENV_ADAPTER_CODE_QIANWEN3_32B == config_info.adapter_code:  # 千问3_32B
            actual_answer = qw_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        elif ENV_ADAPTER_CODE_QIANWENA3B == config_info.adapter_code:  # 千问3_30B-A3B
            actual_answer = qw_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        elif ENV_ADAPTER_CODE_QIANWENQwQ_32B == config_info.adapter_code:  # 千问3 QwQ-32B
            actual_answer = qw_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        elif ENV_ADAPTER_CODE_DOUBAO == config_info.adapter_code:  # 豆包
            actual_answer = EvalDoubaoAgent.compare_text_semantics(item.question,hint_word)
        elif ENV_ADAPTER_CODE_DSR1 == config_info.adapter_code:  # deepseekR1
            actual_answer = ds_r1_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        elif ENV_ADAPTER_CODE_DSV3 == config_info.adapter_code:  # deepseekV3
            actual_answer = ds_v3_chat_speak(item.question, model_url, model_ak, model_bot,hint_word)
        et_log.info(
            f"run_multi_item adapter_code:{config_info.adapter_code} question:{item.question} actual_answer:{actual_answer}")
        end_now = datetime.now()
        diff_dt = end_now - start_now
        use_time = diff_dt.seconds + diff_dt.microseconds * 0.000001
        if item.model_id:
            model_data = DicService.find_pk(item.model_id).to_json_str()
            # 获取 model_data 中的 "name"，如果没有则默认为 "闲聊百科"
            model_name = model_data.get("name", "闲聊及百科大问答")
        else:
            model_name = "闲聊及百科大问答"
        item_result = {"parent_id": "", "data_set_item_id": str(item.id), "task_id": task_id,
                       "task_result_id": str(task_result_id), "expected_answer": item.expected_answer,
                       "expected_category": model_name, "expected_task": model_name,
                       "actual_category": model_name, "result_category": model_name,
                       "actual_answer": actual_answer, "result_answer": 0, "answer_score": 0,
                       "qa_recall": 0, "qa_keywords": "", "qa_use_time": use_time, "result_final": 0,
                       "recall_id": f"{uuid4().hex}", "remark": "",
                       "do_user": user_id, "re_url": "", "re_cmd": "",
                       "re_error_id": "", "re_interval_time": 0, "is_websearch": 0, "eval_config_id": conf_id}
        # 记录每条数据的执行结果
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"run_multi_item insert_one item_result_id:{item_result_id}")

    @classmethod
    def get_task_compare_ai_model(cls, task_id):
        """
        根据任务ID，获取任务对应多模型最后一轮评估结果评分
        :param task_id:任务ID
        :return: ｛pip_id:"通道id", pip_name:"通道名称", pip:通道平均分｝
        """
        et_log.info(f"get_task_compare_ai_model task_id:{task_id}")
        task = cls.find_pk(task_id)
        if task is None or EVAL_TYPE_MULTI != task.eval_type or len(task.eval_multi) == 0:
            et_log.info(
                f"get_task_compare_ai_model task_id:{task_id} (task is None) or (eval_type != 1) or (eval_multi is empty)")
            return None
        pip_ids = task.eval_multi.split(",")
        models = []
        for pip_id in pip_ids:  # 获取任务的应通道 评估结果平均分
            config_info: TestConfigInfoReModel = find_config_info_re(pip_id)
            result_list = TestTaskResult.find_condition({"task_id": ObjectId(task_id), "eval_config_id": pip_id}, None,
                                                        1, 0)
            if result_list:
                result: TestTaskResult = result_list[0]
                models.append({"conf_id": pip_id, "conf_name": config_info.config_name, "avg_score": result.avg_score})
            else:
                et_log.error(f"get_task_compare_ai_model task_id:{task_id} ")
                continue
        return models

    @classmethod
    def get_test_result_compare_model(cls, task_id, page_num, page_size):
        """
        获取任务结果明细的模型对比打分（带分页）
        :param task_id: 任务id
        :param page_num: 当前页
        :param page_size: 每页记录数
        :return:
        """
        et_log.info(f"get_test_result_compare_model task_id:{task_id} page_num:{page_num} page_size:{page_size}")
        try:
            task = cls.find_pk(task_id)
            if task is None or EVAL_TYPE_MULTI != task.eval_type or len(task.eval_multi) == 0:
                et_log.info(
                    f"get_test_result_compare_model task_id:{task_id} (task is None) or (eval_type != 1) or (eval_multi is empty)")
                return None
            condition = {"task_id": ObjectId(task_id)}
            # 查询匹配的数量
            total = TestTaskAiResultModel.find_condition_count(condition)
            if total < 1:  # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            item_list = TestTaskAiResultModel.find_page_list(condition, None, page.skip, page_size)
            if item_list is None or len(item_list) < 1:
                et_log.error(f"get_test_result_compare_model not found data error")
                return None
            item_json_list = []
            for item in item_list:  # 获取同问题，不同模型的最后一次评测得分 返回集合转json格式
                item: TestTaskAiResultModel = item
                test_result_dic = {"question": item.question, "task_id": task_id}
                model_scores = item.model_scores
                for key in model_scores.keys():
                    test_result_dic[key] = model_scores[key]
                item_json_list.append(test_result_dic)
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"get_test_result_compare_model exception:{e},{traceback.print_exc()}")
            return None

    @classmethod
    def insert_multi_item_result(cls, task_id, batch_id, oem_code, user_id):
        """
        新增任务结果明细的模型对比打分（带分页）
        :param task_id: 任务id
        :param batch_id: 处理批次号
        :param oem_code: 当前操作人oem_code
        :param user_id: 操作人id
        :return:
        """
        et_log.info(
            f"insert_multi_item_result task_id:{task_id} batch_id:{batch_id} oem_code:{oem_code} user_id:{user_id}")
        try:
            # 删除任务结果模型比对历史记录
            TestTaskAiResultModel.del_by_condition({"task_id": ObjectId(task_id), "is_del": 0}, user_id)
            # 验证参数有效性
            task = cls.find_pk(task_id)
            if task is None or EVAL_TYPE_MULTI != task.eval_type or len(task.eval_multi) == 0:
                et_log.error(
                    f"insert_multi_item_result task_id:{task_id} (task is None) or (eval_type != 1) or (eval_multi is empty)")
                return False
            pip_ids = task.eval_multi.split(",")
            condition = {"data_set_id": task.data_set_id}
            # 查询任务评测明细数量
            total = EtDataSetItem.find_condition_count(condition)
            if total < 1:  # 未匹配到结果
                et_log.error(
                    f"insert_multi_item_result task_id:{task_id} data_set_id:{task.data_set_id} not found et_data_set_item data")
                return False
            # 查询任务评测明细全量
            item_list = EtDataSetItem.find_condition(condition, None, total, 0)
            for item in item_list:  # 返回集合转json格式
                item: EtDataSetItem = item
                model_scores = {}
                for pip_id in pip_ids:  # 获取同问题，不同模型的最后一次评测得分
                    config_info: TestConfigInfoReModel = find_config_info_re(pip_id)
                    result_condition = {"eval_config_id": pip_id, "data_set_item_id": ObjectId(str(item.id)),
                                        "task_id": ObjectId(task_id)}
                    answer_score = 0
                    test_result_list = TestResult.find_condition(result_condition, None, 1, 0)
                    if test_result_list:
                        answer_score = test_result_list[0].answer_score
                    model_scores[config_info.config_name] = answer_score
                ai_result = {"task_id": ObjectId(str(task_id)), "data_set_id": ObjectId(str(task.data_set_id)),
                             "data_set_item_id": ObjectId(str(item.id)),
                             "config_ids": task.eval_multi, "batch_id": batch_id, "question": item.question,
                             "model_scores": model_scores}
                CTX_OEM_CODES.set(oem_code)
                CTX_USER_ID.set(user_id)
                TestTaskAiResultModel(**ai_result).insert_one()
            return True
        except Exception as e:
            et_log.error(f"get_test_result_compare_model exception:{e},{traceback.print_exc()}")
            return False

# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))
