import asyncio
import threading
import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX, TASK_DATASET_TYPE_BUILD, TASK_DATASET_TYPE_SEL, \
    EVAL_TYPE_MULTI
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.dto.test_task_dto import TestTaskDto, TestTaskQueryDto
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_task_service import TestTaskService
from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID

test_task=Blueprint('test_task',__name__)

# 新增
@test_task.route(f'/{URL_PREFIX}/test-task/create', methods=['POST'])
def test_task_create():
    et_log.info("############test_task_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_task_create error, param is null", "").to_json()
        if "plan_id" not in data or len(str(data["plan_id"]).strip()) == 0:
            et_log.error(f"test_task_create error, plan_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "plan_id in param is null ", "").to_json()
        if "task_name" not in data or len(str(data["task_name"]).strip()) == 0:
            et_log.error(f"test_task_create error, task_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_name in param is null ", "").to_json()
        if "data_set_id" not in data or len(str(data["data_set_id"]).strip()) == 0:
            et_log.error(f"test_task_create error, data_set_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "data_set_id in param is null ", "").to_json()
        # if "env_id" not in data and len(str(data["env_id"]).strip()) == 0:
        #     et_log.error(f"test_task_create error, env_id in param is null")
        #     return ApiResult(ResultCode.PARAM_IS_BLANK.code, "env_id in param is null ", "").to_json()
        data["plan_id"] = str(data["plan_id"]).strip()
        # data["env_id"] = str(data["env_id"]).strip() TODO 暂定新增无需必选环境
        data["env_id"] = str(data["env_id"]).strip() if "env_id" in data and data["env_id"] is not None and len(str(data["env_id"])) > 0 else ""
        data["config_id"] = str(data["config_id"]).strip() if "config_id" in data and data["config_id"] is not None and len(str(data["config_id"])) > 0 else ""
        data["branch_id"] = str(data["branch_id"]).strip() if "branch_id" in data and data["branch_id"] is not None and len(str(data["branch_id"])) > 0 else ""
        data["data_set_id"] = str(data["data_set_id"]).strip()
        data["task_name"] = str(data["task_name"]).strip()
        data["task_type"] = data["task_type"] if ("task_type" in data and data["task_type"] is not None) else 1
        # data["eval_type"] = data.get("eval_type", 0)
        # data["eval_multi"] = data.get("eval_multi", "")
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        data["task_status"] = 0
        insert_result = TestTaskService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"test_task_create create test_task exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 新增
@test_task.route(f'/{URL_PREFIX}/test-task/create-new', methods=['POST'])
def test_task_create_new():
    et_log.info("############test_task_create_new################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_create_new error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_task_create_new error, param is null", "").to_json()
        param_data = TestTaskDto(**data)
        if not param_data.plan_id or len(str(param_data.plan_id).strip()) == 0:
            et_log.error(f"test_task_create_new error, plan_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "plan_id in param is null ", "").to_json()
        if not param_data.task_name or len(str(param_data.task_name).strip()) == 0:
            et_log.error(f"test_task_create_new error, task_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_name in param is null ", "").to_json()
        if TASK_DATASET_TYPE_SEL == param_data.data_set_type and (not param_data.data_set_id or len(str(param_data.data_set_id).strip()) == 0):
            et_log.error(f"test_task_create_new error, data_set_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "data_set_id in param is null ", "").to_json()
        if EVAL_TYPE_MULTI == param_data.eval_type and len(param_data.eval_multi) == 0:
            et_log.error(f"test_task_create_new error, eval_multi is empty")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "eval_multi is empty", "").to_json()
        insert_result = TestTaskService.insert_one_dto(param_data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"test_task_create_new create test_task exception:{e}")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 查询
@test_task.route(f'/{URL_PREFIX}/test-task/query', methods=['POST'])
def test_task_query():
    et_log.info("############test_task_query################")
    data = request.get_json()
    query = TestTaskQueryDto(**data)
    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = TestTaskService.query_page(query, page_num, page_size)
    page = "" if page is None else page.to_json()
    
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 删除
@test_task.route(f'/{URL_PREFIX}/test-task/del', methods=['POST'])   # 数据集删除
def test_task_del():
    et_log.info("############test_task_del################")
    data = request.get_json()
    if not data or "task_id" not in data or data["task_id"] is None:
        et_log.error(f"test_task_del delete error, task_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestTaskService.delete(data["task_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SYSTEM_INNER_ERROR.msg, "delete test plan error").to_json()

# 更新
@test_task.route(f'/{URL_PREFIX}/test-task/update', methods=['POST'])
def test_task_update():
    et_log.info("############test_task_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "task_id" not in data or data["task_id"] is None:
            et_log.error(f"test_task_update error, task_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id in param is null").to_json()
        if EVAL_TYPE_MULTI == data.get("eval_type", 0) and len(data.get("eval_multi", "")) == 0:
            et_log.error(f"test_task_update error, eval_multi is empty")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "eval_multi is empty", "").to_json()
        task_pk:TestTask = TestTaskService.find_pk(data["task_id"])
        if task_pk is None:
            et_log.error(f"test_task_update error, task_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "task_id id is invalid", "").to_json()

        task_pk.env_id = str(data["env_id"]).strip() if "env_id" in data and data["env_id"] is not None and len(str(data["env_id"])) > 0 else ""
        task_pk.config_id = str(data["config_id"]).strip() if "config_id" in data and data["config_id"] is not None and len(str(data["config_id"])) > 0 else ""
        task_pk.data_set_id = str(data["data_set_id"]).strip() if "data_set_id" in data and data["data_set_id"] is not None and len(str(data["data_set_id"])) > 0 else ""
        task_pk.branch_id = str(data["branch_id"]).strip() if "branch_id" in data and data["branch_id"] is not None and len(str(data["branch_id"])) > 0 else ""
        task_pk.task_name = str(data["task_name"]).strip() if "task_name" in data and data["task_name"] is not None and len(str(data["task_name"])) > 0 else ""
        task_pk.task_type = data["task_type"] if ("task_type" in data and data["task_type"] is not None) else None
        task_pk.eval_type = data["eval_type"] if ("eval_type" in data and data["eval_type"] is not None) else None
        task_pk.eval_multi = str(data["eval_multi"]).strip() if ("eval_multi" in data and data["eval_multi"] is not None) else None
        task_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        update_result = TestTaskService.update(task_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"test_task_update update test_task exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update exception").to_json()

# 更新 dto
@test_task.route(f'/{URL_PREFIX}/test-task/update-new', methods=['POST'])
def test_task_update_new():
    et_log.info("############test_task_update_new################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_task_update_new error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        post_dto = TestTaskDto(**data)
        if not post_dto or not post_dto.task_id:
            et_log.error(f"test_task_update_new error, task_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id in param is null").to_json()
        if EVAL_TYPE_MULTI == post_dto.eval_type and len(post_dto.eval_multi) == 0:
            et_log.error(f"test_task_update_new error, eval_multi is empty")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "eval_multi is empty", "").to_json()
        task_pk:TestTask = TestTaskService.find_pk(post_dto.task_id)
        if task_pk is None:
            et_log.error(f"test_task_update_new error, task_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "task_id id is invalid", "").to_json()

        # task_pk.env_id = str(data["env_id"]).strip() if "env_id" in data and data["env_id"] is not None and len(str(data["env_id"])) > 0 else ""
        # task_pk.data_set_id = str(data["data_set_id"]).strip() if "data_set_id" in data and data["data_set_id"] is not None and len(str(data["data_set_id"])) > 0 else ""
        # task_pk.branch_id = str(data["branch_id"]).strip() if "branch_id" in data and data["branch_id"] is not None and len(str(data["branch_id"])) > 0 else ""
        # task_pk.task_name = str(data["task_name"]).strip() if "task_name" in data and data["task_name"] is not None and len(str(data["task_name"])) > 0 else ""
        # task_pk.task_type = data["task_type"] if ("task_type" in data and data["task_type"] is not None) else None
        # task_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        update_result = TestTaskService.update_by_dto(post_dto)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"test_task_update update test_task exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update exception").to_json()


# 执行
@test_task.route(f'/{URL_PREFIX}/test-task/run', methods=['POST'])   # 数据集删除
async def run_test_task():
    et_log.info("############run_test_task################")
    try:
        data = request.get_json()
        if not data or "task_id" not in data or data["task_id"] is None:
            et_log.error(f"run_test_task run error, task_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        # 异步执行
        oems = CTX_OEM_CODES.get()
        user_id = CTX_USER_ID.get()
        thread = threading.Thread(target=TestTaskService.run_task, args=(data["task_id"], user_id, oems, user_id))
        thread.start()

        # loop = asyncio.new_event_loop()
        # asyncio.set_event_loop(loop)
        #     # 在事件循环中运行协程
        # try:
        #     loop.run_until_complete(await TestTaskService.run_task(data["task_id"], data["do_user"]))
        # finally:
        #     # 关闭事件循环
        #     loop.close()

        # asyncio.run(await TestTaskService.run_task(data["task_id"], data["do_user"]))
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    except Exception as e:
        et_log.error(f"run_test_task run task exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "run task exception", "").to_json()

# 查询任务模型对比结果
@test_task.route(f'/{URL_PREFIX}/test-task/model-compare', methods=['POST'])
def model_compare():
    et_log.info("############model_compare################")
    data = request.get_json()
    if not data or "task_id" not in data or data["task_id"] is None:
        et_log.error(f"model_compare error, task_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id is null").to_json()
    result = TestTaskService.get_task_compare_ai_model(data["task_id"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()

# 查询任务明细结果模型对比
@test_task.route(f'/{URL_PREFIX}/test-task/model-detail', methods=['POST'])
def model_detail():
    et_log.info("############model_detail################")
    data = request.get_json()
    if not data or "task_id" not in data or data["task_id"] is None:
        et_log.error(f"model_detail error, task_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "task_id is null").to_json()
    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = TestTaskService.get_test_result_compare_model(data["task_id"], page_num, page_size)
    page = "" if page is None else page.to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()
