
from openai import OpenAI
import json

def chat_speak(query):
    system_prompt = """
            分析用户输入的指令，自动识别并提取任务相关的关键信息。识别的内容包括：\n操作方式（construction_manner）、数量（amount_of_corpus_data）、数据类别（model）、项目名称（project，可为任意文本）、\n测试对象（test_object）。解析后，以 JSON 格式的字符串返回结果，使其适用于不同的任务类型和项目场景。\n{\n    \"construction_manner\": \"选择\",\n    \"amount_of_corpus_data\": 10,\n    \"model\": \"音乐\",\n    \"project\": \"公版\",\n    \"test_object\": \"agent\"\n}
            """

    client = OpenAI(
    #    base_url="http://47.117.182.61/qwen_14B/v1",
      # base_url="http://47.117.182.61/deepseek_32B/v1",
      base_url="http://47.117.182.61/qwen14B_ex/v1",
      api_key="none",
   )


    completion = client.chat.completions.create(
        # model="yt-model-32b-lora",  # 微调后的模型，需搭配系统提示词使用
        model="Qwen2.5-14B-Instruct-lora",  # 原始基座模型
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": query},
        ],
        temperature=0.01,
        stream = False,
        )

    data = json.loads(completion.choices[0].message.content)
    if "中枢+agent" in str(data):
        data["test_object"] = "中枢agent"
    return data

if __name__ == "__main__":
    query = "帮我构造100条音乐自由说数据，做一下长城项目的中枢+agent评测"
    res= chat_speak(query)
    print(res)