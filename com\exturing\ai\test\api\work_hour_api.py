# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.work_hour import WorkHourModel, WorkHourQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.work_hour_service import (
    create,
    query_page,
    query_self_page,
    update,
    delete,
    check_time_overlap,
)

work_hour = Blueprint("work_hour", __name__)


# 新增工时信息
@work_hour.route(f"/{URL_PREFIX}/staff/work_hour/create", methods=["POST"])
def work_hour_create():
    et_log.info("############work_hour_create################")
    req_data = request.get_json()
    work_hour_instance = WorkHourModel(**req_data)

    if check_time_overlap(work_hour_instance):
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code,
            "上报周期时间重叠，请检查！",
            "",
        ).to_json()

    work_hour_id = create(work_hour_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(work_hour_id)
    ).to_json()


# 个人工时上报信息分页查询
@work_hour.route(f"/{URL_PREFIX}/staff/work_hour/self/page", methods=["POST"])
def work_hour_self_page():
    et_log.info("############work_hour_self_page################")
    data = request.get_json()
    query = WorkHourQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_self_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 工时信息分页查询
@work_hour.route(f"/{URL_PREFIX}/staff/work_hour/page", methods=["POST"])
def work_hour_page():
    et_log.info("############work_hour_page################")
    data = request.get_json()
    query = WorkHourQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 修改工时信息
@work_hour.route(f"/{URL_PREFIX}/staff/work_hour/update", methods=["POST"])
def work_hour_update():
    et_log.info("############work_hour_update################")
    req_data = request.get_json()
    id = req_data.get("work_hour_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "work_hour_id is null", ""
        ).to_json()

    work_hour_instance = WorkHourModel(**req_data)

    if check_time_overlap(work_hour_instance, id):
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code,
            "上报周期时间重叠，请检查！",
            "",
        ).to_json()

    update_result = update(id, work_hour_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
    ).to_json()


# 删除工时信息
@work_hour.route(f"/{URL_PREFIX}/staff/work_hour/del", methods=["POST"])
def work_hour_delete():
    et_log.info("############work_hour_delete################")
    req_data = request.get_json()
    id = req_data.get("work_hour_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "work_hour_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()
