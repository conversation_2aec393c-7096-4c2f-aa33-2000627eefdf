import base64
import hashlib
import hmac
import json
import random
import time
from hashlib import sha256
from typing import Any, Dict, Optional
from urllib.parse import urlencode
import uuid

from httpx import get
from com.exturing.ai.test.volcengine_public.http_client import HttpClient
from com.exturing.ai.test.volcengine_public.constants import (
    AK,
    API_URL,
    APP_ID,
    CHANNEL,
    SK,
    VEHICLE_TYPE,
    VEHICLE_ID,
)
from com.exturing.ai.test.volcengine_public.utils import get_chat_id


class VolcengineClient:
    def __init__(self):
        """
        火山大模型接口调用Client
        """

        self.client = HttpClient(API_URL)

    def __enter__(self) -> "VolcengineClient":
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self.close()

    def close(self) -> None:
        """
        Closes the HTTP client.
        """
        self.client.close()

    def post(
        self,
        path: str,
        query: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ):
        """
        发送POST请求
        :param path: 请求路径
        :param query: 请求参数
        :param data: 请求体
        :return: 响应
        """
        chat_id = self.get_chat_id(query)
        query_dict = self.get_query(chat_id, query)
        body_dict = self.get_body(chat_id, data)
        headers = self.get_headers(query_dict, body_dict)

        return self.client.post(path, query_dict, body_dict, headers)

    def stream_post(
        self,
        path: str,
        query: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ):
        """
        发送流式POST请求
        :param path: 请求路径
        :param query: 请求参数
        :param data: 请求体
        :return: 响应
        """
        chat_id = self.get_chat_id(query)
        query_dict = self.get_query(chat_id, query)
        body_dict = self.get_body(chat_id, data)
        headers = self.get_headers(query_dict, body_dict)

        for chunk in self.client.stream_post(path, query_dict, body_dict, headers):
            if chunk:
                try:
                    line_str = chunk.decode("utf-8").strip()
                    if line_str.startswith("data:"):
                        line_str = line_str.removeprefix("data:").strip()
                    # 解析为 JSON
                    yield json.loads(line_str)
                except Exception as e:
                    yield None
            else:
                yield None

        return self.client.stream_post(path, query_dict, body_dict, headers)

    def put(
        self,
        path: str,
        query: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ):
        """
        发送PUT请求
        :param path: 请求路径
        :param query: 请求参数
        :param data: 请求体
        :return: 响应
        """
        chat_id = self.get_chat_id(query)
        query_dict = self.get_query(chat_id, query)
        body_dict = self.get_body(chat_id, data)
        headers = self.get_headers(query_dict, body_dict)

        return self.client.put(path, query_dict, body_dict, headers)

    def delete(
        self,
        path: str,
        query: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ):
        """
        发送DELETE请求
        :param path: 请求路径
        :param query: 请求参数
        :param data: 请求体
        :return: 响应
        """
        chat_id = self.get_chat_id(query)
        query_dict = self.get_query(chat_id, query)
        body_dict = self.get_body(chat_id, data)
        headers = self.get_headers(query_dict, body_dict)

        return self.client.delete(path, query_dict, body_dict, headers)

    def get_chat_id(self, query: Optional[Dict[str, Any]] = None) -> str:
        if query is None:
            return get_chat_id()

        return self.get_chat_id(query)

    def get_headers(
        self,
        query: Optional[Dict[str, Any]] = None,
        body: Optional[Dict[str, Any]] = None,
    ):
        query_str = urlencode(query, doseq=True, encoding="utf-8")
        body_str = json.dumps(body) if body else None

        sign = self.gen_sign(
            "POST",
            query_str,
            body_str,
            SK,
        )

        x_sign = AK + ":" + sign

        headers = {
            "X-Signature": x_sign,
            "X-Use-PPE": "1",  # DEMO测试环境
            "X-Tt-Env": "ppe_vehicle_model_test",  # DEMO测试环境
        }

        return headers

    def get_query(
        self,
        chat_id: str,
        query: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        生成请求参数
        :param query: 请求参数
        :return: 请求参数
        """

        timestamp = int(time.time())
        nonce = random.randint(0, 65535)

        query_dict = {
            "channel": CHANNEL,
            "app_id": APP_ID,
            "chat_id": chat_id,
            "vehicle_id": VEHICLE_ID,
            "vehicle_type": VEHICLE_TYPE,
            "_timestamp": timestamp,
            "_nonce": nonce,
        }

        if query:
            query_dict.update(query)

        return query_dict

    def get_body(
        self, chat_id: str, data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成请求体
        :param data: 请求体
        :return: 请求体
        """

        body_dict = {
            "chat_id": chat_id,
        }

        if data:
            body_dict.update(data)

        return body_dict

    def gen_sign(self, method, querystr, body, sk):
        """
        生成签名
        :param method: 请求方法
        :param querystr: 请求参数
        :param body: 请求体
        :param sk: 密钥
        """
        a = querystr.split("&")
        a.sort()
        sortedquerystr = "&".join(a)
        strtosign = method + "\n" + sortedquerystr + "\n"
        if body != None and len(body) > 0:
            m = hashlib.md5()
            m.update(body.encode("utf8"))
            strtosign += m.hexdigest() + "\n"

        h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
        return base64.b64encode(h).decode()
