import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.category_info import CategoryInfo
from com.exturing.ai.test.service.category_service import CategoryService

category=Blueprint('category',__name__)

# 新增
@category.route(f'/{URL_PREFIX}/category/create', methods=['POST'])
def category_create():
    et_log.info("############category_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"category_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "category_create error, param is null", "").to_json()
        if "category_name" not in data or len(str(data["category_name"]).strip()) == 0:
            et_log.error(f"test_task_create error, category_name is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "category_name is null ", "").to_json()
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        insert_result = CategoryService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"category_create exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 查询
@category.route(f'/{URL_PREFIX}/category/query', methods=['POST'])
def category_query():
    et_log.info("############category_query################")
    data = request.get_json()
    if not data:
        data = {}
    if not data or "create_from_time" not in data or data["create_from_time"] is None or len(str(data["create_from_time"]).strip()) == 0:
        data["create_from_time"] = ""
    if not data or "create_to_time" not in data or data["create_to_time"] is None or len(str(data["create_to_time"]).strip()) == 0:
        data["create_to_time"] = ""
    if not data or "page_num" not in data or data["page_num"] is None or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] is None or data["page_size"] < 1:
        data["page_size"] = 10
    page = CategoryService.query_page(data.get("category_name", ""), data.get("category_code", ""), data.get("category_type", None),
                                      data["create_from_time"], data["create_to_time"], data["page_num"], data["page_size"])
    page = "" if page is None else page.to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 删除
@category.route(f'/{URL_PREFIX}/category/del', methods=['POST'])   # 数据集删除
def category_del():
    et_log.info("############category_del################")
    data = request.get_json()
    if not data or "category_id" not in data or data["category_id"] is None:
        et_log.error(f"category_del delete error, category_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = CategoryService.delete(data["category_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SYSTEM_INNER_ERROR.msg, "delete error").to_json()

# 更新
@category.route(f'/{URL_PREFIX}/category/update', methods=['POST'])
def category_update():
    et_log.info("############category_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"category_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "category_id" not in data or data["category_id"] is None:
            et_log.error(f"category_update error, category_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "category_id is null").to_json()
        category_pk:CategoryInfo = CategoryService.find_pk(data["category_id"])
        if category_pk is None:
            et_log.error(f"category_update error, category_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "category_id id is invalid", "").to_json()

        category_pk.category_name = str(data["category_name"]).strip() if "category_name" in data and data["category_name"] is not None and len(str(data["category_name"])) > 0 else ""
        category_pk.category_code = str(data["category_code"]).strip() if "category_code" in data and data["category_code"] is not None and len(str(data["category_code"])) > 0 else ""
        category_pk.category_type = data["category_type"] if ("category_type" in data and data["category_type"] is not None) else None
        category_pk.pids = str(data["pids"]).strip() if "pids" in data and data["pids"] is not None and len(str(data["pids"])) > 0 else ""
        category_pk.memo = str(data["memo"]).strip() if "memo" in data and data["memo"] is not None and len(str(data["memo"])) > 0 else ""
        category_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        update_result = CategoryService.update(category_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"category_update exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update exception").to_json()
