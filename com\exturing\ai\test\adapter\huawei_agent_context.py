import copy
import json
import traceback
from datetime import datetime
from openai import OpenAI
import requests
from pandas import isnull

from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import *
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


system_prompt = """你是一款指代消歧系统，能够基于最近2~3轮对话上下文，自动判断并还原用户最新一轮发言中的指代内容，分析范围包括用户和助手的发言。
                    使用规则：
                    分析范围：仅限最近2~3轮对话，包含用户和助手的所有发言；
                    处理对象：仅对最新一轮用户发言中的代词或模糊指称进行检测；
                    输出要求：
                    若存在指代，请将该轮发言中的所有代词或模糊指称词，完整替换为对应的具体实体或内容，并输出还原后的完整句子；
                    若无指代或不需还原，仅输出：NO；
                    只输出目标内容，不添加任何额外解释或注释。
                    
                    示例用法：
                    上下文
                    用户：我昨天看了周星驰演的一部新喜剧。
                    助手：那部新喜剧叫什么名字？
                    用户：我记得他在里面演了一个厨师，还很搞笑。
                    输出：
                    我记得周星驰在那部新喜剧里演了一个厨师，还很搞笑。
                    
                    上下文
                    用户：给我推荐三首快歌。
                    助手：好的，推荐你：《不潮不用花钱》，《日不落》，《我们的歌》。
                    用户：第二首是谁唱的？
                    输出：
                    《日不落》是谁唱的？
                    
                    上下文
                    用户：周杰伦今年多大了？
                    助手：他好像五十多了。
                    用户：他的老婆是谁？
                    输出：
                    周杰伦的老婆是谁？
                    
                    （无指代）
                    上下文
                    用户：你喜欢猫吗？
                    助手：我挺喜欢猫的。
                    用户：我更喜欢狗。
                    输出：
                    NO
                    
                    （位置重复不等于指代）
                    上下文
                    用户：附近有什么好玩的
                    助手：天镜湖，72家理想村，梅园。
                    用户：附近有什么好吃的
                    输出：
                    NO
                    
                    """



class Huawei_Context_service:
    @classmethod
    def run_huawei_agent_context(cls,config_info:TestConfigInfoModel, item:EtDataSetItem,children, record_id, request_id, session_id, parent_id, task_id,
                                task_result_id, do_user):
        model_url = config_info.model_url
        model_bot = config_info.model_bot
        model_ak = config_info.model_ak
        et_log.info(
            f"run_agent_context_item params config_info:{config_info} item:{item}  "
            f"上下文接口model_url:{model_url},上下文接口model_bot:{model_bot}")
        try:
            whole_qa = [item.to_json_str()] + children
            et_log.info(f"生成的新列表: {whole_qa}")
            last_item = next((item for item in whole_qa if item.get('is_last') == 1), None)
            if not last_item:
                et_log.info("未找到 is_last=1 的元素")
                raise Exception(f"未找到 is_last=1 的元素")
            Q2 = last_item['question']
            parent_id1 = last_item['parent_id']
            parent_item = next((item for item in whole_qa if item['_id'] == parent_id1), None)
            if not parent_item:
                et_log.info(f"未找到 parent_id={parent_id} 的父元素")
                raise Exception(f"未找到 is_last=1 的元素")
            Q1 = parent_item['question']
            A1 = parent_item['expected_answer']
            input = f"""上下文
            用户：{Q1}
            助手：{A1}
            用户：{Q2}
            输出：
            """
            history = f"""
            <history>
                <question>{Q1}</question>
                <answer>{A1}</answer>
            <history>
            """
            et_log.info(f"输入数据为{input}")
            res = cls.huawei_context_model(model_url,model_bot,input,model_ak)
            test_task: TestTask = TestTask(**TestTask.find_by_pk(task_id))
            item_result1 = {"parent_id": parent_id, "data_set_item_id": str(item.id), "task_id": task_id,
                            "task_result_id": str(task_result_id), "recall_id": record_id,
                            "actual_answer": "", "answer_score": 0,
                            "eval_config_id": test_task.config_id,
                            "qa_recall": 0, "qa_keywords": "",
                            "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user}
            item_result_id = TestItemResultService.insert_one(item_result1)
            qa_keywords = last_item['qa_keywords']
            results = 1 if str(qa_keywords) in str(res) else 0
            item_result2 = {"parent_id": item_result_id, "data_set_item_id": str(children[0]["_id"]),
                            "task_id": task_id,
                            "eval_config_id": test_task.config_id,
                            "task_result_id": str(task_result_id), "recall_id": record_id,
                            "actual_answer": res, "result_answer": results, "answer_score": 0,
                            "qa_recall": 0, "qa_keywords": qa_keywords, "result_final": results,
                            "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user}
            item_result_id = TestItemResultService.insert_one(item_result2)
            et_log.info(f"\n历史QA:\n{history}\n当前回答：\n{res}")
            return history,res
        except Exception as e:
            et_log.error(f"运行报错{e}")
            return None

    @classmethod
    def huawei_context_model(cls,model_url,model_bot,input,model_ak):
        client = OpenAI(base_url=model_url, api_key=model_ak)
        completion = client.chat.completions.create(
            model=model_bot,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": input},
            ],
            temperature=0.01,
            stream=False,
        )
        res = completion.choices[0].message.content
        et_log.info(f"上下文指代回复内容为{res}")
        return res

















