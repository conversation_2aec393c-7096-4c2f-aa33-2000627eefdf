from typing import Optional

from com.exturing.ai.test.dto.params.base_dto import BaseDto


class MetricCalibDto(BaseDto):
    """
    指标标定查询参数DTO对象
    """
    metric_name: Optional[str] = ""  # 指标名称
    metric_app: Optional[str] = None    # 指标应用场景
    metric_pid: Optional[str] = ""    # 父级指标id
    metric_type: Optional[int] = None   # 指标类型 判定|打分|公式|等
    metric_status: Optional[int] = 0    # 指标状态 （预留字段） 0=初始
    metric_formula: Optional[str] = None    # 公式（判定类型）统计总数|平均值|占比|与|或

    def build_dto_condition(self):
        """
        按当前dto构建请求参数查询条件dic字典
        :return: 仅DTO中定义的参数字典
        """
        condition = {}
        if self.metric_name is not None and len(self.metric_name) > 0:
            condition["metric_name"] = self.metric_name
        if self.metric_app is not None and len(self.metric_app) > 0:
            condition["metric_app"] = self.metric_app
        if self.metric_pid is not None and len(self.metric_pid) > 0:
            condition["metric_pid"] = self.metric_pid
        if self.metric_type is not None:
            condition["metric_type"] = self.metric_type
        if self.metric_status is not None:
            condition["metric_status"] = self.metric_status
        if self.metric_formula is not None and len(self.metric_formula) > 0:
            condition["metric_formula"] = self.metric_formula
        return condition

    def build_all_condition(self):
        """
        构建全量请求参数字典
        :return: 返回包含请求公共参数dic
        """
        condition = self.comm_dto_condition()
        condition.update(self.build_dto_condition())
        return condition
