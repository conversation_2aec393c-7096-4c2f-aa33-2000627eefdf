from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class TestModelModel(BaseDataModel):
    model_name: str  # 评测模型名称
    url: Optional[str] = None  # 模型地址
    bot_model: Optional[str] = None  # 模型bot_model
    ak: Optional[str] = None  # 模型ak
    sk: Optional[str] = None  # 模型sk
    model_params: Optional[str] = None  # 模型参数
    desc: Optional[str] = None  # 模型描述


class TestModelQueryModel(BaseModel):
    model_name: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}

        if self.model_name:
            condition["model_name"] = {
                "$regex": str(self.model_name).strip(),
                "$options": "i",
            }

        return condition
