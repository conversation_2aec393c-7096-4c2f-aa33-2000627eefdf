# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.employee import EmployeeModel, EmployeeQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.employee_service import (
    create,
    query_page,
    query_page_all,
    query_name_list,
    update,
    delete,
    file_import,
    export_data,
    export_data_all,
)

employee = Blueprint("employee", __name__)


# 新增员工
@employee.route(f"/{URL_PREFIX}/employee/create", methods=["POST"])
def employee_create():
    et_log.info("############employee_create################")
    req_data = request.get_json()
    employee_instance = EmployeeModel(**req_data)
    employee_id = create(employee_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(employee_id)
    ).to_json()


# 员工分页查询
@employee.route(f"/{URL_PREFIX}/employee/page", methods=["POST"])
def employee_page():
    et_log.info("############employee_page################")
    data = request.get_json()
    query = EmployeeQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 员工分页查询（包含敏感信息）
@employee.route(f"/{URL_PREFIX}/employee/page-all", methods=["POST"])
def employee_page_all():
    et_log.info("############employee_page_all################")
    data = request.get_json()
    query = EmployeeQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page_all(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 员工名称列表
@employee.route(f"/{URL_PREFIX}/employee/name_list", methods=["POST"])
def employee_name_list():
    et_log.info("############employee_name_list################")
    data = request.get_json()
    query = EmployeeQueryModel(**data)

    list = query_name_list(query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, list).to_json()


# 修改员工
@employee.route(f"/{URL_PREFIX}/employee/update", methods=["POST"])
def employee_update():
    et_log.info("############employee_update################")
    req_data = request.get_json()
    id = req_data.get("employee_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "employee_id is null", ""
        ).to_json()

    employee_instance = EmployeeModel(**req_data)
    update_result = update(id, employee_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
    ).to_json()


# 删除员工
@employee.route(f"/{URL_PREFIX}/employee/del", methods=["POST"])
def employee_delete():
    et_log.info("############employee_delete################")
    req_data = request.get_json()
    id = req_data.get("employee_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "employee_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()


# 导入员工数据
@employee.route(f"/{URL_PREFIX}/employee/import", methods=["POST"])
def employee_import():
    et_log.info("############employee_import################")
    if "excel_file" in request.files:
        uploaded_file = request.files["excel_file"]
        # 先判断文件类型是否是Excel（简单判断扩展名，实际应用中可更严谨判断）
        if uploaded_file.filename.endswith(".xlsx") or uploaded_file.filename.endswith(
            ".xls"
        ):
            file_import(uploaded_file)
            return ApiResult(
                ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, ""
            ).to_json()
    else:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "excel_file is null", ""
        ).to_json()


# 导出员工数据
@employee.route(f"/{URL_PREFIX}/employee/export", methods=["POST"])
def employee_export():
    et_log.info("############employee_export################")
    data = request.get_json()
    query = EmployeeQueryModel(**data)

    excel_binary = export_data(query)

    return excel_binary


# 导出员工数据（包含敏感信息）
@employee.route(f"/{URL_PREFIX}/employee/export_all", methods=["POST"])
def employee_export_all():
    et_log.info("############employee_export_all################")
    data = request.get_json()
    query = EmployeeQueryModel(**data)

    excel_binary = export_data_all(query)

    return excel_binary
