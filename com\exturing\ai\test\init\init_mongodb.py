
from datetime import datetime

from bson import ObjectId
from sympy.codegen.fnodes import dimension

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.category_info import CategoryInfo
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.dic_info import DicInfo
from com.exturing.ai.test.model.et_data_set import EtDataSet
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.model.test_metric_calib import TestMetricCalibModel
from com.exturing.ai.test.model.test_plan import TestPlan
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_result import TestTaskResult


class InitMongDb:
    def_oid = ObjectId("df71f839588e9bd88e8be59d") # 默认ObjectId
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    def_user = 1

    @classmethod
    def init_all(cls):
        MongoDBUtil().mdb.drop_collection("et_data_set")
        MongoDBUtil().mdb.drop_collection("et_data_set_item")
        MongoDBUtil().mdb.drop_collection("test_plan")
        MongoDBUtil().mdb.drop_collection("test_task")
        MongoDBUtil().mdb.drop_collection("test_task_result")
        MongoDBUtil().mdb.drop_collection("test_result")
        set_id = cls.init_data_set()
        item_id = cls.init_data_set_item(set_id)
        plan_id = cls.init_test_plan()
        task_id = cls.init_task(plan_id, set_id)
        task_result_id = cls.init_task_result(task_id)
        test_result_id = cls.init_test_result(task_result_id, task_id, item_id)

    @classmethod
    def init_data_set(cls):
        set_id = EtDataSet("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1,
                           "all", "初始化数据集结构", "", "", "v0.0.1",
                           1, 0).insert_one()
        return set_id

    @classmethod
    def init_data_set_item(cls, set_id):
        item_id = EtDataSetItem("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1,
                                cls.def_oid, set_id, cls.def_oid, cls.def_oid, cls.def_oid,0, cls.def_oid,
                                "初始化数据集数据项结构", "期望回答", "期望任务类型",
                                "期望落域", "", 1, "", "",
                                "", "").insert_one()
        return item_id

    @classmethod
    def init_test_plan(cls):
        plan_id = TestPlan("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1,
                           cls.def_oid, "初始化测试计划结构", 1, cls.current_time, "",
                           "初始化测试计划结构").insert_one()
        return plan_id

    @classmethod
    def init_task(cls, plan_id, set_id):
        task_id = TestTask("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1, plan_id,
                           cls.def_oid, set_id, "初始化测试任务结构", 1, 0, "").insert_one()
        return task_id

    @classmethod
    def init_task_result(cls, task_id):
        # 根据测试任务，创建测试结果记录，依赖测试任务id
        task_result_id = TestTaskResult("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1,
                                        task_id, cls.def_oid, cls.def_oid, 0.00,
                                        0.00, 0.00, 0.00, 0.00,
                                        0.00, 0.00, 1, 0.000000000,
                                        0.00, 0, 0, "", "").insert_one()
        return task_result_id

    @classmethod
    def init_test_result(cls, task_result_id, task_id, item_id,):
        result_id = TestResult("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1,
                               cls.def_oid, task_id,
                               item_id, "初始化数据集数据项结构", "实际任务类型", "实际落域",
                               1, "实际回答", 1, 0, 0,
                               0.000000000, 1, 1,
                               1, 1, 1, "期望落域",
                               "期望任务类型", "期望回答", "", task_result_id,
                               cls.def_oid, cls.def_oid, "", "", "", "", 0,
                               0).insert_one()
        return result_id

    @classmethod
    def init_dic_info(cls):
        MongoDBUtil().mdb.drop_collection("dic_info")
        DicInfo("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 1, cls.def_oid,
                "初始化字典集合结构", "DEMO", "初始化集合结构", "INIT", 1, "初始分组").insert_one()

    @classmethod
    def init_test_env(cls):
        MongoDBUtil().mdb.drop_collection("test_env")
        TestEnv("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 0, "test-env_name",
                "test-env-code", 1, "test-env-script", "test-env-uri",
                "test-branch-name", "test env", "", "", "test-env-device-id",
                "v1.0.0", "100000000001", "", "").insert_one()

    @classmethod
    def init_category(cls):
        MongoDBUtil().mdb.drop_collection("category_info")
        CategoryInfo("", cls.current_time, cls.def_user, cls.current_time, cls.def_user, 0, "名称",
                "代码", 1, "车型id", "备忘").insert_one()

    @classmethod
    def init_metric_calib(cls):
        MongoDBUtil().mdb.drop_collection("test_metric_calib")
        init_data = {"create_by":cls.def_user, "create_time": cls.current_time, "update_by":cls.def_user, "update_time": cls.current_time,
                     "oem_codes": "all", "metric_name":"测试指标标定1",}
        TestMetricCalibModel(**init_data).insert_one()

    @classmethod
    def handler_origin_data(cls):
        _doc = "origin_data_item"
        results = MongoDBUtil.mdb["origin_data_item"].find({"create_time": "2025-02-27 14:22:00"})
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if results is not None:
            items = []
            for item in results:
                del item["_id"]
                item["create_time"] = current_time
                item["update_time"] = current_time
                in_id = MongoDBUtil.mdb["origin_data_item"].insert_one(item).inserted_id
                print(in_id)

    @classmethod
    def category_2aispeech(cls):
        """
        category根据
        """
        _doc = "category_info"
        results = MongoDBUtil.mdb[_doc].find()
        current_time = '2025-03-14 15:00:00'
        if results:
            for item in results:
                del item["_id"]
                item["create_time"] = current_time
                item["update_time"] = current_time
                item["create_by"] = -2
                item["update_by"] = -2
                item["oem_codes"] = "aispeech"
                in_id = MongoDBUtil.mdb[_doc].insert_one(item).inserted_id
                print(in_id)

    @classmethod
    def category_2aispeech(cls):
        """
        category根据
        """
        _doc = "category_info"
        results = MongoDBUtil.mdb[_doc].find({"oem_codes":"all", "is_del":0})
        current_time = '2025-03-14 15:00:00'
        if results:
            for item in results:
                del item["_id"]
                item["create_time"] = current_time
                item["update_time"] = current_time
                item["create_by"] = -2
                item["update_by"] = -2
                item["oem_codes"] = "aispeech"
                in_id = MongoDBUtil.mdb[_doc].insert_one(item).inserted_id
                print(in_id)

    @classmethod
    def dic_2aispeech(cls):
        """
        category根据
        """
        _doc = "dic_info"
        model_time = '2025-03-14 15:00:00'
        dimension_time = '2025-03-14 16:00:00'
        model_results = MongoDBUtil.mdb[_doc].find({"oem_codes":"all", "is_del":0, "group_code":"dm-model"})
        if model_results:
            for model_item in model_results:
                old_mid = str(model_item["_id"])
                del model_item["_id"]
                model_item["create_time"] = model_time
                model_item["update_time"] = model_time
                model_item["create_by"] = -2
                model_item["update_by"] = -2
                model_item["oem_codes"] = "aispeech"
                model_id = MongoDBUtil.mdb[_doc].insert_one(model_item).inserted_id
                print(f"new_model_id:{str(model_id)}")
                dimension_results = MongoDBUtil.mdb[_doc].find({"pid": ObjectId(old_mid), "oem_codes": "all", "is_del": 0, "group_code": "dm-dimension"})
                if not dimension_results:
                    continue
                for dimension_item in dimension_results:
                    del dimension_item["_id"]
                    dimension_item["pid"] = model_id
                    dimension_item["create_time"] = dimension_time
                    dimension_item["update_time"] = dimension_time
                    dimension_item["create_by"] = -2
                    dimension_item["update_by"] = -2
                    dimension_item["oem_codes"] = "aispeech"
                    dimension_id = MongoDBUtil.mdb[_doc].insert_one(dimension_item).inserted_id
                    print(f"new_dimension_id:{str(dimension_id)}")

# # 字典的全表数据，拷贝一份给aispeech帐号下
# InitMongDb.dic_2aispeech()

# # 落域的全表数据，拷贝一份给aispeech帐号下
# InitMongDb.category_2aispeech()

# 修改字段名称
# MongoDBUtil().mdb["test_result"].update_many({},{"$rename": {"expected_answer": "excepted_answer" }})
# 多字段名修改
# MongoDBUtil().mdb["test_result"].update_many({},{"$rename": {
#     "excepted_answer": "expected_answer",
#     "excepted_task": "expected_task",
#     "excepted_category": "expected_category"
# }})
# MongoDBUtil().mdb["et_data_set_item"].update_many({},{"$rename": {
#     "excepted_answer": "expected_answer",
#     "excepted_task": "expected_task",
#     "excepted_category": "expected_category"
# }})
# 无效
# MongoDBUtil().mdb["et_data_set_item"].update_many(
#   { "qa_use_time": NaN },
#   { "$set": { "qa_use_time": 0 } }
# )

# InitMongDb.init_all()
# InitMongDb.init_dic_info()
# InitMongDb.init_test_env()
# current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
# DicInfo("", current_time, 0, current_time, 0, 0,"", "lingyu_3.0_test",
#         "", "", DIC_CODE_BRANCH, 1).insert_one()
# DicInfo("", current_time, 0, current_time, 0, 0,"", "LLM_TEST",
#         "", "", DIC_CODE_BRANCH, 1).insert_one()
# DicInfo("", current_time, 0, current_time, 0, 0,"", "llm_test01279623278",
#         "", "", DIC_CODE_BRANCH, 1).insert_one()
# DicInfo("", current_time, 0, current_time, 0, 0,"", "LLM_prod2.0_TEST",
#         "", "", DIC_CODE_BRANCH, 1).insert_one()

# data_dic = MongoDBUtil().mdb["test_env"].find_one({"_id": ObjectId("6789bc2794f4e124e227fcd4")})
# message = copy.deepcopy(json.loads(data_dic["env_script"]))
# print(int(time.time()))
# print(int(datetime.now().timestamp()))


# data = {
#   "sessionId": "722312a2f0f64ef49a310140bd475fae",
#   "asr": "给我生成一个副驾坐人氛围灯就调成粉色，播放周杰伦的可爱女人，创建一个浪漫的车机壁纸",
#   "codeMsg": "success",
#   "data": {
#     "discard": "True",
#     "category": {
#
#     },
#     "debug": "Z, 依据拒识标准1"
#   },
#   "recordId": "f3d35db3b4274724a2fa4f11d26d5e94",
#   "code": 200
# }
# print(data.get("test", "1111111"))

# pipeline = [
#     {
#     "$match": {
#       "task_result_id": ObjectId("67ad8e351556a7b7b0868341")
#     }
#   },
#   {
#     "$group": {
#       "_id": None,
#       "total_use_time": { "$sum": "$qa_use_time" },
#       "average_score": { "$avg": "$answer_score" }
#     }
#   }
# ]
# results = list(MongoDBUtil().mdb["test_result"].aggregate(pipeline))
# # 输出结果
# if results:
#     total_score = results[0]['total_use_time']
#     average_score = results[0]['average_score']
#     print(f"qa_use_time 字段的总和: {total_score}")
#     print(f"score 字段的平均分: {average_score}")
# else:
#     print("未找到符合条件的文档。")


# InitMongDb.init_metric_calib()

# InitMongDb.handler_origin_data()