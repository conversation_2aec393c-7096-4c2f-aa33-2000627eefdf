import os
import logging.config

logfile_dir = os.path.dirname(os.path.abspath(__file__)) + "../../../../../../../logs/ai_test"  # log文件的目录

if not os.path.exists(logfile_dir):
    os.makedirs(logfile_dir)

logfile_name = logfile_dir + '/app.log'

# log配置字典
LOGGING_DIC = {
  "version": 1,
  "formatters": {
    "full": {
        "format": "[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s][%(filename)s:%(lineno)d][%(levelname)s][%(message)s]"
    },
    "sample": {
        "format": "[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d][%(message)s]"
    },
    "stand_format": {
        # "format": "[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s][%(filename)s:%(lineno)d][%(levelname)s][%(message)s]"
        "format": "[%(asctime)s][%(threadName)s:%(thread)d][%(filename)s:%(lineno)d][%(levelname)s][%(message)s]"
    }
  },
  "filters": {},
  "handlers": {
    "console": {
      "level": "DEBUG",
      "class": "logging.StreamHandler",
      "formatter": "sample"
    },
    "server_file": {
      "level": "DEBUG",
      "class": "logging.handlers.TimedRotatingFileHandler",
      "formatter": "stand_format",
      # "formatter": "sample",
      "filename": logfile_name,
      "when": "D",
      "interval": 1,
      # "maxBytes": 1024*1024*500,  # 日志大小 500M
      "backupCount": 30,
      "encoding": "utf-8"
    },
    # "client_file": {
    #   "level": "DEBUG",
    #   "class": "logging.handlers.TimedRotatingFileHandler",
    #   "formatter": "sample",
    #   "filename": "client.log",
    #   "when": "D",
    #   "interval": 1,
    #   "backupCount": 30,
    #   "encoding": "utf-8"
    # }
  },
  "loggers": {
    "server": {
      "handlers": ["console", "server_file"],
      "level": "DEBUG",
      "propagate": False
    },
    # "client": {
    #   "handlers": ["console", "client_file"],
    #   "level": "DEBUG"
    # }
  }
}

logging.config.dictConfig(LOGGING_DIC)
logger = logging.getLogger("server")  # 生成一个log实例

# if __name__=="__main__":
#     logger.info("info")
#     logger.debug("debug")
#     logger.error("error")
#     logger.critical("critical")