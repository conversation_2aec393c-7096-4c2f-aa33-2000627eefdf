# api.py
from flask import Blueprint, request
from com.exturing.ai.test.model.prompt_template import (
    PromptTemplateModel,
    PromptTemplateQueryModel,
)
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.prompt_template_service import (
    create,
    query_page,
    update,
    delete,
    generate,
)

prompt_template = Blueprint("prompt_template", __name__)


# 新增语料生成模板
@prompt_template.route(f"/{URL_PREFIX}/prompt-template/create", methods=["POST"])
def prompt_template_create():
    et_log.info("############prompt_template_create################")
    req_data = request.get_json()
    prompt_template_instance = PromptTemplateModel(**req_data)
    prompt_template_id = create(prompt_template_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(prompt_template_id)
    ).to_json()


# 语料生成模板分页查询
@prompt_template.route(f"/{URL_PREFIX}/prompt-template/page", methods=["POST"])
def prompt_template_page():
    et_log.info("############prompt_template_page################")
    data = request.get_json()
    query = PromptTemplateQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 修改语料生成模板
@prompt_template.route(f"/{URL_PREFIX}/prompt-template/update", methods=["POST"])
def prompt_template_update():
    et_log.info("############prompt_template_update################")
    req_data = request.get_json()
    id = req_data.get("prompt_template_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "prompt_template_id is null", ""
        ).to_json()

    prompt_template_instance = PromptTemplateModel(**req_data)
    update_result = update(id, prompt_template_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
    ).to_json()


# 删除语料生成模板
@prompt_template.route(f"/{URL_PREFIX}/prompt-template/del", methods=["POST"])
def prompt_template_delete():
    et_log.info("############prompt_template_delete################")
    req_data = request.get_json()
    id = req_data.get("prompt_template_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "prompt_template_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()


@prompt_template.route(f"/{URL_PREFIX}/prompt-template/generate", methods=["POST"])
def prompt_template_generate():
    et_log.info("############prompt_template_generate################")
    req_data = request.get_json()
    prompt_template_id = req_data.get("prompt_template_id", None)

    if prompt_template_id is None or len(prompt_template_id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "prompt_template_id is null", ""
        ).to_json()

    generate_list = generate(prompt_template_id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, generate_list
    ).to_json()
