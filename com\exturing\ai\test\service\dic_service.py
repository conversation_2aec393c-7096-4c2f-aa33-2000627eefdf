import traceback
from datetime import datetime

import pymongo
from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.dic_info import DicInfo


# 字典集合Service
class DicService:

    # 数据字典集合 查询
    @classmethod
    def query_list(cls, pid, name, group_code):
        et_log.info(f"query_list params pid:{pid} name:{name} group_code:{group_code}")
        try:
            dic_list = DicInfo.find_name(pid, name, group_code)
            if dic_list and len(dic_list) > 0:
                dic_json_list = []
                for dic in dic_list:
                    dic_json_list.append(dic.to_json_str())
                return dic_json_list
            else:
                return []
        except Exception as e:
            et_log.error(f"query_list is exception,\n{traceback.print_exc()}")
            return []

    @classmethod
    def query_group_list(cls, group_code, group_name):
        et_log.info(f"query_group_list params group_code:{group_code} group_name:{group_name}")
        try:
            condition = {}
            if group_code and len(group_code) > 0:
                condition["group_code"] = group_code
            if group_name and len(group_name) > 0:
                condition["group_name"] = group_name
            # 查询匹配的数量
            group = {"group_code": "$group_code", "group_name": "$group_name"}
            group_list = DicInfo.query_group_list(condition, group, None)
            return group_list
        except Exception as e:
            et_log.error(f"query_group_list is exception,\n{traceback.print_exc()}")
            return []

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data = DicInfo.find_by_pk(_id)
        if data is not None:
            return DicInfo(**data)
        return None

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one dic_info post_data:{post_data}")
        try:
            # 插入当前请求数据
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            dic_name = post_data["dic_name"]
            dic_code = post_data.get("dic_code","")
            group_code = post_data["group_code"]
            bol_val = cls.check_name_code(dic_name, group_code)
            if bol_val:
                et_log.error(f"insert_one dic_info error, dic_name or dic_code has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = DicInfo("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                ctx_oem_codes, post_data["pid"], dic_name, dic_code, post_data.get("dic_desc", ""), group_code,
                                post_data.get("order_by", 0), post_data.get("group_name", ""),).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"insert_one dic_info exception,\n{traceback.print_exc()}")
            return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, pid, dic_name, dic_code, group_code, create_from_time, create_to_time, page_num: int, page_size: int):
        et_log.info(f"dic_info query_page params dic_name:{dic_name} dic_code:{dic_code} group_code:{group_code} "
                    f"create_from_time:{create_from_time} create_to_time:{create_to_time} "
                    f"page_num:{page_num} page_size:{page_size}")
        try:
            condition = {"pid": ObjectId(str()) if len(pid) > 0 else ""}
            if dic_name and len(str(dic_name).strip()) > 0:
                condition["name"] = {"$regex": str(dic_name).strip(), "$options": "i"}
            if dic_name and len(str(dic_code).strip()) > 0:
                condition["code"] = dic_code
            if group_code and len(str(group_code).strip()) > 0:
                condition["group_code"] = group_code
            create_time_filter = {}
            if create_from_time and len(str(create_from_time).strip()) > 0:
                create_time_filter["$gte"] = str(create_from_time).strip()
            if create_to_time and len(str(create_to_time).strip()) > 0:
                create_time_filter["$lte"] = str(create_to_time).strip()
            if create_time_filter is not None and len(create_time_filter) > 0:
                condition["create_time"] = create_time_filter
            # 查询匹配的数量
            total = DicInfo.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            sorts = [("order_by", pymongo.ASCENDING), ("create_time", pymongo.DESCENDING)]
            dic_list = DicInfo.find_condition(condition, sorts, page_size, page.skip)
            if dic_list is None or len(dic_list) < 1:
                et_log.error(f"dic_info query_page not found data error")
                return None
            item_json_list = []
            for item in dic_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"dic_info query_page exception,\n{traceback.print_exc()}")
            return None

    # 删除
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"dic_info delete by _id:{_id} do_user:{do_user}")
        try:
            del_item:DicInfo = cls.find_pk(_id)
            if del_item is None:
                et_log.error(f"dic_info delete by _id error, _id not found item")
                return False
            # 删除id对应的数据
            DicInfo.delete_by_id(_id, do_user)

            # 根据id检查，该数据项是否有子节点，若有则需更新=所有子节点都逻辑删除
            DicInfo.del_by_pid(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"dic_info delete by _id exception,\n{traceback.print_exc()}")
            return False

    # 更新数据
    @classmethod
    def update(cls, post_data:DicInfo):
        et_log.info(f"dic_info update by post_data:{post_data}")
        try:
            # 更新-主键检查
            data:DicInfo = cls.find_pk(post_data.id)
            if data is None:
                et_log.error(f"update dic_info error, _id not found data")
                return 0
            # 更新-名称和代码检查
            if cls.check_update_dic_repeat(post_data.name, post_data.code, post_data.group_code, data.id):
                # 更新请求的数据项数据
                return DicInfo.update_entity_json(post_data.to_json())
            else:
                return 0
        except Exception as e:
            et_log.error(f"dic_info update exception,\n{traceback.print_exc()}")
            return 0

    # 检查名称、（重复性检查）
    @classmethod
    def check_name_code(cls, dic_name, group_code):
        try:
            condition = {"name": str(dic_name).strip(), "group_code": group_code}
            total = DicInfo.find_condition_count(condition)
            if total > 0:
                return True
            return False
        except Exception as e:
            et_log.error(f"dic_info check_name_code exception,\n{traceback.print_exc()}")
            return False

    # 更新环境时，检查名称和代码的重复性
    @classmethod
    def check_update_dic_repeat(cls, dic_name, dic_code, group_code, up_id):
        et_log.info(f"dic_info check_update_dic_repeat params dic_name:{dic_name} dic_code:{dic_code} "
                    f"group_code:{group_code} up_id:{up_id}")
        try:
            condition = {"_id": {"$ne": ObjectId(up_id)}, "group_code": group_code}
            if dic_name and len(str(dic_name).strip()) > 0:
                condition["name"] = str(dic_name).strip()
                count = DicInfo.find_condition_count(condition)
                if count > 0:
                    return False
            if dic_code and len(str(dic_code).strip()) > 0:
                condition["code"] = str(dic_code).strip()
                count = DicInfo.find_condition_count(condition)
                if count > 0:
                    return False
            return True
        except Exception as e:
            et_log.error(f"check_update_dic_repeat dic_info exception,\n{traceback.print_exc()}")
            return False

# 查询全量模块
# print(DicService.query_list("", "", "dm-model"))
# print(DicService.query_list("6780bb01b834e81f583b141c", "", "dm-dimension"))