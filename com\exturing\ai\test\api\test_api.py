
from datetime import datetime
import time

from flask import Blueprint, Response, request

from com.exturing.ai.test.agent.eval_doubao_agent import EvalD<PERSON>baoAgent
from com.exturing.ai.test.agent.eval_qianfang_agent import EvalQianFangAgent
from com.exturing.ai.test.comm.log_tool import et_log

# from com.exturing.ai.test.comm.log_util import logger

test=Blueprint('test',__name__)
@test.route('/login')   #使用user的路由配置
async def login():
    et_log.info("############Login################")
    return {"name": "test_1", "age": 10}


def generate_events():
    """生成事件数据"""
    result_data = {'data_status':0, 'data_status_msg': '评测集处理中', 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()
    time.sleep(3)  # 每2秒发送一次事件
    result_data = {'data_status':1, 'data_status_msg': '评测集已生成', 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()
    time.sleep(2)  # 每2秒发送一次事件
    result_data = {"data_status":2, "data_status_msg": "评测计划已生成", 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()
    time.sleep(2)  # 每2秒发送一次事件
    result_data = {"data_status":3, "data_status_msg": "评测任务已生成", 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()
    time.sleep(2)  # 每2秒发送一次事件
    result_data = {"data_status":4, "data_status_msg": "评测任务执行中", 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()
    time.sleep(5)  # 每2秒发送一次事件
    result_data = {"data_status":5, "data_status_msg": "评测任务已完成", 'ctime':datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    yield f"data: {result_data}\n\n".encode()

@test.route('/sse', methods=['POST'])
def sse():
    """SSE 路由"""
    # return Response(generate_events(), mimetype='application/json+event-stream')
    return Response(generate_events(), mimetype='text/event-stream')

@test.route('/n8n-test')   #使用user的路由配置
async def n8n_test():
    et_log.info("############n8n_test################")
    # result = EvalDoubaoAgent.compute_similarity(request.get_json()["chatInput"])
    result = EvalQianFangAgent.compute_similarity(request.get_json()["chatInput"])
    return Response(result, mimetype='text/event-stream')