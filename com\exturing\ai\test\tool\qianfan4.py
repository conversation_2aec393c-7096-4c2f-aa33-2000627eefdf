import json
import re
import traceback

import requests

from com.exturing.ai.test.comm.comm_constant import QIANFANG_ACCESS_TOKEN_URL, QIANFANG_SIMILARITY_URL
from com.exturing.ai.test.comm.log_tool import et_log


# 百度千帆评估智能体
class QianFan_MODEL_4:

    # 百度千帆
    @classmethod
    def get_access_token(cls):
        """
        获取百度千帆 access_token
        """
        response = requests.post(QIANFANG_ACCESS_TOKEN_URL)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            raise Exception(f"获取 access_token 失败：{response.text}")

    @classmethod
    def model_connect(cls, prompt_data,system_prompt):
        et_log.info(f"qianfan4 prompt_data:{prompt_data}")
        """
        调用流式API接口计算语义相似度
        """
        try:
            access_token = cls.get_access_token()  # 替换为实际的访问令牌
            et_log.info(f"compute_similarity get_access_token:{access_token}")
            url = QIANFANG_SIMILARITY_URL.format(**{"ACCESS_TOKEN":access_token})
            messages = []
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            messages.append({  # 加入用户输入
                "role": "user",
                "content": prompt_data
            })
            payload = json.dumps({
                "messages": messages,
                "stream": True
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.post(url, headers=headers, data=payload, stream=True)
            if response.status_code == 200:
                # 收集流式响应结果
                full_text = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            decoded_line = line.decode("UTF-8")
                            json_data = json.loads(decoded_line.replace("data: ", ""))
                            if "result" in json_data:
                                full_text += json_data["result"]
                        except json.JSONDecodeError:
                            continue
                et_log.info(f"compute_similarity full_text:{full_text}")
                return full_text
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}, 错误信息: {response.text}")
        except Exception as e:
            et_log.error(f"compute_similarity exception:{e},\n{traceback.print_exc()}")


    @classmethod
    def qianfan_model_4(cls, question,hint_word):
        result = cls.model_connect(question,hint_word)
        et_log.info(f"qianfanmodel4——————————————————————————:{result}")
        return result




# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我钟情于节奏轻快的流行音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我喜欢古典音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"", "answer2":"我喜欢古典音乐。"}
# QianFan_MODEL_4.qianfan_model_4("你喜欢什么类型的音乐？")

