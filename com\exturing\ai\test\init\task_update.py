from com.exturing.ai.test.comm.mongodb_util import MongoDB<PERSON>til


def update_task_info():
    """更新所有task信息
    根据task中的env_id找出config中对应的config_id,
    再将config_id更新到对应的task中
    """
    tasks = MongoDBUtil.mdb["test_task"].find({"is_del": 0}) or []
    configs = MongoDBUtil.mdb["test_config"].find({"is_del": 0}) or []

    task_list = list(tasks)
    config_list = list(configs)
    print(f"task_list: {len(task_list)}")
    print(f"config_list: {len(config_list)}")

    config_map = {}
    for config in config_list:
        if config.get("env_id"):
            config_map[config.get("env_id")] = config["_id"]

    for task in task_list:
        env_id = task.get("env_id")
        if env_id and not task.get("config_id"):
            config_id = config_map.get(str(env_id))
            if config_id:
                MongoDBUtil.mdb["test_task"].update_one(
                    {"_id": task["_id"]}, {"$set": {"config_id": config_id}}
                )

                print(f"update task: {task['_id']} config_id: {config_id}")


if __name__ == "__main__":
    update_task_info()

# Run this script with the following command:
# python -m com.exturing.ai.test.init.task_update
