from apscheduler.schedulers.background import BackgroundScheduler

from com.exturing.ai.test.comm.comm_constant import TASK_CYCLE_DAY, TASK_CYCLE_WEEK, TASK_CYCLE_MONTH, TASK_CYCLE_CUSTOM
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.scheduler.scheduler_task import SchedulerTaskEntity
from com.exturing.ai.test.service.employee_service import send_entry_mail
from com.exturing.ai.test.service.test_task_result_service import TaskResultService


class SchedulerMain:
    """
    定时任务类
    """

    scheduler = None
    _instance = None

    def __init__(self):
        et_log.info("scheduler start...")
        self.scheduler = BackgroundScheduler()
        self.add_task()
        self.scheduler.start()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def add_task(self):
        """
        添加定时任务
        :return:
        """
        et_log.info("scheduler add_task")
        # 每隔 10 秒执行一次
        # scheduler.add_job(func=scheduled_task, trigger='interval', seconds=10, args=['参数值1', '参数值2'])
        # 添加定时任务，每天上午 10 点执行，并传递参数
        # self.scheduler.add_job(func=scheduled_task, trigger='cron', hour=10, args=['参数值1', '参数值2'])
        # # 每隔一小时执行过期未完成任务结果检查
        # self.scheduler.add_job(func=TaskResultService.handler_timeout_task, trigger='interval', minutes=1)
        # 每隔一小时执行过期未完成任务结果检查
        self.scheduler.add_job(func=TaskResultService.handler_timeout_task, trigger='cron', hour='*')

        # 每日上午11时，执行发送明天的入职名单邮件
        # self.scheduler.add_job(func=send_entry_mail, trigger='cron', hour=13, minute=29, second=10)
        self.scheduler.add_job(func=send_entry_mail, trigger='cron', hour=11)

    def save_task_job(self, function, params, task_list:list[SchedulerTaskEntity]):
        """
        新增或修改任务执行的计划
        :param function: 调用方法
        :param params: 参数数组，如:[test_task_id, do_user, oems, user_id]
        :param task_list: 任务执行策略list
        :return:
        """
        et_log.info(f"save_task_job function:{function} task_list:{task_list}")
        if not function or not task_list or len(task_list) == 0:
            et_log.error(f"save_task_job fail, function:{function} or task_list:{task_list} is empty")
            return
        for task in task_list:
            task_id = task.task_id
            existing_job = self.scheduler.get_job(task_id)
            if existing_job:
                # 修改已有的任务
                if TASK_CYCLE_DAY == task.cycle_type:# 天
                    self.scheduler.reschedule_job(task_id, trigger=task.trigger, hour=task.cycle_time.hour,
                                                  minute=task.cycle_time.minute, second=task.cycle_time.second,
                                                  args=params)
                elif TASK_CYCLE_WEEK == task.cycle_type:# 周
                    self.scheduler.reschedule_job(task_id, trigger=task.trigger, day_of_week=task.cycle_week,
                                                  hour=task.cycle_time.hour, minute=task.cycle_time.minute,
                                                  second=task.cycle_time.second, args=params)
                elif TASK_CYCLE_MONTH == task.cycle_type:# 月
                    self.scheduler.reschedule_job(task_id, trigger=task.trigger, day=task.cycle_day,
                                                  hour=task.cycle_time.hour, minute=task.cycle_time.minute,
                                                  second=task.cycle_time.second, args=params)
                elif TASK_CYCLE_CUSTOM == task.cycle_type:# 自定义
                    self.scheduler.reschedule_job(task_id, trigger=task.trigger, run_date=task.cycle_date, args=params)
            else:
                # 新增任务
                if TASK_CYCLE_DAY == task.cycle_type:# 天
                    self.scheduler.add_job(function, trigger=task.trigger, hour=task.cycle_time.hour,
                                                  minute=task.cycle_time.minute, second=task.cycle_time.second,
                                                  args=params, id=task_id)
                elif TASK_CYCLE_WEEK == task.cycle_type:# 周
                    self.scheduler.add_job(function, trigger=task.trigger, day_of_week=task.cycle_week,
                                                  hour=task.cycle_time.hour, minute=task.cycle_time.minute,
                                                  second=task.cycle_time.second, args=params, id=task_id)
                elif TASK_CYCLE_MONTH == task.cycle_type:# 月
                    self.scheduler.add_job(function, trigger=task.trigger, day=task.cycle_day,
                                                  hour=task.cycle_time.hour, minute=task.cycle_time.minute,
                                                  second=task.cycle_time.second, args=params, id=task_id)
                elif TASK_CYCLE_CUSTOM == task.cycle_type:# 自定义
                    self.scheduler.add_job(function, trigger=task.trigger, run_date=task.cycle_date, args=params, id=task_id)
