import json
import os
from io import BytesIO

from flask import Flask, request
from werkzeug.datastructures import EnvironHeaders

from com.exturing.ai.test.api.category_api import category
from com.exturing.ai.test.api.chat_box_api import chat_box
from com.exturing.ai.test.api.comm_prompt_template_api import comm_prompt
from com.exturing.ai.test.api.excel_api import excel_import
from com.exturing.ai.test.api.test_api import test
from com.exturing.ai.test.api.dic_api import dic_info
from com.exturing.ai.test.api.data_set_api import data_set
from com.exturing.ai.test.api.data_set_item_api import data_item
from com.exturing.ai.test.api.test_config_api import test_config
from com.exturing.ai.test.api.test_env_api import test_env
from com.exturing.ai.test.api.test_env_branch_api import test_env_branch
from com.exturing.ai.test.api.test_metric_calib_api import test_metric_calib
from com.exturing.ai.test.api.test_plan_api import test_plan
from com.exturing.ai.test.api.test_item_result_api import test_item_result
from com.exturing.ai.test.api.test_project_api import test_project
from com.exturing.ai.test.api.test_task_api import test_task
from com.exturing.ai.test.api.test_task_result_api import test_task_result
from com.exturing.ai.test.api.data_tag_api import data_tag
from com.exturing.ai.test.api.test_model_api import test_model
from com.exturing.ai.test.api.test_tool_api import test_tool
from com.exturing.ai.test.api.test_metric_api import test_metric
from com.exturing.ai.test.api.oem_api import oem
from com.exturing.ai.test.api.vehicle_api import vehicle
from com.exturing.ai.test.api.origin_data_item_api import origin_data_item
from com.exturing.ai.test.api.data_sync_api import data_sync
from com.exturing.ai.test.api.test_report_api import test_report
from com.exturing.ai.test.api.employee_api import employee
from com.exturing.ai.test.api.project_api import project
from com.exturing.ai.test.api.work_hour_api import work_hour
from com.exturing.ai.test.api.prompt_template_api import prompt_template
from com.exturing.ai.test.api.file_api import file
from com.exturing.ai.test.api.text_to_image_api import text_to_image
from com.exturing.ai.test.api.business_api import business
from com.exturing.ai.test.api.labor_cost_api import labor_cost
from com.exturing.ai.test.api.workforce_assessment_api import workforce_assessment
from com.exturing.ai.test.api.project_requirement_api import project_requirement
from com.exturing.ai.test.api.workforce_climb_api import workforce_climb
from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID, SYS_USER
from com.exturing.ai.test.comm.comm_util import decode_jwt_payload

from com.exturing.ai.test.scheduler.scheduler_main import SchedulerMain
from com.exturing.ai.test.service.test_task_service import TestTaskService

app = Flask(__name__)

urls = [
    test,
    excel_import,
    data_set,
    data_item,
    dic_info,
    test_plan,
    test_task,
    test_task_result,
    test_item_result,
    test_config,
    test_env,
    test_env_branch,
    test_project,
    data_tag,
    test_model,
    test_tool,
    test_metric,
    oem,
    vehicle,
    origin_data_item,
    data_sync,
    category,
    test_report,
    test_metric_calib,
    employee,
    project,
    work_hour,
    prompt_template,
    file,
    text_to_image,
    business,
    labor_cost,
    workforce_assessment,
    project_requirement,
    workforce_climb,
    chat_box,
    comm_prompt,
]  # 构建路由数组

for url in urls:
    app.register_blueprint(url)   #将路由均实现蓝图注册到主app应用上
# 定义过滤器函数
@app.before_request
def request_filter():

    # 这里可以添加请求过滤逻辑，例如检查请求头、请求参数等
    if 'Authorization' not in request.headers or len(request.headers["Authorization"]) == 0:
        return "Missing Authorization header", 401
    token = request.headers.get("Authorization")
    payload = decode_jwt_payload(token)
    if payload:
        payload_json = json.loads(payload)
        CTX_OEM_CODES.set(payload_json.get('oems', '') if payload_json else '')
        CTX_USER_ID.set(payload_json.get('user_id', SYS_USER) if payload_json else SYS_USER)
        # raw_data["oem_codes"] = "SMPV"
        # OEM_CODES.set("SMPV")

        # 检查请求的 Content-Type 是否为 application/json
        if request.content_type == 'application/json':
            # 获取原始请求数据
            raw_data = request.get_json()
            if isinstance(raw_data, dict):
                # 将请求头中token对应的相关数据重置入请求json结构体中
                raw_data["request_id"] = request.headers.get('x-request-id', '')
                raw_data["user_id"] = payload_json.get('user_id', 0) if payload_json else 0
                raw_data["username"] = payload_json.get('username', '') if payload_json else ''
                raw_data["remote_ip"] = request.headers.get('remoteip', '')
                raw_data["oem_codes"] = payload_json.get('oems', '') if payload_json else ''

                raw_data = json.dumps(raw_data).encode('utf-8')

                # 重新设置请求上下文
                request.environ['wsgi.input'] = BytesIO(raw_data)
                request.environ['CONTENT_LENGTH'] = str(len(raw_data))
                request.headers = EnvironHeaders(request.environ)


if __name__ == '__main__':
    scheduler = None
    if os.environ.get('WERKZEUG_RUN_MAIN') == 'true':# 检查当前进程是否为主进程
        scheduler = SchedulerMain.get_instance().scheduler
        # 初始化待执行的评测任务计划
        TestTaskService.init_task_jobs()
    try:
        # context = ('./word.willwakingking.com_bundle.crt', './word.willwaking.com.key')
        # app.run(host="0.0.0.0", port=5000, ssl_context=context)
        # logger = LogTool.configure_logger('/et-app.log', 'DEBUG')
        # et_log.info(f"LOG_PATH={LOG_PATH}")
        app.run(host="0.0.0.0", port=8080, debug=True)
    except (KeyboardInterrupt, SystemExit):
        if scheduler:
            scheduler.shutdown()
