import argparse
import traceback
from datetime import datetime, timedelta

import alibabacloud_oss_v2 as oss
import requests

from com.exturing.ai.test.comm.comm_constant import OSS_REGION, OSS_BUCKET, OSS_ENDPOINT
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode


class OssUtil:
    """
    阿里云OSS工具类
    """
    _args = None
    _client = None

    @classmethod
    def get_client(cls):
        """
        获取阿里云OSS Client
        :return:
        """
        if not cls._args:
            # 创建命令行参数解析器
            parser = argparse.ArgumentParser(description="put bucket sample")
            # 添加命令行参数 --region，表示存储空间所在的区域，必需参数
            # parser.add_argument('--region', default=OSS_REGION, help='The region in which the bucket is located.', required=True)
            parser.add_argument('--region', default=OSS_REGION, help='The region in which the bucket is located.')
            # 添加命令行参数 --bucket，表示存储空间的名称，必需参数
            # parser.add_argument('--bucket', default=OSS_BUCKET, help='The name of the bucket.', required=True)
            parser.add_argument('--bucket', default=OSS_BUCKET, help='The name of the bucket.')
            # 添加命令行参数 --endpoint，表示其他服务可用来访问OSS的域名，非必需参数
            parser.add_argument('--endpoint', default=OSS_ENDPOINT, help='The domain names that other services can use to access OSS')
            cls._args = parser.parse_args([])  # 解析命令行参数

        if not cls._client:
            # 从环境变量中加载凭证信息，用于身份验证
            credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()

            # 加载SDK的默认配置，并设置凭证提供者
            cfg = oss.config.load_default()
            cfg.credentials_provider = credentials_provider
            # 设置配置中的区域信息
            cfg.region = cls._args.region
            # 如果提供了endpoint参数，则设置配置中的endpoint
            if cls._args.endpoint is not None:
                cfg.endpoint = cls._args.endpoint

            # 使用配置好的信息创建OSS客户端
            cls._client = oss.Client(cfg)
        return cls._client

    @classmethod
    def upload_file(cls, upload_file, upload_key):
        """
        上传文件到OSS upload_key对应的目录
        :param upload_file: 待上传文件
        :param upload_key: 上传的目标目录
        :return:上传成功后的文件URL，失败=""
        """
        # et_log.info(f"OSS upload_file file:{upload_file.name} upload_path:{upload_key}")
        try:
            # 执行上传对象的请求，指定存储空间名称、对象名称和数据内容
            result = cls.get_client().put_object(oss.PutObjectRequest(
                bucket=cls._args.bucket,
                key=upload_key,
                body=upload_file.read(),
            ))

            # 输出请求的结果状态码、请求ID、内容MD5、ETag、CRC64校验码和版本ID，用于检查请求是否成功
            et_log.info(f'OSS upload_file status code: {result.status_code},'
                  f' request id: {result.request_id},'
                  f' content md5: {result.content_md5},'
                  f' etag: {result.etag},'
                  f' hash crc64: {result.hash_crc64},'
                  f' version id: {result.version_id},'
                  )

            if ResultCode.SUCCESS.code == result.status_code:
                upload_url = cls.get_file_key_url(upload_key)
                # upload_url = f"https://{cls._args.bucket}.{cls._args.endpoint}/{upload_key}"
                et_log.info(f"upload_file success, upload_url:{upload_url.url}")
                return upload_url.url
        except Exception as e:
            et_log.error(f"OSS upload_file exception:{e}")
            traceback.print_exc()
            return ""

    @classmethod
    def download_file_stream(cls, file_key):
        """
        获取file_key对应的OSS文件bytes
        :param file_key:OSS中对应文件的目录
        :return:文件的字节流bytes
        """
        et_log.info(f"OSS download_file_stream file_key:{file_key}")
        try:
            # 执行获取对象的请求，指定存储空间名称和对象名称
            result = cls.get_client().get_object(oss.GetObjectRequest(
                bucket=cls._args.bucket,  # 指定存储空间名称
                key=file_key,  # 指定对象键名
            ))

            # 输出获取对象的结果信息，用于检查请求是否成功
            et_log.info(f'status code: {result.status_code},'
                  f' request id: {result.request_id},'
                  f' content length: {result.content_length},'
                  f' content range: {result.content_range},'
                  f' content type: {result.content_type},'
                  f' etag: {result.etag},'
                  f' last modified: {result.last_modified},'
                  f' content md5: {result.content_md5},'
                  f' cache control: {result.cache_control},'
                  f' content disposition: {result.content_disposition},'
                  f' content encoding: {result.content_encoding},'
                  f' expires: {result.expires},'
                  f' hash crc64: {result.hash_crc64},'
                  f' storage class: {result.storage_class},'
                  f' object type: {result.object_type},'
                  f' version id: {result.version_id},'
                  f' tagging count: {result.tagging_count},'
                  f' server side encryption: {result.server_side_encryption},'
                  f' server side data encryption: {result.server_side_data_encryption},'
                  f' next append position: {result.next_append_position},'
                  f' expiration: {result.expiration},'
                  f' restore: {result.restore},'
                  f' process status: {result.process_status},'
                  f' delete marker: {result.delete_marker},'
                  )
            if result.body and result.body.read():
                return result.body.read()
            return None
        except Exception as e:
            et_log.error(f"OSS download_file_2path exception:{e}")
            traceback.print_exc()
            return None


    @classmethod
    def download_file_2path(cls, file_key, download_path):
        """
        下载file_key对应的OSS文件，至download_path目录
        :param file_key: OSS文件的目录
        :param download_path: 下载至本地保存文件的目录
        :return:下载成功=True|失败=False
        """
        et_log.info(f"OSS download_file_2path file_key:{file_key} download_path:{download_path}")
        try:
            file_bytes = cls.download_file_stream(file_key)
            if not file_bytes:
                et_log.error(f"download_file_2path error, not found file_key:{file_key}")
                return False

            et_log.info(f"download_file_2path 文件读取完成，数据长度：{len(file_bytes)} bytes")

            # path = "./get-object-sample.txt"
            with open(download_path, 'wb') as down_file:
                down_file.write(file_bytes)
            et_log.info(f"download_file_2path 文件下载完成，保存至路径：{download_path}")
            return True
        except Exception as e:
            et_log.error(f"OSS download_file_2path exception:{e}")
            traceback.print_exc()
            return False

    @classmethod
    def upload_url_file(cls, file_url, upload_key):
        """
        上传网络文件到OSS upload_key对应的目录
        :param file_url: 待上传文件URL
        :param upload_key: 上传的目标目录
        :return:上传成功后的文件URL，失败=""
        """
        et_log.info(f"upload_url_file file_url:{file_url} upload_key:{upload_key}")
        try:
            # 发送HTTP GET请求，获取响应内容
            response = requests.get(file_url)
            # 执行上传对象的请求，指定存储空间名称、对象名称和数据内容
            result = cls.get_client().put_object(oss.PutObjectRequest(
                bucket=cls._args.bucket,
                key=upload_key,
                body=response.content,
            ))

            # 输出请求的结果状态码、请求ID、内容MD5、ETag、CRC64校验码和版本ID，用于检查请求是否成功
            et_log.info(f'upload_url_file status code: {result.status_code},'
                  f' request id: {result.request_id},'
                  f' content md5: {result.content_md5},'
                  f' etag: {result.etag},'
                  f' hash crc64: {result.hash_crc64},'
                  f' version id: {result.version_id},'
                  )

            if ResultCode.SUCCESS.code == result.status_code:
                # upload_url = f"https://{cls._args.bucket}.oss-{cls._args.endpoint}/{upload_key}"
                upload_url = cls.get_file_key_url(upload_key)
                url=upload_url.url
                et_log.info(f"upload_url_file success, upload_url:{url}")
                return upload_url.url
            return ""
        except Exception as e:
            et_log.error(f"OSS download_file_2path exception:{e}")
            traceback.print_exc()
            return ""

    @classmethod
    def get_file_key_url(cls, file_key, expiration_days=7):
        """
        根据file_key获取目录文件的临时访问URL(默认7天，即不过期)
        :param file_key: 文件的OSS目录
        :param expiration_days: 生成的URL有效期
        :return:生成的临时访问URL
        """
        et_log.info(f"get_file_key_url file_key:{file_key} expiration_days:{expiration_days}")
        try:
            # 生成文件的临时访问 URL
            # expires = timedelta(days=expiration_days)
            # TODO 待阿里云月底版本修复该问题
            expires_second = 3600*3
            et_log.info(f"get_file_key_url expires:{expires_second} ")
            sign_url_request = cls.get_client().presign(oss.GetObjectRequest(
                                                                bucket=cls._args.bucket,
                                                                key=file_key,
                                                                # response_expires= str(int((datetime.now()+ timedelta(days=expiration_days)).timestamp()))
                                                            ),
                                expires=timedelta(seconds=expires_second),
            )
            et_log.info(f"get_file_key_url success, sign_url_request:{sign_url_request.url}")
            return sign_url_request
        except Exception as e:
            et_log.error(f"OSS get_file_key_url exception:{e}")
            traceback.print_exc()
            return ""

        
# if __name__ == "__main__":

    # 上传文件
    # with open(r"C:\work\test_eval\文生图\电信\1.png", 'rb') as file:
    #     OssUtil.upload_file(file, "test_ai_eval/images/test.png")
    #
    # # 下载文件
    # OssUtil.download_file_2path(r"test_ai_eval/images/test.png", r"C:\work\test_eval\文生图\电信\oss_test.png")

    # # 上传网络文件
    # OssUtil.upload_url_file(r"http://106.63.5.45:19099/lm/picture/20250214/2674fbd2-ea79-11ef-b162-fa163e481f16.png",
    #                         r"test_ai_eval/images/SLJL.png")
    #
    # OssUtil.get_file_key_url("test_ai_eval/images/test.png")
    # OssUtil.get_file_key_url("test_ai_eval/images/SLJL.png", 265)

    # img_url = OssUtil.upload_url_file(r"https://dashscope-result-bj.oss-cn-beijing.aliyuncs.com/1d/bd/20250415/52b0b9b6/8b756fb3-1bd8-4288-8616-55eb55d7de99-1.png?Expires=1744792967&OSSAccessKeyId=LTAI5tQZd8AEcZX6KZV4G8qL&Signature=roPPMvnSrqwHMB2NTI9p7zk9A5U%3D",
    #                         r"test_ai_eval/images/202504151813-8b756fb3-1bd8-4288-8616-55eb55d7de99-2.png")
    # print(img_url)