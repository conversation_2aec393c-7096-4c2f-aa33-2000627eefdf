import json
import re
import traceback

import requests

from com.exturing.ai.test.comm.comm_constant import QIANFANG_ACCESS_TOKEN_URL, QIANFANG_SIMILARITY_URL
from com.exturing.ai.test.comm.comm_util import extract_json_objects
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_env_service import TestEnvService


# 百度千帆评估智能体
class EvalQianFangAgent:

    # 百度千帆
    @classmethod
    def get_access_token(cls):
        """
        获取百度千帆 access_token
        """
        response = requests.post(QIANFANG_ACCESS_TOKEN_URL)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            raise Exception(f"获取 access_token 失败：{response.text}")

    @classmethod
    def compute_similarity(cls, prompt_data):
        et_log.info(f"compute_similarity prompt_data:{prompt_data}")
        """
        调用流式API接口计算语义相似度
        """
        try:
            access_token = cls.get_access_token()  # 替换为实际的访问令牌
            et_log.info(f"compute_similarity get_access_token:{access_token}")
            url = QIANFANG_SIMILARITY_URL.format(**{"ACCESS_TOKEN":access_token})
            payload = json.dumps({
                "messages": [
                    {
                        "role": "user",
                        "content": prompt_data
                    }
                ],
                "stream": True
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.post(url, headers=headers, data=payload, stream=True)
            if response.status_code == 200:
                # 收集流式响应结果
                full_text = ""
                for line in response.iter_lines():
                    if line:
                        try:
                            decoded_line = line.decode("UTF-8")
                            json_data = json.loads(decoded_line.replace("data: ", ""))
                            if "result" in json_data:
                                full_text += json_data["result"]
                        except json.JSONDecodeError:
                            continue
                et_log.info(f"compute_similarity full_text:{full_text}")
                return full_text
            else:
                raise Exception(f"API调用失败，HTTP状态码: {response.status_code}, 错误信息: {response.text}")
        except Exception as e:
            et_log.error(f"compute_similarity exception:{e},\n{traceback.print_exc()}")

    @classmethod
    def get_response_score(cls, response_text):
        et_log.info(f"get_response_score response_text:{response_text}")
        try:
            # 定义正则表达式模式
            pattern = r'<output>(\d+)</output>'
            # 使用 re.search 查找匹配项
            match = re.search(pattern, response_text)

            if match:
                # 提取匹配到的数字
                score = int(match.group(1))
                et_log.info(f"get_response_score response_text:{response_text} score:{score}")
                return score
            else:
                et_log.error(f"get_response_score response_text:{response_text} not find score")
                return 0
        except Exception as e:
            et_log.error(f"get_response_score exception:{e},\n{traceback.print_exc()}")
            return 0

    @classmethod
    def compare_text_semantics(cls, question, expected_answer, actual_answer):
        """
        根据问题question、期望回答expected_answer、实际回答actual_answer，对实际回答进行语义相似度打分
        :param question: 问题
        :param expected_answer: 期望回答
        :param actual_answer: 实际回答
        :return:语义相似度分值，100分制
        """
        compare_data = """
        你是一个评分专家：请根据问题，判断实际回答与期望回答的语义相似性，并能准确回答问题，请以100分制对实际回答进行打分；如果无期望回答，则根据问题和实际回答的相关性，对实际回答的回答内容进行打分即可。
        参考样例：
            问题:请对电影《天若有情》进行评价？
            期望回答：这部电影的剧情非常感人，我看得热泪盈眶。
            实际回答：这部电影的情节十分动人，我感动得流下了眼泪。
            {{"eval_score":100, "score_reason":"评分原因"}}

            问题:请对电影《天若有情》进行评价？
            期望回答：
            实际回答：这部电影的情节十分动人，我感动得流下了眼泪。
            {{"eval_score":100, "score_reason":"评分原因"}}

        请将结果以{{"eval_score":100, "score_reason":"评分原因"}}的JSON格式返回，请对下述内容进行打分。
        问题:{question}
        期望回答:{answer1}
        实际回答:{answer2}
        """
        params = {"question":question, "answer1":expected_answer, "answer2":actual_answer}
        prompt_data = compare_data.format(**params)
        result = cls.compute_similarity(prompt_data)
        score_arr = extract_json_objects(result)
        if not score_arr or len(score_arr) < 1:
            et_log.error(f"compare_text_semantics ai_result:{result} not find score")
            return 0, "未获取到评估结果"
        return score_arr[0].get("eval_score", 0), score_arr[0].get("score_reason", "未获取到评估结果")

    @classmethod
    def eval_by_pt(cls, prompt_template:str, question, ex_answer, au_answer):
        """
        根据提示词模板内容，对问题和回答，参考期望回答进行评分
        :param prompt_template:提示词模板内容
        :param question: 问题
        :param ex_answer: 期望回答
        :param au_answer: 实际回答
        :return:score评分|score_reason评分原因
        """
        et_log.info(f"eval_by_pt question:{question} pt:{prompt_template} ex_answer:{ex_answer} au_answer:{au_answer}")
        try:
            params = {"question": question, "answer1": ex_answer, "answer2": au_answer}
            prompt_data = prompt_template.format(**params)
            result = cls.compute_similarity(prompt_data)
            score_arr = extract_json_objects(result)
            if not score_arr or len(score_arr) < 1:
                et_log.error(f"eval_by_pt ai_result:{result} not find score")
                return 0, "未获取到评估结果"
            return score_arr[0].get("eval_score", 0), score_arr[0].get("score_reason", "未获取到评估结果")
        except Exception as e:
            et_log.error(f"eval_by_pt execute exception:{e}\n{traceback.print_stack()}")
            return 0, "未获取到评估结果"

    @classmethod
    def eval_by_env_code(cls, env_code, question, ex_answer, au_answer):
        """
        根据环境代码对应的评估提示词模板（若未找到，使用默认评估提示词模板），对问题和回答进行评估
        :param env_code: 环境代码
        :param question: 问题
        :param ex_answer: 期望回答
        :param au_answer: 实际回答
        :return:score评分|score_reason评分原因
        """
        et_log.info(f"eval_by_env_code env_code:{env_code} question:{question} ex_answer:{ex_answer} au_answer:{au_answer}")
        try:
            params = {"question": question, "answer1": ex_answer, "answer2": au_answer}
            # 根据环境代码找对应提示词模板，若无则用默认评分提示词模板
            eval_tmp:CommPromptTemplateModel = TestEnvService.get_comm_pt_by_code(env_code)
            # prompt_data = eval_tmp.tmp_content.format(**params)
            return cls.eval_by_pt(eval_tmp.tmp_content, question, ex_answer, au_answer)
        except Exception as e:
            et_log.error(f"eval_by_env_code execute exception:{e}\n{traceback.print_stack()}")
            return 0, "未获取到评估结果"

    @classmethod
    def eval_by_tp_code(cls, task_id:str, env_code:str, question, ex_answer, au_answer):
        """
        首先：根据任务id，获取任务上是否有配置评分提示词模板代码eval_pt_code。
        若有，则用任务配置的评分模板；
        若无，则从任务对应的环境中获取配置的评分提示词模板代码eval_pt_code；
        若也无，则采用默认的评分提示词模板，对问题、期望回答、回答进行打分
        :param task_id: 任务id
        :param env_code: 环境代码
        :param question: 问题
        :param ex_answer: 期望回答
        :param au_answer: 实际回答
        :return:score评分|score_reason评分原因
        """
        et_log.info(f"eval_by_tp_code task_id:{task_id} env_code:{env_code} question:{question} ex_answer:{ex_answer} au_answer:{au_answer}")
        try:
            # 从任务中获取评分提示词模板，进行评分
            if task_id and len(str(task_id)) > 0:
                task:TestTask = TestTask(**TestTask.find_by_pk(str(task_id)))
                if task and task.eval_pt_code and len(task.eval_pt_code.strip()) > 0:
                    comm_pt:CommPromptTemplateModel = CommPromptTemplateModel.query_by_tmp_code(task.eval_pt_code)
                    if comm_pt and comm_pt.tmp_content and len(comm_pt.tmp_content) > 0:
                        return cls.eval_by_pt(comm_pt.tmp_content, question, ex_answer, au_answer)
            # 从通道环境中获取评分提示词模板，进行评分（若无，则会取默认）
            return cls.eval_by_env_code(env_code, question, ex_answer, au_answer)
        except Exception as e:
            et_log.error(f"eval_by_tp_code execute exception:{e}\n{traceback.print_stack()}")
            return 0, "未获取到评估结果"




# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我钟情于节奏轻快的流行音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我喜欢古典音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"", "answer2":"我喜欢古典音乐。"}
# print(EvalQianFangAgent.compare_text_semantics("你喜欢什么类型的音乐？", "", "我喜欢古典音乐。"))
# print(EvalQianFangAgent.compare_text_semantics("上海迪士尼的地址是哪里？", "上海市浦东新区川沙新镇黄赵路310号", "上海市浦东新区"))

