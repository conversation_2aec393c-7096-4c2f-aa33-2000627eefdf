
from flask import Blueprint
from com.exturing.ai.test.comm.api_result import <PERSON>pi<PERSON><PERSON>ult
from com.exturing.ai.test.model.origin_data_item import OriginData<PERSON>temModel
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
import math

data_sync = Blueprint('data_sync', __name__)

# 将data_set_item 数据同步到origin_data_item并去重 (单轮)
@data_sync.route(f'/{URL_PREFIX}/data_sync/origin_data_item', methods=['POST'])
def origin_data_item_sync():
    et_log.info("############origin_data_item_sync################")
    
    data_item = MongoDBUtil.find_condition_page("et_data_set_item", {"is_del": 0, "is_last": 1, "parent_id": ""}, None, 0, 100000)

    data_item_list = [MongoDBUtil.serialize_document(doc) for doc in list(data_item or [])]

    insert_list = []
    insert_q_list = []

    for item in data_item_list:
        if isinstance(item['question'], float) and math.isnan(item['question']):
            item['question'] = ""

        if item["question"] is not None and item["question"] not in insert_q_list:
            insert_q_list.append(item["question"])
            insert_list.append(OriginDataItemModel(**item).model_dump())

    MongoDBUtil.insert_many("origin_data_item", insert_list)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, True).to_json()