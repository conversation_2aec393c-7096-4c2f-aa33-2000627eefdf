# service.py
from bson import ObjectId
from com.exturing.ai.test.model.labor_cost import LaborCostModel, LaborCostQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log

_doc = "labor_cost"


# 新增人力成本
def create(data: LaborCostModel) -> str:
    assert not check_cost_item_exists(data), "人力成本项已存在"

    data_dict = data.model_dump()
    et_log.info(f"create labor_cost: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询人力成本
def query_page(
    page_num: int = 1, page_size: int = 10, query: LaborCostQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 修改人力成本
def update(id: str, data: LaborCostModel) -> bool:
    assert not check_cost_item_exists(data, id), "人力成本项已存在"

    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update labor_cost: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除人力成本
def delete(id: str) -> bool:
    et_log.info(f"delete labor_cost by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 检查人力成本是否已存在
def check_cost_item_exists(data: LaborCostModel, id: str = None) -> bool:
    check_keys = ["type", "role", "base", "level"]

    condition = {}

    if id:
        condition["_id"] = {"$ne": ObjectId(id)}

    for key in check_keys:
        if hasattr(data, key):
            value = getattr(data, key)
            if value:
                condition[key] = value

    count = MongoDBUtil.find_count(_doc, condition)
    return count > 0
