import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID
from com.exturing.ai.test.comm.comm_util import list_to_tree
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_metric_calib import TestMetricCalibModel
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.service.data_item_service import DataItemService
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.service.data_tag_service import _doc as data_tag_doc
from com.exturing.ai.test.comm.condition_util import model_dimension_condition
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
import pandas as pd
from io import BytesIO


# 测试任务结果项Service
class TestItemResultService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"test_result insert_one post_data:{post_data}")
        try:
            set_item_dic = EtDataSetItem.find_by_pk(post_data["data_set_item_id"])
            if not set_item_dic:
                et_log.error(f"test_result insert_one error, data_set_item_id is invalid")
                return None
            set_item = EtDataSetItem(**set_item_dic)

            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            task_id = post_data["task_id"]
            do_user = post_data.get("do_user", 1)
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestResult("", current_time, do_user, current_time, do_user, 0, ctx_oem_codes,
                                   post_data["parent_id"], task_id, set_item.id, set_item.question,
                                   post_data.get("actual_task", ""), post_data.get("actual_category", ""),
                                   post_data.get("result_category", ""), post_data.get("actual_answer", ""),
                                   post_data.get("result_answer", ""), post_data.get("answer_score", ""),
                                   post_data.get("qa_recall", ""),post_data.get("first_res_time", ""),
                                   # post_data["qa_keywords"],
                                   post_data.get("qa_use_time", ""), post_data.get("result_final", ""),
                                   "", "", "", set_item.is_last, set_item.expected_task,
                                   set_item.expected_category, set_item.expected_answer,
                                   post_data.get("recall_id", ""), post_data.get("task_result_id", ""),
                                   set_item.model_id, set_item.dimension_id, post_data.get("remark", ""),
                                   post_data.get("re_url", ""), post_data.get("re_cmd", ""), post_data.get("re_error_id", ""),
                                   post_data.get("re_interval_time", ""), "","","",post_data.get("is_websearch", ""),
                                   post_data.get("result_task", 0), post_data.get("ad_result_task", 0), ctx_oem_codes,
                                   post_data.get("data_tags", ""), post_data.get("metric_calib", ""),
                                   post_data.get("eval_config_id", "")).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"test_result insert_one exception")
            traceback.print_exc()
            return None

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = TestResult.find_by_pk(_id)
        if data_item is not None:
            return TestResult(**data_item)
        return None

    # 查询 分页数据 此中只取一级语料 TODO 可能搜索的语料是非一级的，导致列表无法查询到数据
    @classmethod
    def query_page(cls, task_result_id, question, result_final, ad_result_final, model_id, dimension_id, model_dimension, is_websearch, data_tags, page_num: int, page_size: int):
        et_log.info(f"test_task_item_result query_page params task_result_id:{task_result_id} question:{question} result_final:{result_final} "
                    f"model_id:{model_id} dimension_id:{dimension_id} page_num:{page_num} page_size:{page_size}")
        try:
            condition = {"task_result_id": ObjectId(str(task_result_id).strip())}
            if result_final is not None:
                condition["result_final"] = result_final
            if ad_result_final is not None:
                condition["ad_result_final"] = ad_result_final
            if question and len(str(question).strip()) > 0:
                condition["question"] = {"$regex": str(question).strip(), "$options": "i"}
            if model_id and len(str(model_id).strip()) > 0:
                condition["model_id"] = {"$in": [str(model_id).strip(), ObjectId(str(model_id).strip())]}
            if dimension_id and len(str(dimension_id).strip()) > 0:
                condition["dimension_id"] = {"$in": [str(dimension_id).strip(), ObjectId(str(dimension_id).strip())]}
            condition["parent_id"] = ""
            if is_websearch is not None:
                condition["is_websearch"] = is_websearch
            if data_tags is not None and len(data_tags) > 0:
                regex_conditions = [{"data_tags": {"$regex": f"\\b{tag_id}\\b"}} for tag_id in data_tags]
                condition["$or"] = regex_conditions

            model_dimension_condition(condition, model_dimension)

            # 查询匹配的数量
            total = TestResult.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            result_list = TestResult.find_condition(condition, None, page_size, page.skip)
            if result_list is None or len(result_list) < 1:
                et_log.error(f"test_task_item_result query_page not found data error")
                return None
            item_json_list = []
            for item in result_list:# 返回集合转json格式
                item_json = item.to_json_str()
                # 补充数据集明细中的qa_keywords
                if "data_set_item_id" in item_json and len(item_json.get("data_set_item_id", "")) > 0:
                    item_id = item_json.get("data_set_item_id", "")
                    set_item = EtDataSetItem.find_by_pk(item_id)
                    if set_item:
                        item_json["qa_keywords"] = set_item.get("qa_keywords", "")
                item_json_list.append(item_json)
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_task_item_result query_page exception")
            traceback.print_exc()
            return None

    # 删除数据 TODO 只能删除一级语料结果
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_task_item_result delete by _id:{_id} do_user:{do_user}")
        try:
            del_item:TestResult = cls.find_pk(_id)
            if del_item is None:
                et_log.error(f"test_task_item_result delete by _id error, _id not found data")
                return False

            # 删除id对应的数据集记录
            TestResult.delete_by_id(_id, do_user)
            if 1 != del_item.is_last:# 如果删除的语料子级语料
                condition = {"parent_id": ObjectId(_id)}
                count = TestResult.find_condition_count(condition)
                result_list = TestResult.find_condition({"parent_id": ObjectId(_id)}, None, count, 0)
                for test_result in result_list:# 迭代子数据项，通过回归删除子项结果
                    cls.delete(test_result.id, do_user)
            # TODO 任务结果 数据重计算
            return True
        except Exception as e:
            et_log.error(f"test_task_item_result delete by _id exception")
            traceback.print_exc()
            return False

    # 更新
    @classmethod
    def update(cls, post_data:TestResult):
        et_log.info(f"test_task_item_result update by post_data:{post_data}")
        try:
            # 更新-主键检查
            result = TestResult.find_by_pk(post_data.id)
            if result is None:
                et_log.error(f"update test_task_item_result error, _id not found data")
                return 0
            # 同步更新数据集数据项
            cls.revise_result_update_dataset_item(TestResult(**result), post_data)
            # 处理指标标定
            metric_calib = cls.rebuild_metric_calib(post_data.metric_calib)
            post_data.metric_calib = metric_calib
            # 更新请求的数据项数据
            return TestResult.update_entity_json(post_data.to_json())
        except Exception as e:
            et_log.error(f"update by test_task_item_result exception")
            traceback.print_exc()
            return 0

    @classmethod
    def revise_result_update_dataset_item(cls, update_data: TestResult, post_data: TestResult):
        """
        检查结果项是否变更；若变更，且是对应数据集数据项创建人处理，则同步修改数据集数据项内容
        :param update_data: 待更新的评测结果数据项（原对象）
        :param post_data: 变更的数据对象（更新参数对象）
        :return:
        """
        # 检查参数是否是空值 空值=Ture
        model_empty = post_data.model_id is None or post_data.model_id.strip() == ""
        dimension_empty = post_data.dimension_id is None or post_data.dimension_id.strip() == ""
        exp_task_empty = post_data.expected_task is None or post_data.model_id.strip() == ""
        exp_category_empty = post_data.expected_category is None or post_data.expected_category.strip() == ""
        exp_answer_empty = post_data.expected_answer is None or post_data.expected_answer.strip() == ""
        if model_empty and dimension_empty and exp_task_empty and exp_category_empty and exp_answer_empty:
            et_log.error(f"revise_result_update_dataset_item not change,model_empty:{model_empty} dimension_empty:{dimension_empty} "
                         f"exp_task_empty:{exp_task_empty} exp_category_empty:{exp_category_empty} exp_answer_empty:{exp_answer_empty}")
            return

        # 检查更新结果人与数据集数据项创建人是否相同，相同才可更新
        data_set_item_id = str(update_data.data_set_item_id)
        set_item_row = EtDataSetItem.find_by_pk(data_set_item_id)
        if not set_item_row:
            et_log.error(f"revise_result_update_dataset_item data_set_item_id:{data_set_item_id} not found data")
            return

        if set_item_row.get("create_by") != CTX_USER_ID.get():
            et_log.error(f"revise_result_update_dataset_item current user:{CTX_USER_ID.get()} not same set_item create user")
            return

        set_item = EtDataSetItem(**set_item_row)
        set_data = {}
        if not model_empty and str(post_data.model_id) != str("" if set_item.model_id is None else set_item.model_id):
            set_data["model_id"] = ObjectId(str(post_data.model_id))
        if not dimension_empty and str(post_data.dimension_id) != str("" if set_item.dimension_id is None else set_item.dimension_id):
            set_data["dimension_id"] = ObjectId(str(post_data.dimension_id))
        if not exp_task_empty and str(post_data.expected_task) != str("" if set_item.expected_task is None else set_item.expected_task):
            set_data["expected_task"] = post_data.expected_task
        if not exp_category_empty and str(post_data.expected_category) != str("" if set_item.expected_category is None else set_item.expected_category):
            set_data["expected_category"] = post_data.expected_category
        if not exp_answer_empty and str(post_data.expected_answer) != str("" if set_item.expected_answer is None else set_item.expected_answer):
            set_data["expected_answer"] = post_data.expected_answer
        if not set_data:
            et_log.error(f"revise_result_update_dataset_item up pro same now pro by EtDataSetItem")
            return
        set_data["_id"] = data_set_item_id
        update_result = EtDataSetItem.update_entity_json(set_data)
        et_log.info(f"revise_result_update_dataset_item update {update_result.modified_count} rows EtDataSetItem")
        if update_result.modified_count > 0:
            # 检查是否更新对应的原数据 model_id|dimension_id|expected_task|expected_category|expected_answer
            EtDataSetItem.revise_dataset_update_origin_item(data_set_item_id, set_data)

    @classmethod
    def rebuild_metric_calib(cls, mc_values):
        """
        重构指标标定集合
        :param mc_values:表单提交的指标标定数组
        :return: 重构后添加了指标名称的指标标定数组
        """
        et_log.info(f"rebuild_metric_calib mc_values:{mc_values}")
        try:
            rebuild_metric_calib = []
            if mc_values is None or len(mc_values) == 0:
                return rebuild_metric_calib
            for mc in mc_values:
                if mc is None or mc.get("mc_id") is None or mc.get("value") is None:
                    continue
                mc_data = TestMetricCalibModel.find_by_pk(mc.get("mc_id"))
                if mc_data is None:
                    continue
                mc["metric_name"] = mc_data.metric_name
                rebuild_metric_calib.append(mc)
            return rebuild_metric_calib
        except Exception as e:
            et_log.error(f"rebuild_metric_calib exception:{e},\n{traceback.print_exc()}")
            return []

    @classmethod
    def update_batch(cls, result_item_ids, post_data):
        et_log.info(f"test_task_item_result update_batch by post_data:{post_data}")
        try:
            # 处理指标标定
            metric_calib = cls.rebuild_metric_calib(post_data["metric_calib"])
            post_data["metric_calib"] = metric_calib
            # 更新请求的数据项数据
            return TestResult.update_batch_json(result_item_ids, post_data)
        except Exception as e:
            et_log.error(f"update_batch by test_task_item_result exception:{e},\n{traceback.print_exc()}")
            return 0

    # 根据父数据项id,获取其下所有子结果数据列表
    @classmethod
    def query_child_list(cls, item_pid):
        et_log.info(f"query_child_list param item_pid:{item_pid}")
        try:
            json_list = []
            count = TestResult.find_count_by_pid(item_pid)
            if count < 1:
                return json_list

            result_list = TestResult.find_by_pid(item_pid)
            if result_list and len(result_list) > 0:
                for item in result_list:
                    json_list.append(item.to_json_str())
                    child_list = cls.query_child_list(item.id)
                    if child_list and len(child_list) > 0:
                        json_list.extend(child_list)
            return  json_list
        except Exception as e:
            et_log.error(f"query_child_list query test_task_item_result child list exception")
            traceback.print_exc()
            return []

    # 根据task_result_id查询数据
    @classmethod
    def find_by_task_result_id(cls, task_result_id):
        et_log.info(f"find_by_task_result_id query_page params task_result_id:{task_result_id}")
        try:
            # 只根据 task_result_id 和 parent_id 查询
            condition = {
                "task_id": ObjectId(str(task_result_id).strip()),
                # "parent_id": ""  # 只需要根据 task_result_id 和 parent_id 查询
            }

            # 查询匹配的数量
            total = TestResult.find_condition_count(condition)
            et_log.info(f"find_by_task_result_id total:{total}")

            if total < 1:  # 未匹配到结果
                return None

            result_list = TestResult.find_by_task_result_id(condition)

            if result_list is None or len(result_list) < 1:
                et_log.error(f"test_task_item_result query_page not found data error")
                return None

            return result_list  # 直接返回所有结果，不需要分页

        except Exception as e:
            et_log.error(f"test_task_item_result query_page exception")
            traceback.print_exc()
            return None

    @classmethod
    def export_data(cls, task_result_id, export_keys = []):
        result = MongoDBUtil.find_condition('test_result', {"task_result_id": ObjectId(task_result_id)})
        if result is None:
            return None

        # 获取模型和维度数据
        models = DicService.query_list(None, None, "dm-model") or []
        dimensions = DicService.query_list(None, None, "dm-dimension") or []
        tags = MongoDBUtil.find_condition(data_tag_doc, {"is_del":0})

        data_list = list(result or [])
        data_list_json = [MongoDBUtil.serialize_document(doc) for doc in data_list]
        model_list = list(models or [])
        dimension_list = list(dimensions or [])
        tag_list = list(tags or [])

        for data in data_list_json:
            # 获取模型和维度名称
            if data.get("model_id"):
                data['model_name'] = [item.get("name") for item in model_list if item.get("_id") == data.get("model_id")][0]

            if data.get("dimension_id"):
                data['dimension_name'] = [item.get("name") for item in dimension_list if item.get("_id") == data.get("dimension_id")][0]

                if data.get('model_name') is None:
                    data['model_name'] = [item.get("name") for item in model_list if item.get("_id") == [item.get("pid") for item in dimension_list if item.get("_id") == data.get("dimension_id")][0]][0]

            # 获取标签名称
            if data.get("data_tags"):
                data_tag_ids = data.get("data_tags").split(',')
                data['data_tag_names'] = ','.join([item.get("tag_name") for item in tag_list if str(item.get("_id")) in data_tag_ids])

        export_list = []
        data_tree_json = list_to_tree(data_list_json)
        for data in data_tree_json:
            index = 1
            export_item = {}
            export_item = cls.formatData(data, index, export_item, export_keys)

            # 查询子轮数据并格式化
            if data.get("is_last") != 1:
                children = data.get("children", [])
                for child in children:
                    index += 1
                    export_item = cls.formatData(child, index, export_item, export_keys)

            export_list.append(export_item)

        # 导出数据
        export_df = pd.DataFrame(export_list)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            export_df.to_excel(writer, sheet_name='结果明细数据', index=False)

        excel_binary = output.getvalue()
        output.close()

        return excel_binary

    @classmethod
    def formatData(cls, data_item, index, export_item = {}, export_keys = []):
        export_all = export_keys is None or len(export_keys) < 1

        if export_all or "data_src" in export_keys:
            export_item[f'data_src'] = data_item.get("data_src")
        if export_all or 'model' in export_keys:
            export_item[f'Q{index}_model'] = data_item.get("model_name")
        if export_all or 'dimension' in export_keys:
            export_item[f'Q{index}_dimension'] = data_item.get("dimension_name")
        if export_all or 'question' in export_keys:
            export_item[f'Q{index}'] = data_item.get("question")

        if export_all or 'expected_answer' in export_keys:
            export_item[f'A{index}_expected'] = data_item.get("expected_answer")
        if export_all or 'actual_answer' in export_keys:
            export_item[f'A{index}_actual'] = data_item.get("actual_answer")
        if export_all or 'result_answer' in export_keys:
            export_item[f'A{index}_result'] = cls.getPassFail(data_item.get("result_answer"))
        if export_all or 'ad_result_answer' in export_keys:
            export_item[f'A{index}_result_ad'] = cls.getPassFail(data_item.get("ad_result_answer"))

        if export_all or 'expected_task' in export_keys:
            export_item[f'Q{index}_task_expected'] = data_item.get("expected_task")
        if export_all or 'actual_task' in export_keys:  
            export_item[f'Q{index}_task_actual'] = data_item.get("actual_task")
        if export_all or 'result_task' in export_keys:
            export_item[f'Q{index}_task_result'] = cls.getPassFail(data_item.get("result_task"))
        if export_all or 'ad_result_task' in export_keys:
            export_item[f'Q{index}_task_result_ad'] = cls.getPassFail(data_item.get("ad_result_task"))

        if export_all or 'expected_category' in export_keys:
            export_item[f'Q{index}_category_expected'] = data_item.get("expected_category")
        if export_all or 'actual_category' in export_keys:
            export_item[f'Q{index}_category_actual'] = data_item.get("actual_category")
        if export_all or 'result_category' in export_keys:
            export_item[f'Q{index}_result'] = cls.getPassFail(data_item.get("result_category"))
        if export_all or 'ad_result_category' in export_keys:
            export_item[f'Q{index}_result_ad'] = cls.getPassFail(data_item.get("ad_result_category"))

        if export_all or 'expected_url' in export_keys:
            export_item[f'Q{index}_url'] = data_item.get("re_url_q")
        if export_all or 're_url' in export_keys:
            export_item[f'A{index}_url'] = data_item.get("re_url")

        if export_all or 'expected_cmd' in export_keys:
            export_item[f'Q{index}_cmd'] = data_item.get("re_cmd_q")
        if export_all or 're_cmd' in export_keys:
            export_item[f'A{index}_cmd'] = data_item.get("re_cmd")

        if export_all or 'expected_error_id' in export_keys:
            export_item[f'Q{index}_errorId'] = data_item.get("re_error_id_q")
        if export_all or 're_error_id' in export_keys:
            export_item[f'A{index}_errorId'] = data_item.get("re_error_id")

        if export_all or 'result_final' in export_keys:
            export_item[f'QA{index}_final_result'] = cls.getPassFail(data_item.get("result_final"))
        if export_all or 'ad_result_final' in export_keys:
            export_item[f'QA{index}_final_result_ad'] = cls.getPassFail(data_item.get("ad_result_final"))

        if export_all or 'recall_id' in export_keys:
            export_item[f'A{index}_recall_id'] = data_item.get("recall_id")
        if export_all or 'qa_use_time' in export_keys:
            export_item[f'QA{index}_consume_time'] = data_item.get("qa_use_time")
        if export_all or 're_interval_time' in export_keys:
            export_item[f'A{index}_interval_time'] = data_item.get("re_interval_time")
        if export_all or 'is_websearch' in export_keys:
            export_item[f'A{index}_websearch'] = data_item.get("is_websearch")
        if export_all or "qa_keywords" in export_keys:
            export_item[f'Q{index}_keyword'] = data_item.get("qa_keywords")
        if export_all or 'tags' in export_keys:
            export_item[f'A{index}_tags'] = data_item.get("data_tag_names")
        export_item[f"Q{index}_first_res_time"] = data_item.get("first_res_time")

        return export_item

    @classmethod
    def getPassFail(cls, result):
        if result == 1:
            return 'Pass'
        elif result == 0:
            return 'Fail'

        return ''


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))
