import hashlib
from typing import IO
from werkzeug.datastructures import FileStorage
from com.exturing.ai.test.comm.comm_constant import UPLOAD_FILE_PATH
from com.exturing.ai.test.comm.oss_util import OssUtil


def upload(file: FileStorage):
    if not file:
        return ""

    file_md5 = get_file_md5(file.stream)
    ext = file.filename.split(".")[-1]
    filename = file_md5
    if ext:
        filename = f"{file_md5}.{ext}"

    oss_url = None
    # 判断文件是否已经上传过
    # oss_url = OssUtil.get_file_key_url(f"{UPLOAD_FILE_PATH}{filename}")

    # 如果文件没有上传过，则上传文件
    if not oss_url:
        # 上传文件
        oss_url = OssUtil.upload_file(file.stream, f"{UPLOAD_FILE_PATH}{filename}")

    if not oss_url:
        return ""

    return oss_url.url


# 获取文件的md5值
def get_file_md5(file: IO[bytes]) -> str:
    md5_hash = hashlib.md5()
    # 保存当前文件流的位置
    original_position = file.tell()
    # 将文件流位置重置到开头
    file.seek(0)
    # 逐块读取文件，避免大文件占用过多内存
    for chunk in iter(lambda: file.read(4096), b""):
        md5_hash.update(chunk)

    # 恢复文件流位置
    file.seek(original_position)

    return md5_hash.hexdigest()
