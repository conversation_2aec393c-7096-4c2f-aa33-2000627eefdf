import requests
import json

class BaiduTranslateAPI:
    def __init__(self):
        """
        初始化方法
        """
        # 直接在类中写死 API_KEY 和 SECRET_KEY
        self.API_KEY = "LjdjpvXXrwsWmdOSPj1mxbeX"
        self.SECRET_KEY = "BgUbIQ47iUPmveroNxhTKsuydYbUcIFj"
        self.access_token = None

    def get_access_token(self):
        """
        获取 access_token
        """
        url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={self.API_KEY}&client_secret={self.SECRET_KEY}"
        response = requests.post(url)
        if response.status_code == 200:
            self.access_token = response.json().get("access_token")
        else:
            raise Exception(f"获取 access_token 失败：{response.text}")

    def data_gen(self, prompt_data):
        """
        调用百度 API 进行流式响应处理

        :param prompt_data: 输入的提示数据
        :return: 流式响应的完整结果
        """
        if not self.access_token:
            self.get_access_token()

        url = f"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-128k?access_token={self.access_token}"

        payload = json.dumps({
            "messages": [
                {
                    "role": "user",
                    "content": prompt_data
                }
            ],
            "stream": True
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.post(url, headers=headers, data=payload, stream=True)
        if response.status_code == 200:
            # 收集流式响应结果
            full_text = ""
            for line in response.iter_lines():
                if line:
                    try:
                        decoded_line = line.decode("UTF-8")
                        json_data = json.loads(decoded_line.replace("data: ", ""))
                        if "result" in json_data:
                            full_text += json_data["result"]
                    except json.JSONDecodeError:
                        continue
            return full_text
        else:
            raise Exception(f"API调用失败，HTTP状态码: {response.status_code}, 错误信息: {response.text}")

    def analyze_corpus(self, question):
        """
        分析语料并返回翻译结果

        :param question: 需要翻译的中文文本
        :return: 翻译后的英文文本
        """
        prompt_data = (
            f"请将下面数据翻译成英文，最终输出结果只需要英文生成结果"
            f"{question}\n"
        )
        try:
            result = self.data_gen(prompt_data)
            print(f"________________{result}")
            return result
        except Exception as e:
            print(f"翻译失败: {e}")
            return {"Construction_Manner": "N/A", "Amount_of_corpus_data": "N/A", "Model": "N/A", "Project": "N/A",
                    "Test_Object": "N/A"}  # 如果计算失败，返回错误信息

# 示例用法
if __name__ == "__main__":
    # 创建类实例
    baidu_api = BaiduTranslateAPI()
    # 示例语料
    corpus = "中国很强大"
    result = baidu_api.analyze_corpus(corpus)
    print(result)