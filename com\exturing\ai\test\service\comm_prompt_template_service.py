import traceback

from com.exturing.ai.test.comm.comm_constant import PROMPT_TMP_STATUS_ENABLE, PROMPT_TMP_EVAL_CODE
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_env_service import TestEnvService


class CommPromptTemplateService:
    """
    通用 提示词模板Service
    """

    @classmethod
    def create(cls, tmp_model:CommPromptTemplateModel):
        """
        创建提示词模板
        :param tmp_model: 提示词集合表对象model
        :return: 成功新增后的id
        """
        et_log.info(f"create comm_prompt_template: {None if tmp_model is None else tmp_model.model_dump()}")
        try:
            if not CommPromptTemplateModel.check_fk("", tmp_model.tmp_name, tmp_model.tmp_code):
                et_log.error(f"create comm_prompt_template tmp_name:{tmp_model.tmp_name} tmp_code:{tmp_model.tmp_code} check fail")
                return None
            return tmp_model.insert_one()
        except Exception as e:
            et_log.error(f"create comm_prompt_template exception:{e},\n{traceback.print_exc()}")
            return None

    @classmethod
    def query_page(cls, page_num: int = 1, page_size: int = 10, query: CommPromptTemplateModel = None):
        """
        提示词模板查询分页
        :param page_num:页码 默认=1
        :param page_size: 每页记录数 默认=10
        :param query: 查询参数 名称、代码、状态条件
        :return: 匹配到的结果分页对象
        """
        et_log.info(f"query_page comm_prompt_template query: {None if query is None else query.model_dump()}")
        try:
            condition = {}
            tmp_name = condition.get("tmp_name", "").strip()
            if tmp_name and len(tmp_name) > 0:
                condition["tmp_name"] = {"$regex": tmp_name, "$options": "i"}
            tmp_code = condition.get("tmp_code", "").strip()
            if tmp_code and len(tmp_code) > 0:
                condition["tmp_code"] = {"$regex": tmp_code, "$options": "i"}
            total = CommPromptTemplateModel.find_condition_count(condition)
            page = PageResult(page_num, page_size, total)
            if total > 0:
                result = CommPromptTemplateModel.find_page_list(condition, None, page.skip, page_size)
                result_list = list(result or [])
                json_list = [data.serialize_model() for data in result_list]
                page.page_data = json_list
            return page
        except Exception as e:
            et_log.error(f"query_page comm_prompt_template exception:{e},\n{traceback.print_exc()}")
            return None

    @classmethod
    def update(cls, tmp_id: str, data: CommPromptTemplateModel) -> bool:
        """
        修改提示词模板
        :param tmp_id: 模板id
        :param data: 模板修改的内容
        :return: 修改是否成功 Ture=成功|False=失败
        """
        try:
            data_dict = data.model_dump()
            data_dict['_id'] = tmp_id
            if not CommPromptTemplateModel.check_fk(tmp_id, data.tmp_name, data.tmp_code):
                et_log.error(f"update comm_prompt_template tmp_id:{tmp_id} tmp_name:{data.tmp_name} tmp_code:{data.tmp_code} check fail")
                return False
            et_log.info(f"update comm_prompt_template: {data_dict}")
            update_num = CommPromptTemplateModel.update_entity_json(data_dict)
            if update_num.modified_count and update_num.modified_count > 0:
                return True
            else:
                return False
        except Exception as e:
            et_log.error(f"update comm_prompt_template exception:{e},\n{traceback.print_exc()}")
            return False

    @classmethod
    def delete(cls, tmp_id: str) -> bool:
        """
        删除提示词模板。若提示词
        :param tmp_id:提示词模板id
        :return: 删除数据的数量
        """
        et_log.info(f"delete comm_prompt_template by tmp_id:{tmp_id}")
        try:
            tmp_data = CommPromptTemplateModel.find_by_pk(tmp_id)
            if not tmp_data:
                et_log.error(f"delete comm_prompt_template fail, tmp_id:{tmp_id} not found data")
                return False
            if PROMPT_TMP_STATUS_ENABLE == tmp_data.tmp_status:
                et_log.error(f"delete comm_prompt_template fail, tmp_id:{tmp_id} status is enable")
                return False
            if PROMPT_TMP_EVAL_CODE == tmp_data.tmp_code:
                et_log.error(f"delete comm_prompt_template fail, tmp_id:{tmp_id} is default template")
                return False

            del_count = CommPromptTemplateModel.delete_by_id(tmp_id, 0) > 0
            return True if del_count > 0 else False
        except Exception as e:
            et_log.error(f"delete comm_prompt_template exception:{e},\n{traceback.print_exc()}")
            return False

    @classmethod
    def get_eval_pt(cls, task_id:str, env_code:str):
        """
        首先：根据任务id，获取任务上是否有配置评分提示词模板代码eval_pt_code。
        若有，则用任务配置的评分模板；
        若无，则从任务对应的环境中获取配置的评分提示词模板代码eval_pt_code；
        若也无，则采用默认的评分提示词模板
        :param task_id: 任务id
        :param env_code: 环境代码
        :return:评分提示词模板
        """
        et_log.info(f"get_eval_pt task_id:{task_id} env_code:{env_code}")
        try:
            # 从任务中获取评分提示词模板
            if task_id and len(task_id) > 0:
                task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
                if task and task.eval_pt_code and len(task.eval_pt_code.strip()) > 0:
                    comm_pt:CommPromptTemplateModel = CommPromptTemplateModel.query_by_tmp_code(task.eval_pt_code)
                    if comm_pt and comm_pt.tmp_content and len(comm_pt.tmp_content) > 0:
                        return comm_pt.tmp_content

            # 根据环境代码找对应提示词模板
            if env_code and len(env_code) > 0:
                # 根据env_code查找对应任务的评测通道环境
                env_list = TestEnv.find_condition({"env_code": env_code}, None, None, None)
                if env_list and len(env_list) > 0:
                    env:TestEnv = env_list[0]
                    # 根据环境上的评分模板，获取评分提示词模板内容
                    if env.tmp_id and len(env.tmp_id) > 0:
                        eval_tmp:CommPromptTemplateModel = CommPromptTemplateModel.find_by_pk(env.tmp_id)
                        if eval_tmp and eval_tmp.tmp_content and len(eval_tmp.tmp_content) > 0:
                            return eval_tmp.tmp_content
            # 用默认评分提示词模板
            eval_tmp:CommPromptTemplateModel = CommPromptTemplateModel.query_by_tmp_code(PROMPT_TMP_EVAL_CODE)
            if eval_tmp and eval_tmp.tmp_content and len(eval_tmp.tmp_content) > 0:
                return eval_tmp.tmp_content
            return None
        except Exception as e:
            et_log.error(f"get_eval_pt execute exception:{e}\n{traceback.print_stack()}")
            return None