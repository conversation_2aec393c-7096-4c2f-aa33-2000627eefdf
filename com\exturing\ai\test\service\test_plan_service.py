import asyncio
import re
import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, AUTO_TEST_SEL, AUTO_TEST_TAG, \
    CTX_USER_ID, DIC_CODE_MODEL, TEST_TASK_FAIL, TEST_TASK_INIT
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.send_mail_util import SendMailUtil
from com.exturing.ai.test.dto.params.auto_test_dto import AutoTestDto
from com.exturing.ai.test.mail.test_report_html import task_result_report_html
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.dic_info import DicInfo
from com.exturing.ai.test.model.et_data_set import EtDataSet
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.model.test_plan import TestPlan
from com.exturing.ai.test.model.test_project import TestProject
from com.exturing.ai.test.model.test_report import AccuracyReportGroupQueryModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_result import TestTaskResult
from com.exturing.ai.test.service.data_item_service import DataItemService
from com.exturing.ai.test.service.data_set_service import DataSetService
from com.exturing.ai.test.service.dic_service import DicService
from com.exturing.ai.test.service.origin_data_item_service import create, check_question
from com.exturing.ai.test.service.test_report_service import query_accuracy_rate_group
from com.exturing.ai.test.service.test_task_service import TestTaskService
from com.exturing.ai.test.tool.corpus_analysis import CorpusAnalysis
# from com.exturing.ai.test.tool.corpus_analysis import CorpusAnalysis
from com.exturing.ai.test.tool.data_auto_generate import CorpusGenerator
from com.exturing.ai.test.service.test_config_service import _doc as test_config_doc


# 测试计划Service
class TestPlanService:

    # 新增测试计划
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"insert_one test_plan post_data:{post_data}")
        try:
            # 插入当前请求数据
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            plan_name = post_data["plan_name"]
            bol_val = cls.check_name(plan_name)
            if bol_val:
                et_log.error(f"insert_one test_plan error, plan_name has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestPlan("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                             ctx_oem_codes, post_data["project_id"], plan_name, post_data["plan_status"], post_data["start_time"],
                             post_data["end_time"], post_data["desc"]).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"insert_one test_plan exception")
            traceback.print_exc()
            return None

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        test_plan = TestPlan.find_by_pk(_id)
        if test_plan is not None:
            return TestPlan(**test_plan)
        return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, plan_name, plan_status, create_from_time, create_to_time, create_by, page_num: int, page_size: int):
        et_log.info(f"test_plan query_page params plan_name:{plan_name} plan_status:{plan_status} create_from_time:{create_from_time}"
                    f" create_to_time:{create_to_time} create_by: {create_by} page_num:{page_num} page_size:{page_size}")
        try:
            condition = {}
            if plan_name and len(str(plan_name).strip()) > 0:
                condition["plan_name"] = {"$regex": str(plan_name).strip(), "$options": "i"}
            if plan_status:
                condition["plan_status"] = plan_status
            if create_by:
                condition["create_by"] = create_by
            create_time_filter = {}
            if create_from_time and len(str(create_from_time).strip()) > 0:
                create_time_filter["$gte"] = str(create_from_time).strip()
            if create_to_time and len(str(create_to_time).strip()) > 0:
                create_time_filter["$lte"] = str(create_to_time).strip()
            if create_time_filter is not None and len(create_time_filter) > 0:
                condition["create_time"] = create_time_filter
            # 查询匹配的数量
            total = TestPlan.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            # # TODO 演示临时添加
            # sorts = [("create_time", pymongo.ASCENDING)]
            # plan_list = TestPlan.find_condition(condition, sorts, page_size, page.skip)
            plan_list = TestPlan.find_condition(condition, None, page_size, page.skip)
            if plan_list is None or len(plan_list) < 1:
                et_log.error(f"test_plan query_page not found data error")
                return None
            item_json_list = []
            for item in plan_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_plan query_page exception")
            traceback.print_exc()
            return None

    # 删除
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_plan delete by _id:{_id} do_user:{do_user}")
        try:
            del_item:TestPlan = TestPlan.find_by_pk(_id)
            if del_item is None:
                et_log.error(f"test_plan delete by _id error, _id not found item")
                return False
            # 删除id对应的数据
            TestPlan.delete_by_id(_id, do_user)
            # 根据id检查，该数据项是否有子节点，若有则需更新=所有子节点都逻辑删除
            TestTask.del_by_pid(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"test_plan delete by _id exception")
            traceback.print_exc()
            return False

    # 更新数据
    @classmethod
    def update(cls, post_data:TestPlan):
        et_log.info(f"test_plan update by post_data:{post_data}")
        try:
            condition = {}
            if post_data and post_data.plan_name and len(str(post_data.plan_name).strip()) > 0:
                condition["plan_name"] = str(post_data.plan_name).strip()

            plan_pk:TestPlan = TestPlanService.find_pk(post_data.id)
            if plan_pk is None:
                et_log.error(f"test_plan_update error, plan_id is invalid")
                return 0
            # 检查数据是否重复
            items = TestPlan.find_condition(condition, None, 100, 0)
            if items and len(items) > 0:
                for data_em in items:
                    if str(post_data.id) != str(data_em.id):# 检查表明有重复的问题
                        et_log.error(f"test_plan error, repeat question")
                        return 0
            # 更新请求的数据项数据
            return TestPlan.update_entity_json(post_data.to_json())
        except Exception as e:
            et_log.error(f"test_plan update exception")
            traceback.print_exc()
            return 0

    # 检查计划名称（重复性检查）
    @classmethod
    def check_name(cls, plan_name):
        try:
            condition = {"plan_name": str(plan_name).strip()}
            total = TestPlan.find_condition_count(condition)
            if total > 0:
                return True
            else:
                return False
        except Exception as e:
            et_log.error(f"check_name plan_name exception")
            traceback.print_exc()
            return False

    @classmethod
    def auto_test(cls, describe_auto, oems, user_id):
        """
        根据描述describe_auto自动生成数据集，自动进行评测，并发送评测结果邮件
        :param describe_auto: 自动评测描述
        :return: 成功=Ture|失败=False
        """
        et_log.info(f"auto_test describe_auto:{describe_auto}")
        try:
            CTX_OEM_CODES.set(oems)
            CTX_USER_ID.set(user_id)
            # auto_dic = chat_speak(describe_auto)
            # 评测意图识别
            result_data = {"auto_status":0, "auto_status_desc": f"评测意图识别中...", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
            if not describe_auto or len(describe_auto) == 0:
                et_log.error(f"test_auto_eval error, describe_auto is empty")
                result_data = {"auto_status": 0, "auto_status_desc": f"自动评测描述为空",
                               "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            auto_dic = CorpusAnalysis().gen_ai_result(describe_auto, {})
            et_log.info(f"auto_test auto_dic:{auto_dic}")
            auto_dic = cls.find_auto_dic_by_prompt(describe_auto, auto_dic)
            et_log.info(f"auto_test find_auto_dic_by_prompt auto_dic:{auto_dic}")

            if (not auto_dic or len(auto_dic.get("construction_manner", "")) == 0
                    or "N/A" == auto_dic.get("construction_manner")
                    or auto_dic.get("amount_of_corpus_data", 0) == 0
                    or "N/A" == auto_dic.get("amount_of_corpus_data")
                    or len(auto_dic.get("model", "")) == 0
                    or "N/A" == auto_dic.get("model")
                    or len(auto_dic.get("project", "")) == 0
                    or "N/A" == auto_dic.get("project")
                    or len(auto_dic.get("test_object", "")) == 0
                    or "N/A" == auto_dic.get("test_object")):
                et_log.error(f"auto_test param is invalid:{auto_dic} ")
                # 评测意图识别 失败
                result_data = {"auto_status":0, "auto_status_desc": f"评测意图识别失败", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            # 评测意图识别成功
            result_data = {"auto_status":0, "auto_status_desc": f"评测意图识别成功", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
            auto_dto = AutoTestDto(**auto_dic)

            # 新增测试计划
            plan_name = AUTO_TEST_TAG + auto_dto.construction_manner + auto_dto.model + datetime.now().strftime("%Y%m%d%H%M%S") + "自动计划"
            p_list = TestProject.find_condition({"project_name":{"$regex": str(auto_dto.project).strip(), "$options": "i"}},None, -1, 0)
            project_id = ""
            if p_list and len(p_list) > 0:
                project_id = p_list[0].id
            test_plan = {"plan_name": plan_name, "project_id": project_id, "start_time":"", "end_time":"", "desc":"",
                         "do_user":CTX_USER_ID.get(), "plan_status":1}
            plan_id = cls.insert_one(test_plan)
            if not plan_id:
                et_log.error(f"auto_test insert test_plan fail")
                # 返回评测计划创建失败
                result_data = {"auto_status":1, "auto_status_desc": f"评测计划创建失败", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            et_log.info(f"auto_test [test_plan] plan_name:{plan_name} plan_id:{str(plan_id)}")
            # 返回评测计划已创建
            result_data = {"auto_status":1, "auto_status_desc": f"评测计划已创建[{plan_name}]", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()

            # 新增测试集
            set_name = AUTO_TEST_TAG + auto_dto.construction_manner + auto_dto.model + datetime.now().strftime("%Y%m%d%H%M%S") + "自动数据集"
            set_id = cls.auto_test_data_set(auto_dto.construction_manner, auto_dto.model, auto_dto.amount_of_corpus_data, set_name)
            if not set_id or len(set_id) == 0:
                et_log.error(f"auto_test insert et_data_set fail")
                # 返回评测集创建失败
                result_data = {"auto_status":2, "auto_status_desc": f"新增评测集失败", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            et_log.info(f"auto_test [et_data_set] set_name:{set_name} set_id:{str(set_id)}")
            # 返回评测集创建成功
            result_data = {"auto_status":2, "auto_status_desc": f"新增评测集成功[{set_name}]", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()

            # 新增测试任务
            task_name = AUTO_TEST_TAG + auto_dto.construction_manner + auto_dto.model + datetime.now().strftime("%Y%m%d%H%M%S") + "自动任务"
            test_config = cls.get_auto_test_config(auto_dto.project, auto_dto.test_object)
            if test_config is None:
                et_log.error(f"auto_test match test_config fail, project:{auto_dto.project} test_object:{auto_dto.test_object}")
                # 返回评测任务创建失败
                result_data = {"auto_status":3, "auto_status_desc": f"新增评测任务失败#未查询到评测通道#", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            config_id = str(test_config.get("_id"))
            task_id = TestTask("", "", None, "", None, 0, 
                               CTX_OEM_CODES.get(), plan_id, "", set_id, task_name, 1, 0, "", None, None, False, "", config_id).insert_one()
            if not task_id or len(str(task_id)) == 0:
                et_log.error(f"auto_test insert test_task fail")
                # 返回评测任务创建失败
                result_data = {"auto_status":3, "auto_status_desc": f"新增评测任务失败#写入数据异常#", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            et_log.info(f"auto_test [test_task] task_name:{task_name} task_id:{str(task_id)}")
            # 返回评测任务创建成功
            result_data = {"auto_status": 3, "auto_status_desc": f"新增评测任务成功[{task_name}]", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()

            # 运行任务
            # 返回评测任务执行中
            result_data = {"auto_status": 4, "auto_status_desc": f"评测任务执行中", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
            task_result_id = TestTaskService.run_task(str(task_id), CTX_USER_ID.get(), CTX_OEM_CODES.get(), CTX_USER_ID.get())
            if task_result_id is None:
                et_log.error(f"auto_test run task fail,task_id:{str(task_id)}")
                # 返回评测任务执行失败
                result_data = {"auto_status":4, "auto_status_desc": f"评测任务执行失败", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
                return
            # 返回评测任务执行失败
            result_data = {"auto_status":4, "auto_status_desc": f"评测任务执行成功", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()

            # 发送评测报告邮件
            asyncio.run(cls.send_report_mail(str(task_result_id), task_name, auto_dto.model))
            result_data = {"auto_status": 5, "auto_status_desc": f"评测报告已发送", "ctime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            yield f"""data: {str(result_data).replace("'", '"')}\n\n""".encode()
            return
        except Exception as e:
            et_log.error(f"auto_test exception:{e}")
            traceback.print_exc()
            return

    @classmethod
    def auto_test_data_set(cls, auto_type, model_name, data_size, set_name):
        """
        自动生成数据集
        :param auto_type: 生成方式：自动=模型生成|选择=根据条件从原始语料随机
        :param model_name: 模块名称
        :param data_size: 生成的记录数
        :param set_name: 自动生成的任务名称
        :return: 数据集ID（str）
        """
        et_log.info(f"auto_test_data_set auto_type:{auto_type} model_name:{model_name} data_size:{data_size}")
        try:
            dic_list = DicInfo.find_condition({"name": {"$regex": model_name, "$options": "i"}, "group_code": DIC_CODE_MODEL}, None, -1, 0)
            model_id = None
            if dic_list and len(dic_list) > 0:
                model_id = str(dic_list[0].id)
            # 模块名称未匹配，则新增一条模块数据
            if not model_id or len(model_id) == 0:
                model_id = DicInfo("", "", None, "", None, 0,
                                    CTX_OEM_CODES.get(), "", model_name, "", model_name, DIC_CODE_MODEL,
                                    0, "模块", ).insert_one()
                if not model_id:
                    et_log.error(f"auto_test_data_set insert model dic_info fail")
                    return ""
            data_set_id = ""
            if auto_type in AUTO_TEST_SEL:# 选择=从原始语料中，根据条件随机选择
                data_set_id = DataSetService.auto_build_dataset([{"model_id": str(model_id), "size": data_size}], set_name)
            else:# 自动=模型生成指定条数的语料
                ai_result = CorpusGenerator().analyze_corpus(f"帮我生成{data_size}条{model_name}的语料")
                et_log.info(f"auto_test_data_set auto ai_result:{ai_result}")
                if ai_result and "data" in ai_result and "corpus" in ai_result["data"] and len(ai_result["data"]["corpus"]) > 0:
                    data_set_id = EtDataSet("", "", 0, "", 0, 0, "",
                                       set_name, "", "", "", "", 1, 0, "").insert_one()
                    data_set_id = str(data_set_id)
                    for item in ai_result["data"]["corpus"]:
                        item_data = {"set_id":data_set_id, "question": item["question"], "parent_id":"", "model_id": str(model_id),
                                     "dimension_id":"", "metric_id":"", "item_type":0, "scene_id":"", "expected_answer": item["answer"],
                                     "expected_task":"", "expected_category":"", "item_tags":"", "real_time":"", "data_src":"",
                                     "is_last":1, "do_user": CTX_USER_ID.get()}
                        DataItemService.insert_one(item_data)

                        # 如果自动生成的语料原始语料表中无此语料，则新增一条原始语料
                        origin_count = check_question(item["question"])
                        if origin_count == 0:
                            origin_dic = {
                                "model_id": str(model_id),
                                "dimension_id": "",
                                "question": item["question"],
                                "expected_answer": item["answer"],
                                "expected_task": "",
                                "expected_category": "",
                                "is_last": 1
                            }
                            origin_data = OriginDataItemModel(**origin_dic)
                            create(origin_data)
                    set_count = EtDataSetItem.find_condition_count({"data_set_id":ObjectId(data_set_id)})
                    EtDataSet.update_set_total(data_set_id, set_count)
            et_log.info(f"auto_test_data_set data_set_id:{data_set_id}")
            return data_set_id
        except Exception as e:
            et_log.error(f"auto_test_data_set exception:{e}")
            traceback.print_exc()
            return ""

    @classmethod
    def get_auto_test_config(cls, project_name, test_object):
        """
        根据项目名称和评测对象，获取对应的评测通道对象
        :param project_name: 项目名称
        :param test_object: 评测对象
        :return: 评测通道对象
        """
        et_log.info(f"get_auto_test_config project_name:{project_name} test_object:{test_object}")
        try:
            name = project_name + "-" + test_object
            condition = {"name": {"$regex": name, "$options": "i"}}
            result = MongoDBUtil.find_condition(test_config_doc, condition)
            config_list = list(result or [])
            if config_list and len(config_list) > 0:
                return config_list[0]
            else:
                return None
        except Exception as e:
            et_log.error(f"get_auto_test_config exception:{e}")
            traceback.print_exc()
            return None

    @classmethod
    async def send_report_mail(cls, task_result_id, task_name, model_name):
        """
        发送单任务的评测报告
        :param task_result_id: 任务结果id
        :param task_name: 任务名称
        :param model_name: 模块名称
        :return:
        """
        et_log.info(f"send_report_mail task_result_id:{task_result_id} task_name:{task_name} model_name:{model_name}")
        try:
            result_row = TestTaskResult.find_by_pk(task_result_id)
            if not result_row:
                et_log.error(f"send_report_mail fail, task_result_id:{task_result_id} not found data")
                return False

            task_result = TestTaskResult(**result_row)
            if TEST_TASK_FAIL == task_result.result_status:# 失败不发送报告
                et_log.error(f"send_report_mail fail, task(task_result_id:{task_result_id}) run fail")
                return False
            if TEST_TASK_INIT == task_result.result_status:
                await asyncio.sleep(30) # 每30秒查一次结果，直至获取到任务执行成功
                await cls.send_report_mail(task_result_id, task_name, model_name)

            report_query = AccuracyReportGroupQueryModel()
            report_query.task_result_id = str(task_result_id)
            report_list = query_accuracy_rate_group(report_query)
            if not report_list or len(report_list) == 0:
                et_log.error(f"send_report_mail fail, task(task_result_id:{task_result_id}) not found report data")
                return False

            data = report_list[0]
            if not data or 0 == data["result_item_count"]:# 空结果清单，不发送邮件
                et_log.error(f"send_report_mail fail, task(task_result_id:{task_result_id}) result_item is empty")
                return False

            html_tr_list = f"""<tr>
                <td>{task_name}</td>
                <td>{model_name}</td>
                <td>{data["result_item_count"]}</td>
                <td>{data["pass_count"]}/{data["result_item_count"]-data["pass_count"]}</td>
                <td>{data["ad_pass_count"]}/{data["result_item_count"]-data["ad_pass_count"]}</td>
                <td>{data["accuracy_rate"]}%</td>
                <td>{data["ad_accuracy_rate"]}%</td>
                <td>0/{data["result_item_count"]}</td>
                <td>0/{data["result_item_count"]}</td>
                <td>0.00%</td>
                <td>0.00%</td>
                <td>{data["answer_pass_count"]}/{data["result_item_count"] - data["answer_pass_count"]}</td>
                <td>{data["ad_answer_pass_count"]}/{data["result_item_count"] - data["ad_answer_pass_count"]}</td>
                <td>{data["answer_accuracy_rate"]}%</td>
                <td>{data["ad_answer_accuracy_rate"]}%</td>
            </tr>
                """
            send_html = task_result_report_html.substitute(task_result_list_report=html_tr_list)
            subject = "[报告]" + task_name
            SendMailUtil.send_default_html(["<EMAIL>"], subject, send_html)
            et_log.info(f"send_report_mail {subject} success")
        except Exception as e:
            et_log.error(f"send_report_mail exception:{e}")
            traceback.print_exc()
            return False

    @classmethod
    def find_auto_dic_by_prompt(cls, prompt: str, auto_dic: dict):
        """
        根据描述解析出自动评测需要的参数
        此函数为大模型解析失败后的兜底策略，如果大模型解析成功，则使用大模型解析出的参数数据
        :param prompt: 描述内容
        :return: auto_dic
        """

        if not auto_dic:
            auto_dic = {}

        # 数据集生成方式只能为“自动”或“选择”，如不在二者中间，则默认为“选择”
        if (
            auto_dic.get("construction_manner") != "自动"
            and auto_dic.get("construction_manner") != "选择"
        ):
            auto_dic["construction_manner"] = "选择"

        # 生成数量如不存在，则使用正则到prompt中找数字作为生成数量，如找不到，则默认为10条
        if not auto_dic.get("amount_of_corpus_data") or "N/A" == auto_dic.get(
            "amount_of_corpus_data"
        ):
            amounts = re.findall(r"\d+", prompt)
            auto_dic["amount_of_corpus_data"] = (
                int(amounts[0]) if amounts and len(amounts) > 0 else 10
            )

        # 模块如果不存在则通过prompt在现有模块中匹配，如匹配不到，则默认为“闲聊及百科大回答”
        if not auto_dic.get("model") or "N/A" == auto_dic.get("model"):
            models = DicService.query_list(None, None, "dm-model") or []
            model_names = [item.get("name") for item in list(models or [])]
            find_model = False
            for model_name in model_names:
                if prompt.find(model_name) > -1:
                    auto_dic["model"] = model_name
                    find_model = True
                    break
            if not find_model:
                auto_dic["model"] = "闲聊及百科大回答"

        # 项目如果不存在，则默认为“长城”
        if not auto_dic.get("project") or "N/A" == auto_dic.get("project"):
            auto_dic["project"] = "长城"

        # 评测对象如果不存在，则默认为“agent”
        if not auto_dic.get("test_object") or "N/A" == auto_dic.get("test_object"):
            auto_dic["test_object"] = "agent"

        return auto_dic


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))
