from enum import Enum


class ResponseEnum(Enum):
    """
    自定义异常枚举
    """
    SUCCESS = (200, "请求成功")
    PARAM_ERROR = (401, "参数错误")
    SERVICE_ERROR = (501, "服务处理错误")

    @classmethod
    def get_all(cls):
        return [element for element in cls]

    @classmethod
    def get_enum_by_code(cls, code):
        for element in cls:
            if element.value[0] == code:
                return element
        return None

    @property
    def get_code(self):
        return self.value[0]

    @property
    def get_msg(self):
        return self.value[1]