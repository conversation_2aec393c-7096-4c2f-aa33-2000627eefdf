from flask import Blueprint, request

from com.exturing.ai.test.dto.params.metric_calib_dto import MetricCalibDto
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.test_metric_calib import TestMetricCalibModel
from com.exturing.ai.test.service.test_metric_calib_service import TestMetricCalibService

test_metric_calib = Blueprint('test_metric_calib', __name__)

# 新增指标
@test_metric_calib.route(f'/{URL_PREFIX}/test-metric-calib/create', methods=['POST'])
def test_metric_calib_create():
    et_log.info("############test_metric_calib_create################")
    req_data = request.get_json()
    if 'metric_name' not in req_data and len(str(req_data["metric_name"]).strip()) == 0:
        et_log.error(f"test_metric_calib_create error, metric_name is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "metric_name is null", "").to_json()
    insert_instance = TestMetricCalibModel(**req_data)
    test_metric_id = TestMetricCalibService.create(insert_instance)
    if test_metric_id is None:
        ApiResult(ResultCode.FAILURE.code, "test_metric_calib_create fail", "").to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(test_metric_id)).to_json()

# 指标分页查询
@test_metric_calib.route(f'/{URL_PREFIX}/test-metric-calib/page', methods=['POST'])
def test_metric_calib_page():
    et_log.info("############test_metric_calib_page################")
    data = request.get_json()
    query = MetricCalibDto(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = TestMetricCalibService.query_page(page_num, page_size, query)
    if page is None:
        return ApiResult(ResultCode.FAILURE.code, "test_metric_calib_page fail", "").to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改指标
@test_metric_calib.route(f'/{URL_PREFIX}/test-metric-calib/update', methods=['POST'])
def test_metric_calib_update():
    et_log.info("############test_metric_calib_update################")
    req_data = request.get_json()
    id = req_data.get("metric_id")
    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "metric_id is null", "").to_json()
    update_instance = TestMetricCalibModel(**req_data)
    update_result = TestMetricCalibService.update(id, update_instance)
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, {"result": update_result}).to_json()

# 删除指标
@test_metric_calib.route(f'/{URL_PREFIX}/test-metric-calib/del', methods=['POST'])
def test_metric_calib_delete():
    et_log.info("############test_metric_calib_delete################")
    req_data = request.get_json()
    metric_id = req_data.get("metric_id")
    if metric_id is None or len(metric_id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "metric_id is null", "").to_json()
    
    delete_result = TestMetricCalibService.delete(metric_id)
    if delete_result > 0:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()
    else:
        return ApiResult(ResultCode.FAILURE.code, "test_metric_calib_delete fail", delete_result).to_json()