from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class TestToolModel(BaseDataModel):
    tool_name: str # 评测工具名称
    type: Optional[int] = None # 评测工具类型
    metric_id: Optional[str] = None # 评测指标id
    adapter_code: Optional[str] = None # 脚本代码
    script: Optional[str] = None # 测试脚本示例
    desc: Optional[str] = None
    
class TestToolQueryModel(BaseModel):
    tool_name: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}
        
        if self.tool_name:
            condition["tool_name"] = {"$regex": str(self.tool_name).strip(), "$options": "i"}
       
        return condition

    