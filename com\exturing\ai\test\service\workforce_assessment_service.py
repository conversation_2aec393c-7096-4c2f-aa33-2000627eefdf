# service.py
from bson import ObjectId
from com.exturing.ai.test.comm.comm_util import float_format, str_format
from com.exturing.ai.test.model.project_requirement import ProjectRequirementQueryModel
from com.exturing.ai.test.model.workforce_assessment import (
    WorkforceAssessmentAutoModel,
    WorkforceAssessmentModel,
    WorkforceAssessmentQueryModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDB<PERSON>til
from com.exturing.ai.test.comm.page_result import PageResult
from werkzeug.datastructures import FileStorage
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.service.project_requirement_service import query_tree
import pandas as pd

_doc = "workforce_assessment"


# 新增人力评估
def create(data: WorkforceAssessmentModel) -> str:
    assert not check_assessment_item_exists(data), "人力评估项已存在"

    data_dict = data.model_dump()
    et_log.info(f"create workforce_assessment: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询人力评估
def query_page(
    page_num: int = 1, page_size: int = 10, query: WorkforceAssessmentQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询人力评估列表
def query_list(query: WorkforceAssessmentQueryModel = None):
    condition = query.get_query_condition()

    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    return json_list


# 修改人力评估
def update(id: str, data: WorkforceAssessmentModel) -> bool:
    assert not check_assessment_item_exists(data, id), "人力评估项已存在"

    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update workforce_assessment: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除人力评估
def delete(id: str) -> bool:
    et_log.info(f"delete workforce_assessment by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 自动创建人力评估项
def auto_create(query: WorkforceAssessmentAutoModel):
    et_log.info(f"auto_create workforce_assessment: {query}")
    requirement_query = ProjectRequirementQueryModel(project_id=query.project_id)
    requirement_tree = query_tree(requirement_query)

    create_list = []

    def loop(tree):
        for item in tree:
            if item.get("children"):
                loop(item["children"])
            else:
                for role in query.roles:
                    for base in query.bases:
                        for type in query.types:
                            create_list.append(
                                {
                                    "project_id": query.project_id,
                                    "project_requirement_id": item["_id"],
                                    "type": type,
                                    "role": role,
                                    "base": base,
                                }
                            )

    loop(requirement_tree)

    def get_key(item):
        return (
            item.get("project_requirement_id", "")
            + item.get("type", "notype")
            + item.get("role", "norole")
            + item.get("base", "nobase")
        )

    current_condition = {"project_id": query.project_id}
    current_list = MongoDBUtil.find_condition(_doc, current_condition) or []
    current_keys = [get_key(item) for item in current_list]

    create_list_filter = [x for x in create_list if get_key(x) not in current_keys]

    insert_ids = MongoDBUtil.insert_many(_doc, create_list_filter)

    return len(insert_ids)


# 检查人力评估项是否已存在
def check_assessment_item_exists(
    data: WorkforceAssessmentModel, id: str = None
) -> bool:
    check_keys = ["project_requirement_id", "type", "role", "base"]

    condition = {}

    if id:
        condition["_id"] = {"$ne": ObjectId(id)}

    for key in check_keys:
        if hasattr(data, key):
            value = getattr(data, key)
            if value:
                condition[key] = value

    count = MongoDBUtil.find_count(_doc, condition)

    et_log.info(
        f"check_assessment_item_exists: {condition}, count: {count}"
    )  # Log the condition and count for debugging

    return count > 0


# 导入人力评估数据
def import_data(project_id, file: FileStorage):
    all_sheets = pd.read_excel(file, sheet_name=None)

    requirement_query = ProjectRequirementQueryModel(project_id=project_id)
    requirement_tree = query_tree(requirement_query)

    workforce_assessments = MongoDBUtil.find_condition(_doc, {"project_id": project_id})
    workforce_assessment_list = list(workforce_assessments or [])
    workforce_assessment_json_list = [
        MongoDBUtil.serialize_document(doc) for doc in workforce_assessment_list
    ]

    roles = []
    types = []
    bases = []
    for item in workforce_assessment_json_list:
        if item.get("role") not in roles:
            roles.append(item.get("role"))
        if item.get("type") not in types:
            types.append(item.get("type"))
        if item.get("base") not in bases:
            bases.append(item.get("base"))

    et_log.info(f"workforce_assessment_json_list: {workforce_assessment_json_list}")

    def update_item_workforce(requirement_id, role, type, base, workforce, content):
        for item in workforce_assessment_json_list:
            if (
                item.get("project_requirement_id") == requirement_id
                and item.get("role") == role
                and item.get("type") == type
                and item.get("base") == base
            ):
                update_item = {
                    "_id": ObjectId(item["_id"]),
                    "workload": workforce,
                    "work_content": content,
                }

                MongoDBUtil.update_one_pro(_doc, update_item)

    def get_requirement_id(requirement_tree, level_1, level_2, level_3):
        for item in requirement_tree:
            if item.get("name") == level_1:
                children = item.get("children")
                if not children:
                    return item["_id"]

                for child in item["children"]:
                    if child.get("name") == level_2:
                        children = child.get("children")
                        if not children:
                            return child["_id"]

                        for sub_child in child["children"]:
                            if sub_child.get("name") == level_3:
                                return sub_child["_id"]

        return None

    for sheet_name in all_sheets:
        df = all_sheets[sheet_name]
        sheet_list = df.to_dict(orient="records")

        for item in sheet_list:
            level_1 = str_format("Level_1", item)
            level_2 = str_format("Level_2", item)
            level_3 = str_format("Level_3", item)
            requirement_id = get_requirement_id(
                requirement_tree, level_1, level_2, level_3
            )

            for role in roles:
                for type in types:
                    for base in bases:
                        workforce = float_format(f"{base}_{role}_{type}", item) or 0.0
                        content = str_format(f"{role}_工作内容", item)

                        update_item_workforce(
                            requirement_id, role, type, base, workforce, content
                        )

    return True
