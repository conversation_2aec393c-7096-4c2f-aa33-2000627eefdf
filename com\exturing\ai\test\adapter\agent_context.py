import copy
import json
import traceback
from datetime import datetime

import requests
from pandas import isnull

from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import *
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService

headers = {
   'Content-Type': 'application/json',
}

params = {
    'advconfig': '1',
    'ba_env': 'prod',
    'productId': '279623629',
    'recordId': '3b881c22170f42b98ad8909b9b743b16',
    'requestId': '3b881c22170f42b98ad8909b9b743b16',
    'sessionId': 'ccfce291f59b4cff8de6c307ed18e411',
    'deviceName': 'llmtest_refer_001',
}


dialogHistory_example = {
                        'output': {
                            'widget': {
                                'llmOutputStatus': 'LLM_TYPING_DONE',
                                'type': 'displayText',
                                'llmOutputTips': '以下回答由AI⼤模型⽣成',
                                'streamType': 'done',
                                'displayText': '苏州，被誉为“东方威尼斯”，拥有众多迷人的古典园林和水乡风情。\n\n#### 01 拙政园\n拙政园，中国四大名园之一，以水景著称，展现了江南园林的精致与秀美。\n\n#### 02 苏州博物馆\n苏州博物馆，由著名建筑师贝聿铭设计，现代与古典完美结合，展示了丰富的苏州历史文化。\n\n#### 03 虎丘\n虎丘，有2500多年历史的名胜古迹，山顶的塔是苏州的象征，风景独特。\n\n#### 04 同里古镇\n同里古镇，保存有大量明清时期的建筑，乘坐小船穿梭在古镇水道，体验水乡的宁静与美丽。\n\n这些地方都是苏州的精华所在，值得您一一探索。 ',
                                    },
                                },
                        'timestamp': 1722398347,
                        'source': 'llm',
                        'recordId': 'c8e33e0d549c478c8ce8d7abef079f2e:e06adafc7a064531af8e25e03bae0974',
                        'input': '苏州有什么好玩的景点',
                        }
# adapter_code=上下文
class Semantic_context_service:

    #单条记录运行
    @classmethod
    def run_agent_context_item(cls, config_info:TestConfigInfoModel, item:EtDataSetItem,children, record_id, request_id, session_id, parent_id, task_id,
                                task_result_id, do_user):
        et_log.info(f"run_agent_context_item params config_info:{config_info} item:{item} record_id:{record_id} request_id:{request_id} "
                    f"session_id:{session_id} parent_id:{parent_id} task_id:{task_id} task_result_id:{task_result_id}")
        try :
            message = copy.deepcopy(json.loads(config_info.env_script))
            whole_result = [item.to_json_str()] + children
            et_log.info(f"生成子列表: {children}")
            et_log.info(f"生成的新列表: {whole_result}")
            uri = config_info.env_uri
            result_tree_data = convert_to_tree(whole_result)
            result_tree = result_tree_data[0]
            et_log.info(f"resulttree：{result_tree}")
            result, An_expected = process_tree(result_tree)
            result.popitem()
            et_log.info(f"result和An_expected分别为{result}{An_expected}")
            status, err = is_valid_format1(result, An_expected)
            if not status:
                et_log.error(f"请求数据出错：{err}")
            else:
                res = []
            keys = list(result.keys())
            message['session']['dialogHistory'][0]['output']['widget']['displayText'] = result[keys[1]]
            message['session']['dialogHistory'][0]['input'] = result[keys[0]]
            if len(keys) == 3:
                message['request']['inputs'][0]['input'] = result[keys[2]]
            else:
                tmp_keys =keys[2:]
                while len(tmp_keys) > 1:
                    if result[tmp_keys[2]]=='':
                        message['request']['inputs'][0]['input'] = result[tmp_keys[0]]
                        break
                    else:
                        dialogHistory_example['output']['widget']['displayText'] = result[tmp_keys[1]]
                        dialogHistory_example['input'] = result[tmp_keys[0]]
                        message['session']['dialogHistory'].append(copy.deepcopy(dialogHistory_example))
                        # 添加完成后，再度删除1轮，此时为['Q3']，由于只有1个，所以退出循环
                        tmp_keys = tmp_keys[2:]
                if result[tmp_keys[-1]]!='':
                    message['request']['inputs'][0]['input'] = result[tmp_keys[-1]]
            response = requests.post(url=uri, params=params, headers=headers, json=message)
            et_log.info(f"语义上下文res{response}")
            refineResult = response.json().get('response').get('widget')
            print(response.json().get('response'))
            if refineResult == {}:
                res.append(response.json())
                print(res)
                print("!!!!!!!!!!!!!")
                print(response.json()['response'])
            else:
                res.append(refineResult.get('refineResult')[0])
                print(res)
                print("!!!!!!!!!!!!!!!!")
                print(refineResult)
            et_log.info("请求处理结束，开始判断准确率：")
            test_task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
            expected_value =list(An_expected.values())[0]
            results = 1 if str(expected_value) in str(res) else 0
            item_result1 = {"parent_id":parent_id, "data_set_item_id": str(item.id), "task_id": task_id,
                "task_result_id": str(task_result_id), "recall_id": record_id,
                "actual_answer": item.expected_answer, "answer_score": 0,
                "qa_recall": 0, "qa_keywords": expected_value, "eval_config_id": test_task.config_id,
                "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user }
            item_result_id = TestItemResultService.insert_one(item_result1)
            item_result2 = {"parent_id":item_result_id, "data_set_item_id": str(children[0]["_id"]), "task_id": task_id,
                "task_result_id": str(task_result_id), "recall_id": record_id, "eval_config_id": test_task.config_id,
                "actual_answer": res[0], "result_answer": results, "answer_score": 0,
                "qa_recall": 0, "qa_keywords": expected_value,"result_final": results,
                "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user}
            et_log.info(f"最终输出：{item_result2}")
            item_result_id = TestItemResultService.insert_one(item_result2)

            return item_result_id
        except Exception as e:
            et_log.error(f"run_agent_context_item params item_id:{item.id} exception,\n{traceback.print_exc()}")
            return None