import time
from openai import OpenAI
from com.exturing.ai.test.comm.comm_constant import MIND2_API_KEY,MIND2_BOT_MODEL,MIND2_BASE_URL
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


system_prompt = """
        # 指示
        你是车载语音助手大管家，可以指挥其它的小助手共同合作来处理事件。
        ## 目标 
        1. 主动关怀，帮助用户提高效率，回答要简洁并抓住重点
        2. 主动去理解用户的深层意图
        3. 充分利用自己的能力去为用户提供帮助
        4. 你要学会自己做决定，协调各个小助手，减少对用户的征询。
        ## 约束条件
        1. 不寻求用户协助。
        2. 仅能使用以下列出的小助手所具有的能力。
        ## 助手列表
        新闻百科助手: "google"
         - description: 根据用户的场景提供AI新闻内容及资讯服务，可以根据用户的需求和场景提供百科的内容服务。
         - args: "input": "<搜索内容>"
        用车助手: "car_assistant"
         - description: 可以根据用户的场景被动或者主动完成空调、窗门锁、灯光、座椅及后视镜、声音（音场及均衡器）、驾驶模式的调节的任务及用车说明和工具类型保养类型，驾驶策略（如避让豪车，驾驶安全）的任务。
         - args: "input": "<完整指令>"
        导航助手: "navigation"
         - description: 可以根据用户的场景被动或者主动完成如寻找兴趣点，规划路线，调整路线，查询路线，增加途径点、发起导航、更换路线等导航任务。
         - args: "input": "<完整指令>"
        娱乐助手: "amusement"
         - description: 根据用户的需求或者场景的触发提供音乐内容服务，包括符合用户个人偏好的歌曲的搜索，歌单的生成。
         - args: "input": "<完整指令>"
        生活助手: "living"
         - description: 根据用户的需求或者场景的触发提供订餐，点单，订票，快递等与生活服务类型的生态打通闭环的服务能力。
         - args: "input": "<完整指令>"
         - notices: 需先征询用户意见再执行
        闲聊助手: "chitchat"
         - description: 提供娱乐、陪伴或信息，而不要求高度结构化或严肃的任务导向。
         - args: "input": "<完整指令>"
        ## 性能评估
        1. 指挥每个助手都有代价，因此要聪明高效，尽量以最少的步骤完成任务。
        2. 必要情况下可调度多个助手。 
        ## 格式要求
        (记住！确保你的输出始终遵循以下2种JSON格式之一:
        A. 如果你已完成任务:
        {
          "Speak": ""
        }
        B. 如果你尚未完成任务:
        {
          "Speak": "",
          "Actions": [
            {
              "Action": "",
              "args": {}
            }
          ]
        }
        )
        """

class AgentllmmindV2:

    @classmethod
    def run_chat(cls, config_info: TestConfigInfoModel, item: EtDataSetItem, record_id, request_id, session_id,
                                      parent_id, task_id,
                                      task_result_id, do_user):
        result = ""
        start_time = time.time()
        client = OpenAI(
            base_url = MIND2_BASE_URL,
            api_key = MIND2_API_KEY,
        )
        completion = client.chat.completions.create(
            model= MIND2_BOT_MODEL,  # 微调后的模型，需搭配系统提示词使用
            # model="DeepSeek-R1-Distill-Qwen-32B",  # 原始基座模型
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": item.question},

            ],
            temperature=0.3,
            stream = False,
            )
        # for chunk in completion:

        #     print(chunk.choices[0].delta.content, end='')
        print(completion.choices[0].message.content)
        end_time = time.time()
        elapsed_time = float(end_time - start_time)
        if completion.choices[0].message.content:
            result+=completion.choices[0].message.content
        else:
            result+= "N/A"
        et_log.info(f"_____________________{result}")
        test_task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
        item_result = {"parent_id": parent_id, "data_set_item_id": str(item.id), "task_id": task_id,
                       "task_result_id": str(task_result_id), "expected_answer": item.expected_answer,
                       "expected_category": item.expected_category, "expected_task": item.expected_task,
                       "actual_answer": result,"eval_config_id":test_task.config_id,
                       "answer_score": 0, "qa_recall": 0, "first_res_time": elapsed_time,
                       "qa_use_time": elapsed_time, "recall_id": record_id,
                       "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user,
                       "re_interval_time": 0, "is_websearch": 0}
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"run_mind2:{item_result_id}")
        return completion.choices[0].message.content
