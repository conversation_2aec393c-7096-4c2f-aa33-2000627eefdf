from com.exturing.ai.test.model import project_requirement
from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class WorkforceAssessmentModel(BaseDataModel):
    project_id: Optional[str] = None  # 项目ID
    project_requirement_id: Optional[str] = None  # 需求ID
    type: Optional[str] = None  # 人力类型
    role: Optional[str] = None  # 人力角色
    base: Optional[str] = None  # 人力base地
    workload: Optional[float] = None  # 工作量 人天
    work_content: Optional[str] = None  # 工作内容
    desc: Optional[str] = None  # 评估描述


class WorkforceAssessmentQueryModel(BaseModel):
    project_id: Optional[str] = None
    type: Optional[str] = None
    base: Optional[str] = None
    role: Optional[str] = None
    project_requirement_id: Optional[str] = None

    def get_query_condition(self):
        condition = {}

        if self.project_id:
            project_id_list = self.project_id.split(",")
            if len(project_id_list) > 1:
                condition["project_id"] = {"$in": project_id_list}
            else:
                condition["project_id"] = self.project_id

        if self.type:
            condition["type"] = self.type

        if self.base:
            condition["base"] = self.base

        if self.role:
            condition["role"] = self.role

        if self.project_requirement_id:
            condition["project_requirement_id"] = self.project_requirement_id

        return condition


class WorkforceAssessmentAutoModel(BaseModel):
    project_id: Optional[str] = None  # 项目ID
    types: Optional[list[str]] = []  # 人力类型
    roles: Optional[list[str]] = []  # 人力角色
    bases: Optional[list[str]] = []  # 人力base地
