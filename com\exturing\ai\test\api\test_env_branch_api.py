import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON><PERSON>
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.test_env_branch import TestEnvBranch
from com.exturing.ai.test.service.test_branch_service import TestEnvBranchService

test_env_branch=Blueprint('test_env_branch',__name__)

# 新增
@test_env_branch.route(f'/{URL_PREFIX}/test-env-branch/create', methods=['POST'])
def test_env_branch_create():
    et_log.info("############test_env_branch_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_env_branch_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_env_branch_create error, param is null", "").to_json()
        if "branch_name" not in data or len(str(data["branch_name"]).strip()) == 0:
            et_log.error(f"test_env_branch_create error, branch_name is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "branch_name is null ", "").to_json()
        data["branch_name"] = str(data["branch_name"]).strip()
        data["branch_desc"] = str(data["branch_desc"]).strip() if "branch_desc" in data and data["branch_desc"] is not None and len(str(data["branch_desc"]).strip()) > 0 else ""
        data["param_pid"] = str(data["param_pid"]).strip() if "param_pid" in data and data["param_pid"] is not None and len(str(data["param_pid"]).strip()) > 0 else ""
        data["device_id"] = str(data["device_id"]).strip() if "device_id" in data and data["device_id"] is not None and len(str(data["device_id"]).strip()) > 0 else ""
        data["version"] = str(data["version"]).strip() if "version" in data and data["version"] is not None and len(str(data["version"]).strip()) > 0 else ""
        data["user_id"] = str(data["user_id"]).strip() if "user_id" in data and data["user_id"] is not None and len(str(data["user_id"]).strip()) > 0 else ""
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        insert_result = TestEnvBranchService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, ResultCode.INTERFACE_INNER_INVOKE_ERROR.msg, "").to_json()
    except Exception as e:
        et_log.error(f"test_env_branch_create create test_env exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, ResultCode.INTERFACE_INNER_INVOKE_ERROR.msg, "").to_json()

# 查询
@test_env_branch.route(f'/{URL_PREFIX}/test-env-branch/query', methods=['POST'])
def test_env_query():
    et_log.info("############test_env_query################")
    data = request.get_json()
    if not data or "branch_name" not in data or data["branch_name"] is None or len(str(data["branch_name"]).strip()) == 0:
        data["branch_name"] = ""
    result = TestEnvBranchService.query_branch_list(data["branch_name"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()

# 删除
@test_env_branch.route(f'/{URL_PREFIX}/test-env-branch/del', methods=['POST'])
def test_branch_del():
    et_log.info("############test_branch_del################")
    data = request.get_json()
    if not data or "branch_id" not in data or data["branch_id"] is None:
        et_log.error(f"test_branch_del delete error, branch_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "branch_id is null", "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestEnvBranchService.delete(data["branch_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "delete test_env_branch error", "").to_json()

# 更新
@test_env_branch.route(f'/{URL_PREFIX}/test-env-branch/update', methods=['POST'])
def test_branch_update():
    et_log.info("############test_branch_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_branch_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "branch_id" not in data or data["branch_id"] is None:
            et_log.error(f"test_branch_update error, branch_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "branch_id in param is null", "").to_json()
        branch_pk:TestEnvBranch = TestEnvBranchService.find_pk(data["branch_id"])
        if branch_pk is None:
            et_log.error(f"test_env_branch_update error, branch_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "branch_id id is invalid", "").to_json()
        branch_pk.branch_name = str(data["branch_name"]).strip() if "branch_name" in data and data["branch_name"] is not None and len(str(data["branch_name"])) > 0 else ""
        branch_pk.branch_desc = str(data["branch_desc"]).strip() if "branch_desc" in data and data["branch_desc"] is not None and len(str(data["branch_desc"])) > 0 else ""
        branch_pk.param_pid = str(data["param_pid"]).strip() if "param_pid" in data and data["param_pid"] is not None and len(str(data["param_pid"])) > 0 else ""
        branch_pk.device_id = str(data["device_id"]).strip() if "device_id" in data and data["device_id"] is not None and len(str(data["device_id"])) > 0 else ""
        branch_pk.version = str(data["version"]).strip() if "version" in data and data["version"] is not None and len(str(data["version"])) > 0 else ""
        branch_pk.user_id = str(data["user_id"]).strip() if "user_id" in data and data["user_id"] is not None and len(str(data["user_id"])) > 0 else ""
        branch_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        update_result = TestEnvBranchService.update(branch_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update error", "").to_json()
    except Exception as e:
        et_log.error(f"test_env_branch_update update test_env_branch exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update exception", "").to_json()

