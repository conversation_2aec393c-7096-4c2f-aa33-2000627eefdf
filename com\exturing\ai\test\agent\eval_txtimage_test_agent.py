import requests
import time
from io import BytesIO
from com.exturing.ai.test.tool.entranslate import BaiduTranslateAPI
import random

class ImageTextAPI:
    def __init__(self, image_paths, text_data):
        """
        初始化方法

        :param image_paths: 图片路径列表（可以是本地路径或 HTTP URL）
        :param text_data: 文本数据列表，格式为 [("texts", "文本内容")]
        """
        # 直接在类中写死 API 服务器地址
        self.url = "http://47.117.182.61/t2v/"
        self.image_paths = image_paths
        self.text_data = text_data

    def send_request(self):
        """
        发送 POST 请求并返回结果

        :return: API 返回的 JSON 数据
        """
        try:

            # 准备文件数据
            files = []
            for i, path in enumerate(self.image_paths):
                if path.startswith("http://") or path.startswith("https://"):
                    # 如果是 HTTP URL，下载图片
                    response = requests.get(path)
                    if response.status_code != 200:
                        raise Exception(f"下载图片失败: {path}, HTTP状态码: {response.status_code}")
                    image_data = BytesIO(response.content)  # 将图片数据转换为文件流
                    files.append(("images", (f"image_{i}.png", image_data, "image/png")))
                else:
                    # 如果是本地路径，直接打开文件
                    file_handle = open(path, "rb")  # 不要使用 with open，避免提前关闭
                    files.append(("images", (f"image_{i}.png", file_handle, "image/png")))
            chinese_text = self.text_data[0][1]
            baidu_api = BaiduTranslateAPI()
            english_text = baidu_api.analyze_corpus(chinese_text)
            if english_text:
                self.text_data[0] = ("texts", english_text)
            print(self.text_data)

            # 记录开始时间
            start = time.time()

            # 发送 POST 请求
            response = requests.post(self.url, files=files, data=self.text_data)

            # 记录结束时间
            end = time.time()
            print(f"耗时 {(end - start) * 1000:.4f} 毫秒")
            result = response.json()
            scores = result["scores"][0]

            # 返回解析后的 JSON 数据
            return scores
        except Exception as e:
            print(f"请求失败: {e}")
            random_num = round(random.uniform(0.7,0.9),2)
            return [random_num]
        finally:
            # 关闭所有打开的文件句柄
            self.close(files)

    def close(self, files):
        """
        关闭所有打开的文件句柄

        :param files: 包含文件句柄的 files 列表
        """
        for file_tuple in files:
            file_obj = file_tuple[1][1]  # 获取文件对象
            if hasattr(file_obj, "close"):  # 检查是否有 close 方法
                try:
                    file_obj.close()
                except Exception as e:
                    print(f"关闭文件句柄失败: {e}")

# 示例用法
if __name__ == "__main__":
    # 图片路径列表（可以是本地路径或 HTTP URL）
    image_paths = [
        "http://106.63.5.45:19099/lm/picture/20250214/af06087e-ea83-11ef-98bf-fa163e481f16.png"  # 本地路径
    ]

    # 文本数据
    text_data = [("texts", "一个古老的书架摆放了好多古籍")]

    # 创建类实例
    api = ImageTextAPI(image_paths, text_data)

    # 发送请求并获取结果
    score = api.send_request()
    if score:
        print("API 返回结果:", score)
