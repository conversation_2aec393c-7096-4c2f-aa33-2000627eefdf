import traceback
from typing import Optional

from bson import ObjectId

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.model.base_pydantic_model import BasePydanticModel


class TestTaskAiResultModel(BasePydanticModel):
    """
    任务 AI评测结果明细表
    """
    _doc = "test_task_ai_result"

    task_id: Optional[ObjectId] = None    # 任务id
    data_set_id: Optional[ObjectId] = None    # 数据集id
    data_set_item_id: Optional[ObjectId] = None    # 数据集明细id
    config_ids: Optional[str] = None    # 模型通道组合ids
    batch_id: Optional[str] = None    # 批次id
    question: Optional[str] = None    # 问题
    model_scores: dict = None    # 父级指标id

    @classmethod
    def query_page(cls, task_id, page_num, page_size):
        """
        查询任务明细模型对比分页
        :param task_id: 任务id
        :param page_num: 页码
        :param page_size: 每页记录数
        :return: 任务明细模型对比分页对象
        """
        et_log.info(f"query_page task_id:{task_id} page_num:{page_num} page_size:{page_size}")
        try:
            if task_id is None or len(task_id) == 0:
                return None
            condition = {"task_id": ObjectId(str(task_id))}
            page_num = 1 if not page_num or page_num < 1 else page_num
            page_size = 10 if not page_size or page_size < 1 else page_size
            # 查询匹配的数量
            total = TestTaskAiResultModel.find_condition_count(condition)
            if total < 1:  # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            item_list = TestTaskAiResultModel.find_page_list(condition, None, page_size, page.skip)
            result = []
            if item_list:
                for item in item_list:
                    data = {"task_id":str(item.task_id), "data_set_id": str(item.data_set_id),
                            "data_set_item_id": str(item.data_set_item_id),
                            "question": item.question}
                    for model in item.model_scores:
                        et_log.info(f"query_page ")


            page.page_data = result
        except Exception as e:
            et_log.error(f"query_page exception:{e},\n{traceback.print_exc()}")
            return False
