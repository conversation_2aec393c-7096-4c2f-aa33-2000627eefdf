import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.test_env import TestEnv
from com.exturing.ai.test.service.test_env_service import TestEnvService

test_env=Blueprint('test_env',__name__)

# 新增
@test_env.route(f'/{URL_PREFIX}/test-env/create', methods=['POST'])
def test_env_create():
    et_log.info("############test_env_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_env_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_env_create error, param is null", "").to_json()
        if "env_name" not in data or len(str(data["env_name"]).strip()) == 0:
            et_log.error(f"test_env_create error, env_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "plan_id in param is null ", "").to_json()
        if "env_code" not in data or len(str(data["env_code"]).strip()) == 0:
            et_log.error(f"test_env_create error, env_code in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_name in param is null ", "").to_json()
        data["env_name"] = str(data["env_name"]).strip()
        data["env_code"] = str(data["env_code"]).strip()
        data["env_type"] = data["env_type"] if ("env_type" in data and data["env_type"] is not None) else 2
        data["env_script"] = str(data["env_script"]).strip() if "env_script" in data and data["env_script"] is not None and len(str(data["env_script"]).strip()) > 0 else ""
        data["env_uri"] = str(data["env_uri"]).strip() if "env_uri" in data and data["env_uri"] is not None and len(str(data["env_uri"]).strip()) > 0 else ""

        data["branch_name"] = str(data["branch_name"]).strip() if "branch_name" in data and data["branch_name"] is not None and len(str(data["branch_name"]).strip()) > 0 else ""
        data["env_desc"] = str(data["env_desc"]).strip() if "env_desc" in data and data["env_desc"] is not None and len(str(data["env_desc"]).strip()) > 0 else ""
        data["vm_id"] = str(data["vm_id"]).strip() if "vm_id" in data and data["vm_id"] is not None and len(str(data["vm_id"]).strip()) > 0 else ""
        data["pid_val"] = str(data["pid_val"]).strip() if "pid_val" in data and data["pid_val"] is not None and len(str(data["pid_val"]).strip()) > 0 else ""
        data["device_id"] = str(data["device_id"]).strip() if "device_id" in data and data["device_id"] is not None and len(str(data["device_id"]).strip()) > 0 else ""
        data["version"] = str(data["version"]).strip() if "version" in data and data["version"] is not None and len(str(data["version"]).strip()) > 0 else ""
        data["user_id"] = str(data["user_id"]).strip() if "user_id" in data and data["user_id"] is not None and len(str(data["user_id"]).strip()) > 0 else ""

        data["adapter_code"] = str(data["adapter_code"]).strip() if "adapter_code" in data and data["adapter_code"] is not None and len(str(data["adapter_code"]).strip()) > 0 else ""
        data["oem_code"] = str(data["oem_code"]).strip() if "oem_code" in data and data["oem_code"] is not None and len(str(data["oem_code"]).strip()) > 0 else ""

        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        insert_result = TestEnvService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert test_env error", "").to_json()
    except Exception as e:
        et_log.error(f"test_env_create create test_env exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert test_env exception", "").to_json()

# 查询
@test_env.route(f'/{URL_PREFIX}/test-env/query', methods=['POST'])
def test_env_query():
    et_log.info("############test_env_query################")
    data = request.get_json()
    if not data or "env_name" not in data or data["env_name"] is None or len(str(data["env_name"]).strip()) == 0:
        data["env_name"] = ""
    if not data or "env_code" not in data or data["env_code"] is None or len(str(data["env_code"]).strip()) == 0:
        data["env_code"] = ""
    if not data or "env_type" not in data or data["env_type"] is None or data["env_type"] < 1:
        data["env_type"] = None
    result = TestEnvService.query_env_list(data["env_name"], data["env_code"], data["env_type"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()

# 删除
@test_env.route(f'/{URL_PREFIX}/test-env/del', methods=['POST'])
def test_env_del():
    et_log.info("############test_env_del################")
    data = request.get_json()
    if not data or "env_id" not in data or data["env_id"] is None:
        et_log.error(f"test_env_del delete error, env_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestEnvService.delete(data["env_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "delete test_env error", "").to_json()

# 更新
@test_env.route(f'/{URL_PREFIX}/test-env/update', methods=['POST'])
def test_env_update():
    et_log.info("############test_env_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_env_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "env_id" not in data or data["env_id"] is None:
            et_log.error(f"test_env_update error, env_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_id in param is null", "").to_json()
        env_pk:TestEnv = TestEnvService.find_pk(data["env_id"])
        if env_pk is None:
            et_log.error(f"test_env_update error, env_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "env_id id is invalid", "").to_json()

        env_pk.env_name = str(data["env_name"]).strip() if "env_name" in data and data["env_name"] is not None and len(str(data["env_name"])) > 0 else ""
        env_pk.env_code = str(data["env_code"]).strip() if "env_code" in data and data["env_code"] is not None and len(str(data["env_code"])) > 0 else ""
        env_pk.env_uri = str(data["env_uri"]).strip() if "env_uri" in data and data["env_uri"] is not None and len(str(data["env_uri"])) > 0 else ""
        env_pk.env_script = str(data["env_script"]).strip() if "env_script" in data and data["env_script"] is not None and len(str(data["env_script"])) > 0 else ""
        env_pk.env_type = data["env_type"] if ("env_type" in data and data["env_type"] is not None) else None
        env_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0

        env_pk.branch_name = str(data["branch_name"]).strip() if "branch_name" in data and data[
            "branch_name"] is not None and len(str(data["branch_name"]).strip()) > 0 else ""
        env_pk.env_desc = str(data["env_desc"]).strip() if "env_desc" in data and data["env_desc"] is not None and len(
            str(data["env_desc"]).strip()) > 0 else ""
        env_pk.vm_id = str(data["vm_id"]).strip() if "vm_id" in data and data["vm_id"] is not None and len(
            str(data["vm_id"]).strip()) > 0 else ""
        env_pk.pid_val = str(data["pid_val"]).strip() if "pid_val" in data and data["pid_val"] is not None and len(
            str(data["pid_val"]).strip()) > 0 else ""
        env_pk.device_id = str(data["device_id"]).strip() if "device_id" in data and data[
            "device_id"] is not None and len(str(data["device_id"]).strip()) > 0 else ""
        env_pk.version = str(data["version"]).strip() if "version" in data and data["version"] is not None and len(
            str(data["version"]).strip()) > 0 else ""
        env_pk.user_id = str(data["user_id"]).strip() if "user_id" in data and data["user_id"] is not None and len(
            str(data["user_id"]).strip()) > 0 else ""

        env_pk.adapter_code = str(data["adapter_code"]).strip() if "adapter_code" in data and data["adapter_code"] is not None and len(str(data["adapter_code"]).strip()) > 0 else ""
        env_pk.oem_code = str(data["oem_code"]).strip() if "oem_code" in data and data["oem_code"] is not None and len(str(data["oem_code"]).strip()) > 0 else ""

        env_pk.tmp_id = str(data["tmp_id"]).strip() if "tmp_id" in data and data["tmp_id"] is not None and len(str(data["tmp_id"]).strip()) > 0 else ""
        env_pk.tmp_code = str(data["tmp_code"]).strip() if "tmp_code" in data and data["tmp_code"] is not None and len(str(data["tmp_code"]).strip()) > 0 else ""

        update_result = TestEnvService.update(env_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update test_env error", "").to_json()
    except Exception as e:
        et_log.error(f"test_env_update update test_env exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update test_env exception", "").to_json()

