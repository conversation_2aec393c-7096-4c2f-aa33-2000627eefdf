import re
import traceback

from volcenginesdkarkruntime import Ark

from com.exturing.ai.test.comm.comm_constant import DOUBAO_API_KEY, DOUBAO_BASE_URL, DOUBAO_BOT_MODEL
from com.exturing.ai.test.comm.log_tool import et_log



# 豆包评估智能体
class EvalDoubaoAgent:

    @classmethod
    def compute_similarity(cls, prompt_data,system_prompt):
        """
        根据提示词内容，进行语义相似度打分
        :param prompt_data:含问题、期望回答、实际回答和提示词描述的内容
        :return:
        """
        try:
            # 初始化 Ark 客户端
            client = Ark(
                api_key=DOUBAO_API_KEY,
                base_url=DOUBAO_BASE_URL
            )
            et_log.info(f"compute_similarity prompt_data:{prompt_data}")
            if system_prompt:
                system_prompt = system_prompt
            else:
                system_prompt = "你是豆包，是由字节跳动开发的AI人工智能助手。"
            # 调用流式接口
            stream = client.bot_chat.completions.create(
                model=DOUBAO_BOT_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt_data}
                ],
                stream=True
            )

            # # 记录请求开始时间
            # begin_time = datetime.datetime.now()
            # i = 0
            response_content = ""  # 初始化用于保存流式输出内容

            # 处理流式响应
            for chunk in stream:
                if chunk.references:
                    et_log.info(f"引用内容: {chunk.references}")
                if not chunk.choices:
                    et_log.info("响应内容为空或格式不正确")
                    continue

                # # 计算首轮响应时间
                # if i == 0:
                #     final_time = datetime.datetime.now()
                #     single_round_time = (final_time - begin_time).total_seconds()
                #     print(f"============== This round total time (s): {single_round_time}\n")
                #     i += 1

                # 拼接流式响应内容
                content = chunk.choices[0].delta.content
                response_content += content  # 拼接内容
                # print(content, end="")
            # 将最终流式响应的整体内容保存到 DataFrame 的 24 列
            et_log.info(f"compute_similarity response_content:{response_content}")
            return response_content
        except Exception as e:
            et_log.error(f"请求失败，错误信息：{e},\n{traceback.print_exc()}")
            raise e

    @classmethod
    def compare_text_semantics(cls, question,hint_word):
        result = cls.compute_similarity(question,hint_word)
        et_log.info(f"____________{result}_________")
        return result




# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我钟情于节奏轻快的流行音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我喜欢古典音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"", "answer2":"我喜欢古典音乐。"}
# EvalDoubaoAgent.compare_text_semantics("你喜欢什么类型的音乐？")

