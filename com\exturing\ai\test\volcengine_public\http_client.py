import httpx
from typing import Optional, Dict, Any, Generator


class HttpClient:
    def __init__(
        self,
        base_url: str,
        default_headers: Optional[Dict[str, str]] = None,
        default_query: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None,
    ):
        self.base_url = base_url.rstrip("/")
        self.default_headers = default_headers or {}
        self.default_query = default_query or {}
        self.timeout = timeout or 60
        self.client = httpx.Client(base_url=self.base_url, timeout=self.timeout)

    def close(self) -> None:
        self.client.close()

    def __enter__(self) -> "HttpClient":
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self.close()

    def _merge_params(self, params: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        combined = self.default_query.copy()
        if params:
            combined.update(params)
        return combined

    def _merge_headers(self, headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        combined = self.default_headers.copy()
        if headers:
            combined.update(headers)
        return combined

    def get(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        return self.client.get(
            path,
            params=self._merge_params(params),
            headers=self._merge_headers(headers),
        )

    def post(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        return self.client.post(
            path,
            params=self._merge_params(params),
            headers=self._merge_headers(headers),
            json=data,
        )

    def stream_post(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Generator[str, None, None]:
        with self.client.stream(
            "POST",
            path,
            params=self._merge_params(params),
            headers=self._merge_headers(headers),
            json=data,
        ) as response:
            for line in response.iter_lines():
                if line:
                    yield line.encode("utf-8")

    def put(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        return self.client.put(
            path,
            params=self._merge_params(params),
            headers=self._merge_headers(headers),
            json=data,
        )

    def delete(
        self,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        return self.client.delete(
            path,
            params=self._merge_params(params),
            headers=self._merge_headers(headers),
            json=data,
        )
