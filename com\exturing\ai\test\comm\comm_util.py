import base64
import json
from json import JSONDecodeError

import json5
import pandas as pd

from com.exturing.ai.test.comm.log_tool import et_log


def decode_jwt_payload(jwt_token):
    # JWT由三部分组成，header.payload.signature，通过点（.）分隔
    try:
        # 分割JWT token
        parts = jwt_token.split(".")
        # 确保有3个部分
        if len(parts) != 3:
            raise ValueError("Invalid JWT token")

        # 解码payload部分（第二个部分）
        payload_base64 = parts[1]
        # JWT使用base64url编码，需要替换-和_为+和/
        payload_base64 += "=" * (-len(payload_base64) % 4)  # 补全等号
        payload_json = base64.urlsafe_b64decode(payload_base64.encode("utf-8")).decode(
            "utf-8"
        )

        return payload_json
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


def list_to_tree(nodes):
    result = []
    node_map = {}

    # 第一次遍历：构建节点映射
    for node in nodes:
        node_id = node.get("_id")
        if node_id is None:
            continue  # 忽略没有_id的节点
        node_map[node_id] = node

    # 第二次遍历：构建树结构
    for node in nodes:
        parent_id = node.get("parent_id", "")
        if not parent_id or parent_id == node.get("_id"):
            # 没有父节点，或者父节点是自己，作为根节点添加到result
            result.append(node)
        else:
            parent = node_map.get(parent_id)
            if parent is None:
                # 父节点不存在，跳过该节点并记录警告
                print(
                    f"Warning: Parent node with _id {parent_id} not found for node {node['_id']}"
                )
                continue
            # 确保父节点有children字段
            if "children" not in parent:
                parent["children"] = []
            parent["children"].append(node)

    return result


def collect_children_keys(node, key="question"):
    keys = [node[key]]
    for child in node.get("children", []):
        keys.extend(collect_children_keys(child, key))
    return keys


def str_format(key, dict):
    value = dict.get(key, None)
    if pd.isna(value) or value is None:
        return None
    return str(value)


def int_format(key, dict):
    value = dict.get(key, None)
    if pd.isna(value) or value is None:
        return None

    try:
        return int(value)
    except Exception as e:
        return None


def float_format(key, dict):
    value = dict.get(key, None)
    if pd.isna(value) or value is None:
        return None
    try:
        return float(value)
    except Exception as e:
        return None


def time_format(key, dict=None):
    value = dict.get(key, None) if dict else key
    if pd.isna(value) or value is None:
        return None
    try:
        return value.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        return None


def date_format(key, dict=None):
    value = dict.get(key, None) if dict else key
    if pd.isna(value) or value is None:
        return None
    try:
        return value.strftime("%Y-%m-%d")
    except Exception as e:
        return None


def month_format(key, dict=None):
    value = dict.get(key, None) if dict else key
    if pd.isna(value) or value is None:
        return None
    try:
        return value.strftime("%Y-%m")
    except Exception as e:
        return None


def bool_format(key, dict):
    value = dict.get(key, None)
    if pd.isna(value) or value is None:
        return None
    return value == 1 or value == "1" or value == "是"


def get_file_type_by_magic_number(file):
    """
    根据文件，获取文件的类型
    :param file: 文件对象
    :return: 文件类型 如:pdf|jpeg|png|docx|doc|xlsx|xls|zip等
    """

    file_name = file.name
    try:
        # 读取文件的前几个字节
        header = file.read(8)
        # 判断 PDF 文件
        if header.startswith(b"%PDF-"):
            et_log.info(f"{file_name} PDF 文件")
            return "pdf"
        # 判断 JPEG 文件
        elif header[:2] == b"\xff\xd8":
            et_log.info(f"{file_name} JPEG 图像")
            return "jpeg"
        # 判断 PNG 文件
        elif header[:8] == b"\x89PNG\r\n\x1a\n":
            et_log.info(f"{file_name} PNG 图像")
            return "png"
        # 判断 ZIP 文件（DOCX、XLSX 等基于 ZIP 格式）
        elif header[:4] == b"PK\x03\x04":
            if file_name.lower().endswith(".docx"):
                et_log.info(f"{file_name} Word 文档")
                return "docx"
            elif file_name.lower().endswith(".doc"):
                et_log.info(f"{file_name} Word 文档")
                return "doc"
            elif file_name.lower().endswith(".xls"):
                et_log.info(f"{file_name} Excel xls表格")
                return "xls"
            elif file_name.lower().endswith(".xlsx"):
                et_log.info(f"{file_name} Excel xlsx表格")
                return "xlsx"
            else:
                et_log.info(f"{file_name} ZIP 压缩文件")
                return "zip"
        else:
            et_log.info(f"{file_name} 未知文件类型")
            return None
    except Exception as e:
        et_log.info(f"{file_name} 读取文件时出错: {e}")
        return None


# 根据关键词过滤树形结构
def filter_tree_by_keyword(tree: list, keyword: str = "", key="name") -> list:
    if not tree or not keyword:
        return tree

    def contains_keyword(node, keyword):
        """判断当前节点是否包含关键词"""
        if keyword.lower() in node[key].lower():
            return True
        for child in node.get("children", []):
            if contains_keyword(child, keyword):
                return True
        return False

    def traverse(node, keyword, include_ancestors=False):
        """
        遍历树并收集匹配的节点。

        :param node: 当前遍历的节点
        :param keyword: 关键词
        :param include_ancestors: 是否因为子节点匹配而包含此节点
        :return: 包含匹配节点的新树的一部分
        """
        new_children = []
        match_in_children = False

        # 检查当前节点是否有符合条件的子节点
        for child in node.get("children", []):
            filtered_child = traverse(child, keyword)
            if filtered_child is not None:
                new_children.append(filtered_child)
                match_in_children = True

        # 如果当前节点包含关键词，或者它的任何子节点包含关键词，或者需要包含祖先节点时
        if contains_keyword(node, keyword) or match_in_children or include_ancestors:
            new_node = node.copy()
            if new_children:  # 如果有符合条件的子节点，更新子节点列表
                new_node["children"] = new_children
            else:
                # 如果没有符合的子节点，检查原node是否有子节点决定是否保留'children'键
                if not node.get("children"):
                    new_node.pop("children", None)
            return new_node
        return None

    # 过滤原始树中的节点
    filtered_tree = []
    for root in tree:
        filtered_root = traverse(root, keyword)
        if filtered_root is not None:
            filtered_tree.append(filtered_root)

    return filtered_tree


def get_label_from_tree_by_id(_id: str, tree: list, label_key="name") -> str:
    """
    从树形结构中获取标签
    :param _id: 节点ID
    :param tree: 树形结构
    :return: 标签名称
    """

    if not _id or not tree:
        return None

    for node in tree:
        if node.get("_id") == _id:
            return node.get([label_key])
        if "children" in node:
            label = get_label_from_tree_by_id(_id, node["children"])
            if label:
                return label
    return None


def get_id_from_tree_by_label(label: str, tree: list, label_key="name") -> str:
    """
    从树形结构中获取ID
    :param label: 节点标签
    :param tree: 树形结构
    :return: 节点ID
    """

    if not label or not tree:
        return None

    for node in tree:
        if node.get(label_key) == label:
            return node.get("_id")
        if "children" in node:
            _id = get_id_from_tree_by_label(label, node["children"])
            if _id:
                return _id
    return None

def extract_json_objects(text):
    """
    从文本中抽取所有非重复完整的JSON数组对象（支持嵌套结构及带注释的JSON）
    :param text: 包含JSON的原始文本
    :return: 抽取到的JSON对象列表（字典形式）
    """
    json_list = []
    depth = 0  # 大括号嵌套层级计数器
    start_idx = -1  # JSON块起始位置

    for i, char in enumerate(text):
        if char == '{':
            # 遇到左大括号时：
            # - 若当前层级为0，标记JSON块起始位置
            # - 无论层级如何，层级计数器+1
            if depth == 0:
                start_idx = i
            depth += 1

        elif char == '}':
            # 遇到右大括号时：
            # - 若层级>0，层级计数器-1
            # - 若层级回到0且存在起始位置，提取完整JSON块
            if depth > 0:
                depth -= 1
                if depth == 0 and start_idx != -1:
                    json_candidate = text[start_idx:i + 1]  # 提取完整JSON字符串
                    try:
                        # 使用json5解析（支持注释、未引号键等扩展语法）
                        json_obj = json5.loads(json_candidate)
                        json_list.append(json_obj)
                    except JSONDecodeError:
                        print(f"警告：无效JSON片段（跳过）: {json_candidate[:50]}...")
                    except Exception as e:
                        print(f"处理异常: {str(e)}")
                    start_idx = -1  # 重置起始位置
    # 初始化空集合（使用set()或{}均可，注意空集合不能用{}）
    json_set = set()

    # 遍历JSON列表，将每个对象转换为字符串后添加到集合
    for json_obj in json_list:
        # 使用json.dumps确保字符串格式化一致（排序键避免顺序不同导致的差异）
        json_str = json.dumps(json_obj, sort_keys=True, ensure_ascii=False)
        json_set.add(json_str)  # 集合自动去重

    return [json.loads(str(json_str)) for json_str in json_set]

if __name__ == "__main__":
    text = """
    ```json
    {
        "eval_score": 80,
        "score_reason": {
            "fact_correctness": "实际回答中提供的API指令'PlayZhaoLiYingTVShows'能够直接满足用户观看赵丽颖主演电视剧的需求，且逻辑清晰，无事实性错误。智能助理响应逻辑也合理，考虑了资源获取成功与失败两种情况的处理方式。",
            "meet_demand": "实际回答覆盖了用户的核心需求，即观看赵丽颖主演的电视剧，并且提供了直接的播放指令。虽然目前API未支持更个性化的推荐，但已提出扩展建议，显示出一定的灵活性和未来改进的空间。",
            "feasibility_responsible": "提供的API指令和智能助理响应逻辑在技术上是可行的，且考虑了资源不可用时的备选方案，显示出负责任的态度。然而，对于个性化推荐的扩展建议，虽然提出了方向，但尚未实现，因此在可行性与负责任态度上略有扣分。"
        }
    }
    ```
    """
    json_arr = extract_json_objects(text)
    print(json_arr)
