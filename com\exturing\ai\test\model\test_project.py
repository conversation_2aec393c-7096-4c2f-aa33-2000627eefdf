from bson import ObjectId

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 项目
class TestProject(BaseModel):

    _doc = "test_project"

    pid: str # 父项目id
    project_name: str  # 项目名称
    project_code: str  # 项目代码
    oem_code: str  # 车厂代码
    oem_name: str  # 车厂名称
    vehicle_model: str  # 车型
    project_desc: str  # 项目描述
    is_last: int # 是否无子节点标识

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", pid="", project_name="", project_code="",
                 oem_code="", oem_name="", vehicle_model="", project_desc="", is_last=1):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.pid = pid
        self.project_name = project_name
        self.project_code = project_code
        self.oem_code = oem_code
        self.oem_name = oem_name
        self.vehicle_model = vehicle_model
        self.project_desc = project_desc
        self.is_last = is_last
        self.oem_codes = oem_codes

    def to_json(self):
        base_json = super().to_json()
        base_json["pid"] = ObjectId(str(self.pid)) if self.pid and len(str(self.pid)) > 0 else ""
        base_json["project_name"] = self.project_name
        base_json["project_code"] = self.project_code
        base_json["oem_code"] = self.oem_code
        base_json["oem_name"] = self.oem_name
        base_json["vehicle_model"] = self.vehicle_model
        base_json["project_desc"] = self.project_desc
        base_json["is_last"] = self.is_last
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["pid"] = str(self.pid) if self.pid and len(str(self.pid)) > 0 else ""
        return data_json


