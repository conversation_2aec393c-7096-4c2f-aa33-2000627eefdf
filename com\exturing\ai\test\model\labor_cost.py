from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class LaborCostModel(BaseDataModel):
    type: Optional[str] = None  # 人力类型 1：正式 2：外包
    base: Optional[str] = None  # 人力base地
    level: Optional[str] = None  # 人力级别
    role: Optional[str] = None  # 人力角色
    cost: Optional[float] = None  # 人力成本 人天
    desc: Optional[str] = None  # 成本描述


class LaborCostQueryModel(BaseModel):
    type: Optional[str] = None
    role: Optional[str] = None
    base: Optional[str] = None

    def get_query_condition(self):
        condition = {}

        if self.type:
            condition["type"] = self.type

        if self.role:
            condition["role"] = self.role

        if self.base:
            condition["base"] = self.base

        return condition
