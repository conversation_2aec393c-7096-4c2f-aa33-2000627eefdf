import time
import uuid


class CurriedJoin:
    """
    A class that allows for a curried join operation on strings.
    """

    def __init__(self, initial=""):
        self.parts = [initial] if initial else []

    def __call__(self, part: str = "") -> "CurriedJoin":
        if part:
            self.parts.append(part)
        return self

    def result(self) -> str:
        return "".join(self.parts)

    def __str__(self):
        return self.result()


def get_chat_id() -> str:
    """
    生成聊天ID
    :return: 聊天ID
    """
    return str(uuid.uuid4()).replace("-", "")
