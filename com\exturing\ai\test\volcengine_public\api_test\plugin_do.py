from com.exturing.ai.test.volcengine_public.api_test.intent import intent_api
from com.exturing.ai.test.volcengine_public.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_chat_id
from com.exturing.ai.test.volcengine_public.volcengine_client import VolcengineClient

api_path = "/dpfm/v1/plugin/do/stream"


def plugin_do_api():
    """
    请求插件接口测试
    """

    with VolcengineClient() as client:
        response = client.stream_post(
            path=api_path,
            data=test_data,
        )

        answer = CurriedJoin()

        for chunk in response:
            if chunk:
                print("Chunk:", chunk)
                if chunk.get("content"):
                    answer(chunk["content"])

        print("Response:", answer.result())


if __name__ == "__main__":
    test_data = {
        "chat_id": get_chat_id(),
        "query": "上海明天是什么天气？",
    }
    # 先请求意图识别接口
    res = intent_api(test_data)

    test_data["question_id"] = res.get("question_id")
    test_data["intent_type"] = res.get("intent_type")
    test_data["intent_log_id"] = res.get("log_id")

    # 请求插件接口
    plugin_do_api()

# python -m com.exturing.ai.test.volcengine_public.api_test.plugin_do
