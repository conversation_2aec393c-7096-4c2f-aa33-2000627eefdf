from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.file_service import upload

file = Blueprint("file", __name__)


@file.route(f"/{URL_PREFIX}/file/upload", methods=["POST"])
def file_upload():
    if not request.files.get("file"):
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "file is null", "").to_json()

    file = request.files["file"]

    url = upload(file)

    if not url:
        return ApiResult(ResultCode.FAILURE.code, "upload file fail", "").to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, url).to_json()
