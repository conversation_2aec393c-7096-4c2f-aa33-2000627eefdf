# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.data_tag import DataTagModel, DataTagQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.data_tag_service import create, query_page, update, delete

data_tag = Blueprint('data_tag', __name__)

# 新增数据标签
@data_tag.route(f'/{URL_PREFIX}/data-tag/create', methods=['POST'])
def data_tag_create():
    et_log.info("############data_tag_create################")
    req_data = request.get_json()
    data_tag = DataTagModel(**req_data)
    data_tag_id = create(data_tag)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(data_tag_id)).to_json()

# 数据标签分页查询
@data_tag.route(f'/{URL_PREFIX}/data-tag/page', methods=['POST'])
def data_tag_page():
    et_log.info("############data_tag_page################")
    data = request.get_json()
    query = DataTagQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改数据标签
@data_tag.route(f'/{URL_PREFIX}/data-tag/update', methods=['POST'])
def data_tag_update():
    et_log.info("############data_tag_update################")
    req_data = request.get_json()
    id = req_data.get("data_tag_id")

    if(id is None or len(id) < 1):
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_tag_id is null", "").to_json()

    data_tag = DataTagModel(**req_data)
    data_tag_id = update(id, data_tag)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(data_tag_id)).to_json()

# 删除数据标签
@data_tag.route(f'/{URL_PREFIX}/data-tag/del', methods=['POST'])
def data_tag_delete():
    et_log.info("############data_tag_delete################")
    req_data = request.get_json()
    id = req_data.get("data_tag_id")

    if(id is None or len(id) < 1):
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_tag_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()