# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.business import BusinessModel, BusinessQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.business_service import create, query_page, update, delete, file_import

business = Blueprint('business', __name__)

# 新增业务
@business.route(f'/{URL_PREFIX}/business/create', methods=['POST'])
def business_create():
    et_log.info("############business_create################")
    req_data = request.get_json()
    business_instance = BusinessModel(**req_data)
    business_id = create(business_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(business_id)).to_json()

# 业务分页查询
@business.route(f'/{URL_PREFIX}/business/page', methods=['POST'])
def business_page():
    et_log.info("############business_page################")
    data = request.get_json()
    query = BusinessQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改业务
@business.route(f'/{URL_PREFIX}/business/update', methods=['POST'])
def business_update():
    et_log.info("############business_update################")
    req_data = request.get_json()
    id = req_data.get("business_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "business_id is null", "").to_json()

    business_instance = BusinessModel(**req_data)
    update_result = update(id, business_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除业务
@business.route(f'/{URL_PREFIX}/business/del', methods=['POST'])
def business_delete():
    et_log.info("############business_delete################")
    req_data = request.get_json()
    id = req_data.get("business_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "business_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()

# 导入业务数据
@business.route(f'/{URL_PREFIX}/business/import', methods=['POST'])
def business_import():
    et_log.info("############business_import################")
    if 'excel_file' in request.files:
        uploaded_file = request.files['excel_file']
        # 先判断文件类型是否是Excel（简单判断扩展名，实际应用中可更严谨判断）
        if uploaded_file.filename.endswith('.xlsx') or uploaded_file.filename.endswith('.xls'):
            file_import(uploaded_file)
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "excel_file is null", "").to_json()