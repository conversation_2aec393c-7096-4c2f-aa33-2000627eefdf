from enum import Enum
from typing import List, Optional

class WeekEnum(Enum):
    """
    周 枚举
    """
    MONDAY = (1, "Monday", "mon", "星期一")
    TUESDAY = (2, "Tuesday", "tue", "星期二")
    WEDNESDAY = (3, "Wednesday", "wed", "星期三")
    THURSDAY = (4, "Thursday", "thu", "星期四")
    FRIDAY = (5, "Friday", "fri", "星期五")
    SATURDAY = (6, "Saturday", "sat", "星期六")
    SUNDAY = (7, "Sunday", "sun", "星期日")

    @classmethod
    def get_all_elements(cls):
        return [element for element in cls]

    @classmethod
    def get_element_by_value(cls, value):
        for element in cls:
            if element.value[0] == value:
                return element
        return None

    @property
    def wk_value(self):
        return self.value[0]

    @property
    def en_name(self):
        return self.value[1]

    @property
    def en_value(self):
        return self.value[2]

    @property
    def cn_name(self):
        return self.value[3]


class RoleEnum(Enum):
    """Role enumeration"""
    DEFAULT = ''
    XIAO_HU_LI = '知性小狐'
    XIAO_LA_JIAO = '小辣椒'
    XIN_YA = '新芽'
    XIAO_LI = '小狸'
    TAN_KE = 'GWM_TANK_P03'
    DONG_MEI = 'sp_dm'
    XIAO_CHI = 'sp_xc'


class OemEnum(Enum):
    """OEM enumeration"""
    BEI_QI = 'baic'
    CHANG_CHENG = 'gwm'
    WU_LIN = 'SGMW'
    BEN_TENG = 'bestune'
    QI_RUI = 'chery'
    CARIAD = 'CARIAD'


class DskVersion(Enum):
    """DSK Version enumeration"""
    V10 = "1.0"
    V11 = "1.1"


class RequestConfig:
    """Request configuration class"""
    def __init__(self):
        self.role: str = RoleEnum.DEFAULT.value
        self.oem: str = OemEnum.CHANG_CHENG.value
        self.dsk_version: DskVersion = DskVersion.V11


class ResDto:
    """Response DTO class"""
    def __init__(self):
        self.display_text: Optional[str] = None
        self.speak: Optional[str] = None
        self.block_text: Optional[str] = None
        self.cmd: Optional[str] = None
        self.has_error: bool = False
        self.blocks: Optional[List] = None

    def __str__(self) -> str:
        return f"""display_text = {self.display_text},
                block_text = {self.block_text},
                cmd = {self.cmd},
                speak = {self.speak}
                blocks = {self.blocks}
                """