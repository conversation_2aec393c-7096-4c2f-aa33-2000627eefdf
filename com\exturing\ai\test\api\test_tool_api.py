# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.test_tool import TestToolModel, TestToolQueryModel
from com.exturing.ai.test.comm.api_result import ApiR<PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_tool_service import create, query_page, update, delete

test_tool = Blueprint('test_tool', __name__)

# 新增工具
@test_tool.route(f'/{URL_PREFIX}/test-tool/create', methods=['POST'])
def test_tool_create():
    et_log.info("############test_tool_create################")
    req_data = request.get_json()
    test_tool_instance = TestToolModel(**req_data)
    test_tool_id = create(test_tool_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(test_tool_id)).to_json()

# 工具分页查询
@test_tool.route(f'/{URL_PREFIX}/test-tool/page', methods=['POST'])
def test_tool_page():
    et_log.info("############test_tool_page################")
    data = request.get_json()
    query = TestToolQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改工具
@test_tool.route(f'/{URL_PREFIX}/test-tool/update', methods=['POST'])
def test_tool_update():
    et_log.info("############test_tool_update################")
    req_data = request.get_json()
    id = req_data.get("test_tool_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_tool_id is null", "").to_json()

    test_tool_instance = TestToolModel(**req_data)
    update_result = update(id, test_tool_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除工具
@test_tool.route(f'/{URL_PREFIX}/test-tool/del', methods=['POST'])
def test_tool_delete():
    et_log.info("############test_tool_delete################")
    req_data = request.get_json()
    id = req_data.get("test_tool_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_tool_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()