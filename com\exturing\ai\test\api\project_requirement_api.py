# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.project_requirement import (
    ProjectRequirementModel,
    ProjectRequirementQueryModel,
)
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.project_requirement_service import (
    create,
    query_page,
    query_tree,
    update,
    delete,
    import_data,
)

project_requirement = Blueprint("project_requirement", __name__)


# 新增项目需求
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/create", methods=["POST"]
)
def project_requirement_create():
    et_log.info("############project_requirement_create################")
    req_data = request.get_json()
    project_requirement_instance = ProjectRequirementModel(**req_data)
    project_requirement_id = create(project_requirement_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(project_requirement_id)
    ).to_json()


# 项目需求分页查询
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/page", methods=["POST"]
)
def project_requirement_page():
    et_log.info("############project_requirement_page################")
    data = request.get_json()
    query = ProjectRequirementQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 项目需求树查询
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/tree", methods=["POST"]
)
def project_requirement_tree():
    et_log.info("############project_requirement_tree################")

    data = request.get_json()
    query = ProjectRequirementQueryModel(**data)

    json_tree = query_tree(query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, json_tree
    ).to_json()


# 修改项目需求
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/update", methods=["POST"]
)
def project_requirement_update():
    et_log.info("############project_requirement_update################")
    req_data = request.get_json()
    id = req_data.get("project_requirement_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "project_requirement_id is null", ""
        ).to_json()

    project_requirement_instance = ProjectRequirementModel(**req_data)
    update_result = update(id, project_requirement_instance)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
    ).to_json()


# 删除项目需求
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/del", methods=["POST"]
)
def project_requirement_delete():
    et_log.info("############project_requirement_delete################")
    req_data = request.get_json()
    id = req_data.get("project_requirement_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "project_requirement_id is null", ""
        ).to_json()

    try:
        delete_result = delete(id)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 项目需求导入
@project_requirement.route(
    f"/{URL_PREFIX}/staff/project-requirement/import", methods=["POST"]
)
def project_requirement_import():
    et_log.info("############project_requirement_import################")

    if "excel_file" in request.files:
        uploaded_file = request.files["excel_file"]
        project_id = request.form.get("project_id")

        if not project_id:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
            ).to_json()

        if uploaded_file.filename.endswith(".xlsx") or uploaded_file.filename.endswith(
            ".xls"
        ):
            result = import_data(project_id, uploaded_file)

            if result:
                return ApiResult(
                    ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result
                ).to_json()
            else:
                return ApiResult(
                    ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "导入失败"
                ).to_json()
        else:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "excel_file is not xlsx or xls", ""
            ).to_json()
    else:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "excel_file is null", ""
        ).to_json()
