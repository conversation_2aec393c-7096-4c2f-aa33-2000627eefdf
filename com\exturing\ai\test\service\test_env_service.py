import asyncio
import time
import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, PROMPT_TMP_EVAL_CODE
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.model.test_env import TestEnv

# 测试环境Service
class TestEnvService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"test_env insert_one post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            env_name = post_data["env_name"]
            env_code = post_data["env_code"]
            bol_val = cls.check_insert_env_repeat(env_name, env_code)
            if bol_val:
                et_log.error(f"test_env insert_one env_name:{env_name} env_code:{env_code} has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestEnv("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                ctx_oem_codes, env_name, env_code, post_data["env_type"], post_data["env_script"], post_data["env_uri"],
                                post_data["branch_name"], post_data["env_desc"], post_data["vm_id"], post_data["pid_val"],
                                post_data["device_id"], post_data["version"], post_data["user_id"],
                                post_data["adapter_code"], post_data["oem_code"],
                                post_data.get("tmp_id", ""), post_data.get("tmp_code", "")).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"test_env insert_one exception")
            traceback.print_exc()
            return None

    @classmethod
    def check_insert_env_repeat(cls, env_name, env_code):
        et_log.info(f"check_insert_env_repeat params env_name:{env_name} env_code:{env_code}")
        try:
            condition = {}
            if env_name and len(str(env_name).strip()) > 0:
                condition["env_name"] = str(env_name).strip()
                count = TestEnv.find_condition_count(condition)
                if count > 0:
                    return True
            if env_code and len(str(env_code).strip()) > 0:
                condition["env_code"] = str(env_code).strip()
                count = TestEnv.find_condition_count(condition)
                if count > 0:
                    return True
            return False
        except Exception as e:
            et_log.error(f"check_insert_env_repeat test_env exception")
            traceback.print_exc()
            return True

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = TestEnv.find_by_pk(_id)
        if data_item is not None and len(data_item) > 0:
            return TestEnv(**data_item)
        return None

    # 查询 分页数据
    @classmethod
    def query_env_list(cls, env_name, env_code, env_type):
        et_log.info(f"test_env query_env_list params env_name:{env_name} env_code:{env_code} env_type:{env_type}")
        try:
            condition = {}
            if env_name and len(str(env_name).strip()) > 0:
                condition["env_name"] = {"$regex": str(env_name).strip(), "$options": "i"}
            if env_code and len(str(env_name).strip()) > 0:
                condition["env_code"] = env_code
            if env_type is not None and env_type > 0:
                condition["env_type"] = env_type
            # 查询匹配的数量
            total = TestEnv.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return []
            result_list = TestEnv.find_condition(condition, None, total, 0)
            if result_list is None or len(result_list) < 1:
                et_log.error(f"test_env query_env_list not found data error")
                return None
            item_json_list = []
            for item in result_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            return item_json_list
        except Exception as e:
            et_log.error(f"test_task query_env_list exception")
            traceback.print_exc()
            return None

    # 删除数据
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_env delete by _id:{_id} do_user:{do_user}")
        try:
            del_item = cls.find_pk(_id)
            if del_item is None:
                et_log.error(f"test_env delete by _id error, _id not found data")
                return False
            # 删除id对应的数据记录
            TestEnv.delete_by_id(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"test_env delete by _id exception")
            traceback.print_exc()
            return False

    # 更新
    @classmethod
    def update(cls, post_data:TestEnv):
        et_log.info(f"test_env update by post_data:{post_data}")
        try:
            # 更新-主键检查
            data:TestEnv = cls.find_pk(post_data.id)
            if data is None:
                et_log.error(f"update test_env error, _id not found data")
                return 0
            # 更新-名称和代码检查
            if cls.check_update_env_repeat(post_data.env_name, post_data.env_code, data.id):
                # 更新请求的数据项数据
                return TestEnv.update_entity_json(post_data.to_json())
            else:
                return 0
        except Exception as e:
            et_log.error(f"update by test_env exception")
            traceback.print_exc()
            return 0

    # 更新环境时，检查名称和代码的重复性
    @classmethod
    def check_update_env_repeat(cls, env_name, env_code, up_id):
        et_log.info(f"check_update_env_repeat params env_name:{env_name} env_code:{env_code} up_id:{up_id}")
        try:
            condition = {"_id": {"$ne": ObjectId(up_id)}}
            if env_name and len(str(env_name).strip()) > 0:
                condition["env_name"] = str(env_name).strip()
                count = TestEnv.find_condition_count(condition)
                if count > 0:
                    return False
            if env_code and len(str(env_code).strip()) > 0:
                condition["env_code"] = str(env_code).strip()
                count = TestEnv.find_condition_count(condition)
                if count > 0:
                    return False
            return True
        except Exception as e:
            et_log.error(f"check_update_env_repeat test_env exception")
            traceback.print_exc()
            return False

    @classmethod
    def get_comm_pt_by_code(cls, env_code):
        """
        根据环境代码env_code，获取对应评估使用的提示词模板对象;若环境未设置对应的评分模板，则使用默认评估提示词模板
        :param env_code: 环境代码
        :return: CommPromptTemplate对象
        """
        et_log.info(f"get_comm_pt_by_code env_code:{env_code}")
        try:
            if env_code and len(env_code) > 0:
                condition = {"env_code": env_code}
                env_list = TestEnv.find_condition(condition, None, None, None)
                if env_list and len(env_list) > 0:
                    env:TestEnv = env_list[0]
                    if env.tmp_id and len(env.tmp_id) > 0:
                        return CommPromptTemplateModel.find_by_pk(env.tmp_id)

            return CommPromptTemplateModel.query_by_tmp_code(PROMPT_TMP_EVAL_CODE)
        except Exception as e:
            et_log.error(f"get_comm_pt_by_code exception:{e}\n{traceback.print_stack()}")
            return None


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))

