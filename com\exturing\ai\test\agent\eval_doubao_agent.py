import re
import traceback

from volcenginesdkarkruntime import Ark

from com.exturing.ai.test.comm.comm_constant import DOUBAO_API_KEY, DOUBAO_BASE_URL, DOUBAO_BOT_MODEL
from com.exturing.ai.test.comm.log_tool import et_log



# 豆包评估智能体
class EvalDoubaoAgent:

    @classmethod
    def compute_similarity(cls, prompt_data):
        """
        根据提示词内容，进行语义相似度打分
        :param prompt_data:含问题、期望回答、实际回答和提示词描述的内容
        :return:
        """
        try:
            # 初始化 Ark 客户端
            client = Ark(
                api_key=DOUBAO_API_KEY,
                base_url=DOUBAO_BASE_URL
            )
            et_log.info(f"compute_similarity prompt_data:{prompt_data}")
            # 调用流式接口
            stream = client.bot_chat.completions.create(
                model=DOUBAO_BOT_MODEL,
                messages=[
                    {"role": "system", "content": "你是豆包，是由字节跳动开发的AI人工智能助手。"},
                    {"role": "user", "content": prompt_data}
                ],
                stream=True
            )

            # # 记录请求开始时间
            # begin_time = datetime.datetime.now()
            # i = 0
            response_content = ""  # 初始化用于保存流式输出内容

            # 处理流式响应
            for chunk in stream:
                if chunk.references:
                    et_log.info(f"引用内容: {chunk.references}")
                if not chunk.choices:
                    et_log.info("响应内容为空或格式不正确")
                    continue

                # # 计算首轮响应时间
                # if i == 0:
                #     final_time = datetime.datetime.now()
                #     single_round_time = (final_time - begin_time).total_seconds()
                #     print(f"============== This round total time (s): {single_round_time}\n")
                #     i += 1

                # 拼接流式响应内容
                content = chunk.choices[0].delta.content
                response_content += content  # 拼接内容
                # print(content, end="")
            # 将最终流式响应的整体内容保存到 DataFrame 的 24 列
            et_log.info(f"compute_similarity response_content:{response_content}")
            return response_content
        except Exception as e:
            et_log.error(f"请求失败，错误信息：{e},\n{traceback.print_exc()}")
            raise e

    @classmethod
    def get_response_score(cls, response_text):
        et_log.info(f"get_response_score response_text:{response_text}")
        try:
            # 定义正则表达式模式
            pattern = r'<output>(\d+)</output>'
            # 使用 re.search 查找匹配项
            match = re.search(pattern, response_text)

            if match:
                # 提取匹配到的数字
                score = int(match.group(1))
                et_log.info(f"get_response_score response_text:{response_text} score:{score}")
                return score
            else:
                et_log.error(f"get_response_score response_text:{response_text} not find score")
                return 0
        except Exception as e:
            et_log.error(f"get_response_score exception:{e},\n{traceback.print_exc()}")
            return 0

    @classmethod
    def compare_text_semantics(cls, question, expected_answer, actual_answer):
        """
        根据问题question、期望回答expected_answer、实际回答actual_answer，对实际回答进行语义相似度打分
        :param question: 问题
        :param expected_answer: 期望回答
        :param actual_answer: 实际回答
        :return:语义相似度分值，100分制
        """
        compare_data = """
        请根据问题，判断回答2与回答1的语义相似性，并打以100分制，进行打分；如果无回答1，则根据问题和回答2的相关性，对回答2的回答内容进行打分即可。
        参考样例：
        问题:请对电影《天若有情》进行评价？
        回答1：这部电影的剧情非常感人，我看得热泪盈眶。
        回答2：这部电影的情节十分动人，我感动得流下了眼泪。
        <output>100</output>

        问题:请对电影《天若有情》进行评价？
        回答1：
        回答2：这部电影的情节十分动人，我感动得流下了眼泪。
        <output>100</output>

        请将结果放在<output>打分结果</output>标签之间，作为接口返回，请对下述内容进行打分。
        问题:{question}
        回答1:{answer1}
        回答2:{answer2}
        """
        params = {"question":question, "answer1":expected_answer, "answer2":actual_answer}
        prompt_data = compare_data.format(**params)
        result = cls.compute_similarity(prompt_data)
        return cls.get_response_score(result)




# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我钟情于节奏轻快的流行音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"我偏爱旋律优美的古典音乐。", "answer2":"我喜欢古典音乐。"}
# params = {"question":"你喜欢什么类型的音乐？", "answer1":"", "answer2":"我喜欢古典音乐。"}
# print(EvalDoubaoAgent.compare_text_semantics("你喜欢什么类型的音乐？", "", "我喜欢古典音乐。"))

