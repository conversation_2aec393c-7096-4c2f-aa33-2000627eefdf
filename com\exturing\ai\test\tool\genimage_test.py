import contextvars
import os
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.comm_constant import ST_URL, CTX_USER_ID, DIC_GROUP_SYS_CONF, \
    DIC_CODE_MAX_T2I, CTX_USER_T2I_COUNT
from com.exturing.ai.test.adapter.dsk_agent_autotest_connector import has_done, get_url_text, get_display_text
from com.exturing.ai.test.agent.eval_txtimage_test_agent import ImageTextAPI
from com.exturing.ai.test.comm.oss_util import OssUtil
from typing import Dict, List
import json
import uuid
from datetime import datetime
import websockets
import traceback
import time
import asyncio
import aiohttp
from http import HTTPStatus
from dashscope import ImageSynthesis
from com.exturing.ai.test.comm.comm_constant import HUOSHAN_API_KEY, HUOSHAN_SECRET_KEY, HUOSHAN_CHANNNEl, \
    HUOSHAN_APPID, HUOSHAN_VEHICLE_ID, HUOSHAN_intent_URL, HUOSHAN_PLAGIN_URL, QIANFAN_MODELCONTROL_API_KEY, \
    ENINE_IRAG_URL, QIANFANG_API_KEY, QIANFANG_SECRET_KEY
import hashlib
import hmac
from hashlib import sha256
import base64
import random
import urllib.parse
import sys
from com.exturing.ai.test.model.dic_info import DicInfo
import requests
from com.exturing.ai.test.tool.entranslate import BaiduTranslateAPI
from io import BytesIO

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
class GenImageTest:
    @classmethod
    async def main(cls, model_list: List[str], prompt: str):
        """使用异步协程并发执行任务"""
        count: int
        flag: bool
        flag, count = cls.check_t2i_count()
        # flag, count = True, 300 # 演示用
        final_res = {}
        if not flag:
            et_log.error(f"GenImageTest check_t2i_count False")
            return []
        request_id = cls.get_uuid()
        text_data = [("texts", prompt)]
        tasks = [cls.run_model_task(model, prompt, text_data) for model in model_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                et_log.error(f"任务失败: {str(result)}")
            elif result:
                valid_results.append(result)
                et_log.info(f"任务完成: {result}")
                COUNT_CTX = CTX_USER_T2I_COUNT.get()
                COUNT_CTX[str(CTX_USER_ID.get()) + "_img"] = datetime.now().strftime("%Y%m%d")+"_"+str(count+1)
                CTX_USER_T2I_COUNT.set(COUNT_CTX)

        final_res = {
            "request_id": request_id,
            "question": prompt,
            "result": valid_results
        }
        return final_res

    @classmethod
    def check_t2i_count(cls):
        """
        校验用户文生图t2i单日次数
        :return: True=可再生 False=不可再生 | count=当前次数
        """
        day = datetime.now().strftime("%Y%m%d")
        # 请求上下文中的[user_id]_t2i=count
        cur_ctx_key = f"{CTX_USER_ID.get()}_img"
        COUNT_CTX = CTX_USER_T2I_COUNT.get()
        if cur_ctx_key not in COUNT_CTX:
            et_log.info(f"check_t2i_count user key is null")
            COUNT_CTX[cur_ctx_key] = day + "_0"
            CTX_USER_T2I_COUNT.set(COUNT_CTX)
            return True, 0

        val_key = CTX_USER_T2I_COUNT.get()[cur_ctx_key]
        if not val_key or len(val_key) == 0 or len(val_key.split("_")) != 2:
            et_log.info(f"check_t2i_count history count: 0")
            COUNT_CTX[cur_ctx_key] = day + "_0"
            CTX_USER_T2I_COUNT.set(COUNT_CTX)
            return True, 0
        his_day = val_key.split("_")[0]
        count = int(val_key.split("_")[1])
        if his_day != day:
            et_log.info(f"check_t2i_count history expire")
            COUNT_CTX[cur_ctx_key] = day + "_0"
            CTX_USER_T2I_COUNT.set(COUNT_CTX)
            return True, 0
        # 获取限制数
        dic_list = DicInfo.find_condition({"code": DIC_CODE_MAX_T2I, "group_code": DIC_GROUP_SYS_CONF}, None,
                                          None, None)
        if not dic_list or len(dic_list) == 0:
            et_log.info(f"check_t2i_count error, not found max_dic")
            COUNT_CTX[cur_ctx_key] = day + "_0"
            CTX_USER_T2I_COUNT.set(COUNT_CTX)
            return True, 0
        max_count = dic_list[0].order_by
        if (count+1) > max_count:
            et_log.info(f"check_t2i_count error, user use t2i > {max_count}")
            return False, count
        else:
            et_log.info(f"check_t2i_count success, user use t2i count:{count} max_count:{max_count}")
            return True, count

    @classmethod
    async def run_model_task(cls, model: str, prompt: str, text_data: list):
        """根据模型类型分发任务"""
        # if model == "doubao_image":
        #     return await cls.doubao_image_task(prompt, text_data)
        if model == "ernie_irag":#百度千帆:文生图
            return await cls.ernie_irag_imag(prompt, text_data)
        elif model == "st":#商汤
            return await cls.st_task(prompt, text_data)
        elif model == "FLUX.1-schnell":#百度千帆:FLUX.1-schnell
            return await cls.flux1_schnell_task(prompt, text_data)
        elif model == "Stable-Diffusion-XL":#百度千帆:Stable-Diffusion-XL
            return await cls.stable_diffusion_xl_img(prompt, text_data)
        return None

    @classmethod
    async def doubao_image_task(cls, prompt: str, text_data: list):
        """豆包模型图像生成任务"""
        try:
            et_log.info("开始使用豆包模型生成图像...")
            question_id = await cls.get_questionid(prompt)
            if question_id:
                start_time = time.time()
                doubaoimage = await cls.get_doubao_url(prompt, question_id)
                end_time = time.time()
                res_time = round(float(end_time-start_time),2)
                if doubaoimage:
                    name = cls.get_uuid()
                    doubaoimage_url = cls.convert_to_permanent_url(name, doubaoimage)
                    if doubaoimage:
                        et_log.info(f"豆包模型永久URL生成成功: {doubaoimage}")
                        image_paths = [doubaoimage]
                        api = ImageTextAPI(image_paths, text_data)
                        api_result = api.send_request()
                        return {
                            "image": doubaoimage,
                            "image_key": doubaoimage_url,
                            "model": "doubao_image",
                            "score": (
                                float(api_result[0])
                                if isinstance(api_result, (list, tuple))
                                else 0.5
                            ),
                            "res_time": res_time
                        }
                else:
                    raise Exception(f"豆包生图失败, 错误信息: 抱歉，无法生成你要求的图片")
        except Exception as e:
            et_log.error(f"doubao_image 模型处理异常: {str(e)}")

    @classmethod
    async def ernie_irag_imag(cls, prompt: str, text_data: list):
        """ernie_irag 模型图像生成"""
        try:
            et_log.info("开始使用 ernie_irag 模型生成图像...")
            start_time = time.time()
            ernie_irag_img = await cls.ernie_irag(prompt)
            end_time = time.time()
            res_time = round(float(end_time-start_time),2)
            if ernie_irag_img:
                name = cls.get_uuid()
                ernie_irag_img_url = cls.convert_to_permanent_url(name, ernie_irag_img)
                if ernie_irag_img:
                    et_log.info(f"ernie_irag模型永久URL生成成功: {ernie_irag_img_url}")
                image_paths = [ernie_irag_img]
                api = ImageTextAPI(image_paths, text_data)
                api_result = api.send_request()
                return {
                    "build_param":{"size":"1024*1024"},
                    "image": ernie_irag_img,
                    "image_key": ernie_irag_img_url,
                    "model": "ernie_irag",
                    "score": (
                        float(api_result[0])
                        if isinstance(api_result, (list, tuple))
                        else 0.5
                    ),
                    "res_time": res_time
                }
            else:
                raise Exception(f"百度生图失败, 错误信息: 抱歉，无法生成你要求的图片")
        except Exception as e:
            et_log.error(f"ernie_irag 模型处理异常: {str(e)}")

    @classmethod
    async def st_task(cls, prompt: str, text_data: list):
        """ST 模型图像生成任务"""
        try:
            et_log.info("开始使用 ST 模型生成图像...")
            start_time = time.time()
            result = await cls.run_st_imagegen(prompt)
            end_time = time.time()
            res_time = round(float(end_time-start_time),2)
            print(result)
            if result != "N/A":
                name = cls.get_uuid()
                permanent_url = cls.convert_to_permanent_url(name, result)
                if result:
                    et_log.info(f"ST模型永久URL生成成功: {result}")
                    image_paths = [result]
                    api = ImageTextAPI(image_paths, text_data)
                    api_result = api.send_request()
                    return {
                        "build_param": {"size": "1024*1024"},
                        "image": result,
                        "image_key": permanent_url,
                        "model": "st",
                        "score": (
                            float(api_result[0])
                            if isinstance(api_result, (list, tuple))
                            else 0.5
                        ),
                        "res_time": res_time
                    }
            else:
                raise Exception(f"商汤生图失败, 错误信息: 抱歉，无法生成你要求的图片")
        except Exception as e:
            et_log.error(f"ST 模型处理异常: {str(e)}")

    @classmethod
    async def flux1_schnell_task(cls,prompt: str, text_data: list):
        try:
            et_log.info("开始使用 FLUX.1-schnell模型生成图像...")
            start_time = time.time()
            flux1_schnell_image = await cls.flux1_schnell_img(prompt)

            end_time = time.time()
            res_time = round(float(end_time-start_time),2)
            if flux1_schnell_image and flux1_schnell_image != "N/A":
                name = cls.get_uuid()
                flux1_schnell_image_url = cls.convert_to_permanent_url(name, flux1_schnell_image)
                if flux1_schnell_image_url:
                    et_log.info(f"FLUX.1-schnell模型生图永久url生成成功：{flux1_schnell_image_url}")
                image_paths = [flux1_schnell_image]
                api = ImageTextAPI(image_paths, text_data)
                api_result = api.send_request()
                return {
                    "build_param": {"size": "1024*1024"},
                    "image": flux1_schnell_image,
                    "image_key": flux1_schnell_image_url,
                    "model": "FLUX.1-schnell",
                    "score": (
                        float(api_result[0])
                        if isinstance(api_result, (list, tuple))
                        else 0.5
                    ),
                    "res_time": res_time
                }
            else:
                raise Exception(f"FLUX.1-schnell模型生图失败")
        except Exception as e:
            et_log.error(f"FLUX.1-schnell模型异常：{str(e)}")



    @classmethod
    async def flux1_schnell_img(cls,prompt):
        url = "https://qianfan.baidubce.com/v2/images/generations"
        prompt_eng = BaiduTranslateAPI().analyze_corpus(prompt)
        if prompt_eng:
            payload = json.dumps({
                "prompt": prompt_eng,
                "model": "irag-1.0"
            }, ensure_ascii=False)
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer bce-v3/ALTAK-C4aC0sePgOrgnQgqVdubR/84edf53990c45d2440cb969c35de3eaf08387952'
        }
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.post(url, headers=headers, data=payload.encode("utf-8")) as response:
                    response_data = await response.json()
                    return response_data.get("data", [{}])[0].get("url")
        except Exception as e:
            et_log.error(f"flux1_schnell_img 请求失败: {str(e)}")
            return None

    @classmethod
    def get_access_token(cls,ak,sk):
        """
        使用 AK，SK 生成鉴权签名（Access Token）
        :return: access_token，或是None(如果错误)
        """
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {"grant_type": "client_credentials", "client_id": ak, "client_secret": sk}
        return str(requests.post(url, params=params).json().get("access_token"))

    @classmethod
    async def stable_diffusion_xl_img(cls,prompt, text_data):
        try:
            url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/text2image/sd_xl?access_token=" + cls.get_access_token(ak=QIANFANG_API_KEY, sk=QIANFANG_SECRET_KEY)
            payload = json.dumps({
                "prompt": prompt,
                "size": "1024x1024",
                "n": 1,
                "steps": 20,
                "sampler_index": "Euler a",
                "style": "Anime"
            }, ensure_ascii=False)
            headers = {
                'Content-Type': 'application/json',
                'Accept' : 'application/json'
            }
            start_time = time.time()
            # response = requests.request("POST",url,headers=headers, data= payload.encode("utf-8"))
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=payload.encode("utf-8")) as response:
                    response_data = await response.json()
            # response_data = response.json()
                    b64_image_data = response_data.get('data',[{}])[0].get('b64_image',"N/A")
            end_time = time.time()
            res_time = round(float(end_time-start_time),2)
            if b64_image_data != "N/A":
                name = cls.get_uuid()
                decodeed_image = base64.b64decode(b64_image_data)
                # image = Image.open(BytesIO(decodeed_image))
                image_file_object = BytesIO(decodeed_image)
                permanent_url = cls.imagefile_to_oss_url(name,image_file_object)
                if permanent_url:
                    et_log.info(f"Stable-Diffusion-XL模型生图永久url成功：{permanent_url}")
                image_paths = [permanent_url]
                api = ImageTextAPI(image_paths, text_data)
                api_result = api.send_request()
                return {
                    "build_param": {"size": "1024*1024", "style": "Anime"},
                    "image": permanent_url,
                    "image_key": permanent_url,
                    "model": "Stable-Diffusion-XL",
                    "score": (
                        float(api_result[0])
                        if isinstance(api_result, (list, tuple))
                        else 0.5
                    ),
                    "res_time": res_time
                }
            else:
                raise Exception(f"Stable-Diffusion-XL模型生图失败")
        except Exception as e:
            et_log.error(f"Stable-Diffusion-XL模型异常：{str(e)}")


    @classmethod
    def convert_to_permanent_url(cls, name, temp_url):
        """将临时OSS URL转换为永久URL"""
        try:
            new_oss_key = f"test_ai_eval/images/{name}.png"
            permanent_url = OssUtil.upload_url_file(temp_url, new_oss_key)
            et_log.info(f"permanent_url:{permanent_url}")
            return permanent_url if permanent_url else ""
        except Exception as e:
            et_log.error(f"URL转换失败: {str(e)}")
            return ""

    @classmethod
    def imagefile_to_oss_url(cls,name,file_object):
        """将图片文件对象转为永久url"""
        try:
            new_oss_key = f"test_ai_eval/images/{name}.png"
            permanent_url = OssUtil.upload_file(file_object, new_oss_key)
            et_log.info(f"permanent_url:{permanent_url}")
            return permanent_url if permanent_url else ""
        except Exception as e:
            et_log.error(f"URL转换失败：{str(e)}")
            return ""


    @classmethod
    def gen_sign(cls, method, querystr, sk, body=None):
        """
        :param method: 请求方式
        :param querystr: 请求内容
        :param body: 请求参数
        :param sk: sk
        :return: 加密后的数据
        """
        querystr = urllib.parse.quote(querystr, safe=':/&=')
        a = querystr.split("&")
        a.sort()
        sortedquerystr = "&".join(a)
        strtosign = method + "\n" + sortedquerystr + "\n"
        if body is not None and len(body) > 0:
            m = hashlib.md5()
            m.update(body.encode("utf8"))
            strtosign += m.hexdigest() + "\n"
        h = hmac.new(sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
        return base64.b64encode(h).decode()

    @classmethod
    async def run_st_imagegen(cls, prompt):
        et_log.info(f"run_st_imagegen config info:{prompt}")
        cmd = ''
        require_link = ''
        res_time = 0
        info_time = None
        uri = ST_URL
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(script_dir, "input_template.json")

        input_json = cls.load_json_file(file_path)
        input_json['session']['sessionId'] = cls.get_uuid()
        input_json['context']['device']['deviceName'] = f'TEXT_IMAGE_{cls.get_uuid()}'
        input_json['context']['product']['productId'] = "279623278"
        input_json['context']['product']['oem']['code'] = "SGMW"
        input_json['context']['product']['oem']['name'] = "长城"
        input_json['request']['task'] = '壁纸生成'
        input_json['context']['product']['aliasKey'] = 'LLM_TEST'
        input_json['request']['inputs'][0]['input'] = prompt
        input_json['request']['timestamp'] = int(datetime.now().timestamp())
        input_json['request']['recordId'] = cls.get_uuid()
        input_json['request']['request_id'] = cls.get_uuid()
        input_json['session']['sessionId'] = cls.get_uuid()
        try:
            async with websockets.connect(uri, open_timeout=180, close_timeout=180) as websocket:
                start_time = time.time()
                et_log.info(f"dsk_prompt send data:{input_json}")
                await asyncio.wait_for(websocket.send(json.dumps(input_json)), 60)
                while True:
                    message = await asyncio.wait_for(websocket.recv(), 60)
                    res = json.loads(message)
                    done = has_done(res)
                    if 'streamType": "intermediate' in str(res):
                        info_time = time.time()
                    if done:
                        if 'response' in res and 'execute' in res['response']:
                            cmd += str(res['response']['execute'])
                        et_log.info(f"dsk_prompt response res:{res}")
                        display_text = get_display_text(res)
                        require_link = get_url_text(res)
                        done_time = time.time()
                        asrend_time = (done_time - start_time) * 1.00
                        if info_time is not None:
                            res_time = (info_time - start_time) * 1.00
                        break
            et_log.info(
                f"dsk_prompt return diaplay:{display_text} cmd:{cmd} res_time:{res_time} require_link:{require_link} consume_time:{asrend_time}")
            return require_link
        except websockets.exceptions.ConnectionClosedOK:
            et_log.error(f"dsk_prompt ConnectionClosedOK exception,\n{traceback.print_exc()}")
            return 0
        except Exception as e:
            et_log.error(f"dsk_prompt exception:{e},\n{traceback.print_exc()}")
            return 0

    @classmethod
    async def sample_async_call(cls, model, prompt):
        rsp = ImageSynthesis.async_call(model=model,
                                        prompt=prompt,
                                        size='1024*1024')
        if rsp.status_code == HTTPStatus.OK:
            print(rsp.output)  # 打印初始响应
            print(rsp.usage)
        else:
            print('Failed, status_code: %s, code: %s, message: %s' %
                  (rsp.status_code, rsp.code, rsp.message))

        status = ImageSynthesis.fetch(rsp)
        if status.status_code == HTTPStatus.OK:
            print(status.output.task_status)  # 打印任务状态
        else:
            print('Failed, status_code: %s, code: %s, message: %s' %
                  (status.status_code, status.code, status.message))

        rsp = ImageSynthesis.wait(rsp)
        if rsp.status_code == HTTPStatus.OK:
            print("!!!!!!!!!!!")
            res = rsp.output  # 打印最终结果
            print(res)
            if res.get("task_status") == "SUCCEEDED":
                results = res.get("results", [])
                url = results[0].get("url") if results else None
                return url

        else:
            print('Failed, status_code: %s, code: %s, message: %s' %
                  (rsp.status_code, rsp.code, rsp.message))

    @classmethod
    async def ernie_irag(cls, prompt):
        url = ENINE_IRAG_URL
        payload = json.dumps({
            "prompt": prompt,
            "model": "irag-1.0"
        }, ensure_ascii=False)
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {QIANFAN_MODELCONTROL_API_KEY}"
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=payload.encode("utf-8")) as response:
                    response_text = await response.text()
                    et_log.info(f"ernie_irag response:{response_text}")
                    response_data = json.loads(response_text)
                    image_url = response_data["data"][0]["url"]
                    return image_url
        except Exception as e:
            et_log.error(f"未知错误: {str(e)}")
            return None

    @classmethod
    def load_json_file(cls, file_name: str) -> Dict:
        with open(file_name, 'r', encoding='utf-8') as f:
            res = json.load(f)
            return res

    @classmethod
    def get_uuid(cls) -> str:
        return str(uuid.uuid4())

    @classmethod
    async def get_questionid(cls, prompt):
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&channel={HUOSHAN_CHANNNEl}&app_id={HUOSHAN_APPID}&vehicle_id={HUOSHAN_VEHICLE_ID}"
        params = {
            "query": prompt,
            "chat_id": "123456789",
            "history": [{"q": "你好！", "a": "嗯"}],
            "car_info": {"vehicle_name": "Tesla Model X"}
        }
        body = json.dumps(params, ensure_ascii=False)
        sign = cls.gen_sign(method, querystr, HUOSHAN_SECRET_KEY, body)
        headers = {
            "X-Signature": f'{HUOSHAN_API_KEY}:{sign}',
            "X-Use-PPE": "1",
            "X-Tt-Env": "ppe_vehicle_model_test",
            "content-type": "application/json;charset=utf-8"
        }

        # 使用aiohttp发送第一个请求获取 question_id
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=20)) as session:
            async with session.post(f'{HUOSHAN_intent_URL}?{querystr}', headers=headers,
                                    data=body.encode("utf-8")) as response:
                if response.status != 200:
                    et_log.error(f"获取question_id失败，状态码: {response.status}")
                    return None

                try:
                    search_results = await response.text()
                    data = json.loads(search_results)
                    question_id = data["data"]["question_id"]
                    et_log.info(f"Obtained question_id: {question_id}")
                    return question_id
                except (json.JSONDecodeError, KeyError) as e:
                    et_log.error(f"解析question_id响应失败: {str(e)}")
                    return None

    @classmethod
    async def get_doubao_url(cls, prompt: str, question_id: str):
        method = "POST"
        timestamp = int(time.time())
        random_integer = random.randint(0, 65535)
        querystr = f"_timestamp={timestamp}&_nonce={random_integer}&channel={HUOSHAN_CHANNNEl}&app_id={HUOSHAN_APPID}&vehicle_id={HUOSHAN_VEHICLE_ID}"
        params = {
            "query": prompt,
            "chat_id": "123456789",
            "intent_type": "draw",
            "question_id": question_id,
            "history": [{"q": "你好！", "a": "嗯"}],
        }
        body = json.dumps(params, ensure_ascii=False)
        et_log.info(f"text to image request body: {body}")
        sign = cls.gen_sign(method, querystr, HUOSHAN_SECRET_KEY, body)
        headers = {
            "X-Signature": f'{HUOSHAN_API_KEY}:{sign}',
            "X-Use-PPE": "1",
            "X-Tt-Env": "ppe_vehicle_model_test",
            "content-type": "application/json;charset=utf-8"
        }
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=20)) as session:
                async with session.post(f'{HUOSHAN_PLAGIN_URL}?{querystr}', headers=headers,
                                        data=body.encode("utf-8")) as response:
                    if response.status != 200:
                        et_log.error(f"豆包图片接口请求失败，状态码{response.status}")
                        return None
                    try:
                        search_results = await response.text()
                        response_lines = search_results.split('\n')
                        for line in response_lines:
                            print(line)
                            if line.startswith('data:'):
                                event_data = json.loads(line[5:])
                                if 'observation' in event_data:
                                    observation = json.loads(event_data['observation'])
                                    if observation.get('response_type') == 'image':
                                        image_data = observation.get('image', {})
                                        urls = image_data.get('url', [])
                                        if urls and isinstance(urls, list) and len(urls) > 0:
                                            doubaoimage = urls[0]
                                            if '.png' in str(doubaoimage):
                                                et_log.info(f"找到PNG图片URL: {doubaoimage}")
                                                break
                        return doubaoimage
                    except Exception as e:
                        et_log.info(f"图片信息获取失败{e}")
        except Exception as e:
            et_log.info(f"豆包异步接口请求失败{e}")


# async def main():
#     # 测试参数
#     test_models = ['doubao_image', 'ernie_irag', 'st']
#     # test_models = ['doubao_image']
#     test_prompt = "画一副冬天雪景的图片"
#
#     # 运行主函数
#     results = await GenImageTest.main(test_models, test_prompt)
#     if results:
#         et_log.info(f"最终生成评分结果:{results}")
#     else:
#         et_log.info(f"评分失败")


if __name__ == "__main__":
    # test_models = ['ernie_irag', 'st']
    test_models = ['ernie_irag','st','FLUX.1-schnell','Stable-Diffusion-XL']
    test_prompt = "画一副丰盛晚餐的图片"
    results = asyncio.run(GenImageTest.main(test_models, test_prompt))
    if results:
        et_log.info(f"最终生成评分结果:{json.dumps(results, ensure_ascii=False, indent=3)}")
    else:
        et_log.info(f"评分失败")

# python -m com.exturing.ai.test.tool.genimage_test
