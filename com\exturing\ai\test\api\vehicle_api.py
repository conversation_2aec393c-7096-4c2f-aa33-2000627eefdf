# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.vehicle import VehicleModel, VehicleQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.vehicle_service import create, query_page, update, delete

vehicle = Blueprint('vehicle', __name__)

# 新增Vehicle
@vehicle.route(f'/{URL_PREFIX}/vehicle/create', methods=['POST'])
def vehicle_create():
    et_log.info("############vehicle_create################")
    req_data = request.get_json()
    vehicle_instance = VehicleModel(**req_data)
    vehicle_id = create(vehicle_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(vehicle_id)).to_json()

# Vehicle分页查询
@vehicle.route(f'/{URL_PREFIX}/vehicle/page', methods=['POST'])
def vehicle_page():
    et_log.info("############vehicle_page################")
    data = request.get_json()
    query = VehicleQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改Vehicle
@vehicle.route(f'/{URL_PREFIX}/vehicle/update', methods=['POST'])
def vehicle_update():
    et_log.info("############vehicle_update################")
    req_data = request.get_json()
    id = req_data.get("vehicle_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "vehicle_id is null", "").to_json()

    vehicle_instance = VehicleModel(**req_data)
    update_result = update(id, vehicle_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除Vehicle
@vehicle.route(f'/{URL_PREFIX}/vehicle/del', methods=['POST'])
def vehicle_delete():
    et_log.info("############vehicle_delete################")
    req_data = request.get_json()
    id = req_data.get("vehicle_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "vehicle_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()