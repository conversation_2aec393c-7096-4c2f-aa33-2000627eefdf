import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.test_project import TestProject
from com.exturing.ai.test.service.test_project_service import TestProjectService

test_project=Blueprint('test_project',__name__)


# 新增
@test_project.route(f"/{URL_PREFIX}/project/create", methods=["POST"])
def test_project_create():
    et_log.info("############project_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"project_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "project_create error, param is null", "").to_json()
        if "project_name" not in data or len(str(data["project_name"]).strip()) == 0:
            et_log.error(f"project_create error, project_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "project_name in param is null ", "").to_json()
        if "project_code" not in data or len(str(data["project_code"]).strip()) == 0:
            et_log.error(f"project_create error, project_code in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "project_code in param is null ", "").to_json()

        data["pid"] = str(data.get("project_pid", "")).strip()
        data["project_name"] = str(data.get("project_name", "")).strip()
        data["project_code"] = str(data.get("project_code", "")).strip()
        data["oem_code"] = str(data.get("oem_code", "")).strip()
        data["oem_name"] = str(data.get("oem_name", "")).strip()
        data["vehicle_model"] = str(data.get("vehicle_model", "")).strip()
        data["project_desc"] = str(data.get("project_desc", "")).strip()
        data["is_last"] = 1
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        insert_result = TestProjectService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert test_project error", "").to_json()
    except Exception as e:
        et_log.error(f"project_create create dic_info exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert test_project exception", "").to_json()


# 查询 分页
@test_project.route(f"/{URL_PREFIX}/project/query", methods=["POST"])
def test_project_query():
    et_log.info("############test_project_query################")
    data = request.get_json()
    data["project_name"] = str(data.get("project_name", "")).strip()
    data["project_code"] = str(data.get("project_code", "")).strip()
    data["oem_code"] = str(data.get("oem_code", "")).strip()
    data["vehicle_model"] = str(data.get("vehicle_model", "")).strip()
    if not data or "create_from_time" not in data or len(str(data["create_from_time"]).strip()) == 0:
        data["create_from_time"] = ""
    if not data or "create_to_time" not in data or len(str(data["create_to_time"]).strip()) == 0:
        data["create_to_time"] = ""
    if not data or "page_num" not in data or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] < 1:
        data["page_size"] = 10
    result = TestProjectService.query_page(data["project_name"], data["project_code"], data["oem_code"], data["vehicle_model"],
                                   data["create_from_time"], data["create_to_time"],
                                   data["page_num"], data["page_size"])
    if result and result.page_data and len(result.page_data) > 0:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result.to_json()).to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, "query test_project not found data", {}).to_json()


# 删除
@test_project.route(f"/{URL_PREFIX}/project/del", methods=["POST"])
def test_project_del():
    et_log.info("############test_project_del################")
    data = request.get_json()
    if not data or "project_id" not in data or data["project_id"] is None:
        et_log.error(f"test_project_del delete error, project_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestProjectService.delete(data["project_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "delete test_project error", "").to_json()


# 更新
@test_project.route(f"/{URL_PREFIX}/project/update", methods=["POST"])
def test_project_update():
    et_log.info("############test_project_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_project_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "project_id" not in data or data["project_id"] is None:
            et_log.error(f"test_project_update error, project_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "project_id in param is null", "").to_json()
        project_pk: TestProject = TestProjectService.find_pk(data["project_id"])
        if project_pk is None:
            et_log.error(f"test_project_update error, dic_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "dic_id is invalid", "").to_json()


        project_pk.pid = str(data.get("project_pid", "")).strip()
        project_pk.project_name = str(data.get("project_name", "")).strip()
        project_pk.project_code = str(data.get("project_code", "")).strip()
        project_pk.oem_code = str(data.get("oem_code", "")).strip()
        project_pk.oem_name = str(data.get("oem_name", "")).strip()
        project_pk.vehicle_model = str(data.get("vehicle_model", "")).strip()
        project_pk.project_desc = str(data.get("project_desc", "")).strip()
        project_pk.update_by = data.get("do_user", 0)
        update_result = TestProjectService.update(project_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update test_project error","").to_json()
    except Exception as e:
        et_log.error(f"test_dic_update update test_env exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update test_project exception","").to_json()
