# service.py
from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID
from com.exturing.ai.test.model.test_config import (
    TestConfigInfoModel,
    TestConfigModel,
    TestConfigQueryModel, TestConfigInfoReModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.service.test_tool_service import find_pk as find_tool_pk
from com.exturing.ai.test.service.test_model_service import find_pk as find_model_pk
from com.exturing.ai.test.service.test_env_service import TestEnvService


_doc = "test_config"


# 新增评测通道
def create(data: TestConfigModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create test_config: {data_dict}")

    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询评测通道
def query_page(
    page_num: int = 1, page_size: int = 10, query: TestConfigQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page.page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 修改评测通道
def update(id: str, data: TestConfigModel) -> bool:
    data_dict = data.model_dump()
    data_dict["_id"] = id

    et_log.info(f"update test_config: {data_dict}")

    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除评测通道
def delete(id: str) -> bool:
    et_log.info(f"delete test_config by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, CTX_USER_ID.get()) > 0


# 根据id查询评测通道
def find_pk(id: str):
    et_log.info(f"find test_config by id:{id}")
    res = MongoDBUtil.find_by_id(_doc, id)
    return MongoDBUtil.serialize_document(res) if res else None


# 根据config_id查询评测通道及其关联的其他评测环境信息
def find_config_info(id: str):
    et_log.info(f"find test_config_info by id:{id}")

    config_dict = find_pk(id)

    if not config_dict:
        return None

    tool_dict = find_tool_pk(config_dict.get("tool_id")) or {}
    model_dict = find_model_pk(config_dict.get("model_id")) or {}
    env = TestEnvService.find_pk(config_dict.get("env_id"))
    env_dict = env.to_json() if env else {}

    config_info = TestConfigInfoModel(
        **{
            "env_uri": env_dict.get("env_uri") or model_dict.get("url"),
            "branch_name": env_dict.get("branch_name"),
            "oem_code": env_dict.get("oem_code"),
            "pid_val": env_dict.get("pid_val"),
            "device_id": env_dict.get("device_id"),
            "user_id": env_dict.get("user_id"),
            "version": env_dict.get("version"),
            "adapter_code": tool_dict.get("adapter_code") or env_dict.get("adapter_code"),
            "env_script": env_dict.get("env_script") or tool_dict.get("script"),
            "model_url": model_dict.get("url"),
            "model_bot": model_dict.get("bot_model"),
            "model_ak": model_dict.get("ak"),
            "model_sk": model_dict.get("sk"),
            "model_params":model_dict.get("model_params"),
        }
    )

    return config_info

def find_config_info_re(config_id: str):
    """
    模型对比使用，原find_config_info接口增加了通道名称返回
    :param config_id: 通道id
    :return:
    """
    et_log.info(f"find find_config_info_re by config_id:{config_id}")

    config_dict = find_pk(config_id)

    if not config_dict:
        return None

    tool_dict = find_tool_pk(config_dict.get("tool_id")) or {}
    model_dict = find_model_pk(config_dict.get("model_id")) or {}
    env = TestEnvService.find_pk(config_dict.get("env_id"))
    env_dict = env.to_json() if env else {}

    config_info = TestConfigInfoReModel(
        **{
            "config_name": config_dict.get("name"),
            "env_uri": env_dict.get("env_uri") or model_dict.get("url"),
            "branch_name": env_dict.get("branch_name"),
            "oem_code": env_dict.get("oem_code"),
            "pid_val": env_dict.get("pid_val"),
            "device_id": env_dict.get("device_id"),
            "user_id": env_dict.get("user_id"),
            "version": env_dict.get("version"),
            "adapter_code": tool_dict.get("adapter_code") or env_dict.get("adapter_code"),
            "env_script": env_dict.get("env_script") or tool_dict.get("script"),
            "model_url": model_dict.get("url"),
            "model_bot": model_dict.get("bot_model"),
            "model_ak": model_dict.get("ak"),
            "model_sk": model_dict.get("sk"),
            "model_params": model_dict.get("model_params"),
        }
    )

    return config_info