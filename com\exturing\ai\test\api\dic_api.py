import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.dic_info import DicInfo
from com.exturing.ai.test.service.dic_service import DicService

dic_info=Blueprint('dic_info',__name__)

# 一级字典分类查询（需传递分组代码group_code）
@dic_info.route(f'/{URL_PREFIX}/dic/query', methods=['POST'])   # 一级字典分类查询
def dic_query():
    et_log.info("############dic_query################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"dic_query error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "parent_query error, param is null", "").to_json()
        # if "group_code" not in data or len(str(data["group_code"]).strip()) == 0:
        #     et_log.error(f"dic_query error, group_code in param is null")
        #     return ApiResult(ResultCode.PARAM_IS_BLANK.code, "group_code in param is null ", "").to_json()
        if "parent_id" not in data or len(str(data["parent_id"]).strip()) == 0:
            data["parent_id"] = ""
        if "dic_name" not in data or len(str(data["dic_name"]).strip()) == 0:
            data["dic_name"] = ""
        dic_json_list = DicService.query_list(data["parent_id"], data["dic_name"], data["group_code"])
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, dic_json_list).to_json()
    except Exception as e:
        et_log.error(f"dic_query query exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "dic query exception", "").to_json()

# 根据字典id，获取字典数据记录
@dic_info.route(f'/{URL_PREFIX}/dic/pk', methods=['POST'])   # 一级字典分类查询
def dic_query_pk():
    et_log.info("############dic_query_pk################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"dic_query_pk error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "parent_query error, param is null", "").to_json()
        if "dic_id" not in data or len(str(data["dic_id"]).strip()) == 0:
            et_log.error(f"dic_query_pk error, dic_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "dic_id is null ", "").to_json()
        dic = DicService.find_pk(data["dic_id"])
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, dic.to_json_str()).to_json()
    except Exception as e:
        et_log.error(f"dic_query_pk query exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "dic query by pk exception", "").to_json()

# 新增
@dic_info.route(f'/{URL_PREFIX}/dic/create', methods=['POST'])
def dic_info_create():
    et_log.info("############dic_info_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"dic_info_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "dic_info_create error, param is null", "").to_json()
        if "dic_name" not in data or len(str(data["dic_name"]).strip()) == 0:
            et_log.error(f"dic_info_create error, dic_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "dic_name in param is null ", "").to_json()
        # if "dic_code" not in data or len(str(data["dic_code"]).strip()) == 0:
        #     et_log.error(f"dic_info_create error, dic_code in param is null")
        #     return ApiResult(ResultCode.PARAM_IS_BLANK.code, "dic_code in param is null ", "").to_json()
        if "group_code" not in data or len(str(data["group_code"]).strip()) == 0:
            et_log.error(f"dic_info_create error, group_code in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "group_code in param is null ", "").to_json()
        data["dic_name"] = str(data["dic_name"]).strip()
        data["dic_code"] = data.get("dic_code", "")
        # data["dic_code"] = str(data["dic_code"]).strip()
        data["group_code"] = str(data["group_code"]).strip()
        data["group_name"] = str(data["group_name"]).strip()
        data["dic_desc"] = data.get("dic_desc", "")
        data["order_by"] = data.get("order_by", 0)
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        insert_result = DicService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert dic_info error", "").to_json()
    except Exception as e:
        et_log.error(f"dic_info_create create dic_info exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "insert dic_info exception", "").to_json()

# 查询 分页
@dic_info.route(f'/{URL_PREFIX}/dic/search', methods=['POST'])
def test_dic_search():
    et_log.info("############test_dic_search################")
    data = request.get_json()
    if not data or "pid" not in data or data["pid"] is None or len(str(data["pid"]).strip()) == 0:
        data["pid"] = ""
    if not data or "dic_name" not in data or data["dic_name"] is None or len(str(data["dic_name"]).strip()) == 0:
        data["dic_name"] = ""
    if not data or "dic_code" not in data or data["dic_code"] is None or len(str(data["dic_code"]).strip()) == 0:
        data["dic_code"] = ""
    if not data or "group_code" not in data or data["group_code"] is None or len(str(data["group_code"]).strip()) == 0:
        data["group_code"] = ""
    if not data or "create_from_time" not in data or len(str(data["create_from_time"]).strip()) == 0:
        data["create_from_time"] = ""
    if not data or "create_to_time" not in data or len(str(data["create_to_time"]).strip()) == 0:
        data["create_to_time"] = ""
    if not data or "page_num" not in data or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] < 1:
        data["page_size"] = 10
    result = DicService.query_page(data["pid"], data["dic_name"], data["dic_code"], data["group_code"],
                                   data["create_from_time"], data["create_to_time"],
                                   data["page_num"], data["page_size"])
    if result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result.to_json()).to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, {}).to_json()

# 删除
@dic_info.route(f'/{URL_PREFIX}/dic/del', methods=['POST'])
def test_dic_del():
    et_log.info("############test_dic_del################")
    data = request.get_json()
    if not data or "dic_id" not in data or data["dic_id"] is None:
        et_log.error(f"test_dic_del delete error, dic_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = DicService.delete(data["dic_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "delete dic_info error", "").to_json()

# 更新
@dic_info.route(f'/{URL_PREFIX}/dic/update', methods=['POST'])
def test_dic_update():
    et_log.info("############test_dic_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_dic_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or "dic_id" not in data or data["dic_id"] is None:
            et_log.error(f"test_dic_update error, dic_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "dic_id in param is null", "").to_json()
        dic_pk: DicInfo = DicService.find_pk(data["dic_id"])
        if dic_pk is None:
            et_log.error(f"test_dic_update error, dic_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "dic_id is invalid", "").to_json()

        dic_pk.name = data.get("dic_name", "")
        dic_pk.code = data.get("dic_code", "")
        dic_pk.desc = data.get("dic_desc", "")
        dic_pk.order_by = data.get("order_by", 0)
        dic_pk.update_by = data.get("do_user", 0)
        dic_pk.group_code = data.get("group_code", "")
        dic_pk.group_name = data.get("group_name", "")
        update_result = DicService.update(dic_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update dic_info error","").to_json()
    except Exception as e:
        et_log.error(f"test_dic_update update test_env exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, "update dic_info exception","").to_json()

# 字典分组查询
@dic_info.route(f'/{URL_PREFIX}/dic/query-group', methods=['POST'])
def dic_query_group():
    et_log.info("############dic_query_group################")
    data = request.get_json()
    try:
        if "group_code" not in data or len(str(data["group_code"]).strip()) == 0:
            data["group_code"] = ""
        if "group_name" not in data or len(str(data["group_name"]).strip()) == 0:
            data["group_name"] = ""
        dic_json_list = DicService.query_group_list(data["group_code"], data["group_name"])
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, dic_json_list).to_json()
    except Exception as e:
        et_log.error(f"dic_query_group query exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "dic group query exception", "").to_json()


