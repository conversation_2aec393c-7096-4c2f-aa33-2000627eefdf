# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.test_metric import TestMetricModel, TestMetricQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_metric_service import create, query_page, update, delete

test_metric = Blueprint('test_metric', __name__)

# 新增指标
@test_metric.route(f'/{URL_PREFIX}/test-metric/create', methods=['POST'])
def test_metric_create():
    et_log.info("############test_metric_create################")
    req_data = request.get_json()
    test_metric_instance = TestMetricModel(**req_data)
    test_metric_id = create(test_metric_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(test_metric_id)).to_json()

# 指标分页查询
@test_metric.route(f'/{URL_PREFIX}/test-metric/page', methods=['POST'])
def test_metric_page():
    et_log.info("############test_metric_page################")
    data = request.get_json()
    query = TestMetricQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改指标
@test_metric.route(f'/{URL_PREFIX}/test-metric/update', methods=['POST'])
def test_metric_update():
    et_log.info("############test_metric_update################")
    req_data = request.get_json()
    id = req_data.get("test_metric_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_metric_id is null", "").to_json()

    test_metric_instance = TestMetricModel(**req_data)
    update_result = update(id, test_metric_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除指标
@test_metric.route(f'/{URL_PREFIX}/test-metric/del', methods=['POST'])
def test_metric_delete():
    et_log.info("############test_metric_delete################")
    req_data = request.get_json()
    id = req_data.get("test_metric_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_metric_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()