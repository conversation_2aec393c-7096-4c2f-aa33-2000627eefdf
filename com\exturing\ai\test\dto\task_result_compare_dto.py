from typing import Optional

from bson import ObjectId
from pydantic import BaseModel


class TaskResultCompareDto(BaseModel):
    """
    任务结果比对DTO
    """
    set_item_id: Optional[str] = ""     # 评测集id
    data_set_id: Optional[str] = ""     # 语料id
    question: Optional[str] = ""        # 语料
    expected_answer: Optional[str] = "" # 期望回答
    expected_category: Optional[str] = ""   # 期望落域
    result_new5:Optional[list]  # 近5次的回答及评估情况，ItemResultCompareDto的5次集合

class ItemResultCompareDto(BaseModel):
    """
    任务结果明细DTO
    """
    result_id: Optional[str] = ""       # 评测结果id
    result_datetime: Optional[str] = "" # 评测结果时间
    conf_id: Optional[str] = ""         # 评测通道id
    conf_name: Optional[str] = ""       # 评测通道名称
    actual_answer: Optional[str] = ""   # 实际回答
    actual_category: Optional[str] = "" # 实际落域
    first_res_time: Optional[float] = 0   # 首token耗时
    qa_use_time: Optional[float] = 0    # 完整耗时
    answer_score: Optional[int] = 0     # 回答得分
    result_answer: Optional[int] = 0    # 回答判定
    result_category: Optional[int] = 0  # 落域判定
    result_final: Optional[int] = 0     # 最终判定
