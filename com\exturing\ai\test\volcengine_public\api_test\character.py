from com.exturing.ai.test.volcengine_public.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_chat_id
from com.exturing.ai.test.volcengine_public.volcengine_client import VolcengineClient

api_path = "/dpfm/v1/chat/character/stream"


def character_api():
    """
    角色扮演聊天接口测试
    """
    # 测试数据
    test_data = {
        "query": "周杰伦有哪些歌？",
    }

    with VolcengineClient() as client:
        response = client.stream_post(
            path=api_path,
            data=test_data,
        )

        answer = CurriedJoin()

        for chunk in response:
            print("Chunk:", chunk)
            if chunk:
                if chunk.get("content"):
                    answer(chunk["content"])

        print("Response:", answer.result())


if __name__ == "__main__":
    pass
    # character_id不存在 无法使用

    # test_data = {
    #     "query": "你叫什么名字？",
    #     "chat_id": get_chat_id(),
    #     "character_id": 61,  # 童年玩伴
    #     "character_info": {"dress": "白色T恤", "localtion": "学校里"},
    #     "history": [{"q": "你好！", "a": "嗯"}],
    # }
    # character_api()

# python -m com.exturing.ai.test.volcengine_public.api_test.character
