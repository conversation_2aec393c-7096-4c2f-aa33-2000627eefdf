from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 测试环境分支
class TestEnvBranch(BaseModel):

    _doc = "test_env_branch"
    branch_name: str  # 分支名称
    branch_desc: str  # 分支描述
    param_pid: str  # pid参数
    device_id: str  # deviceId参数
    version: str  # ..参数
    user_id: str  # ..userId参数

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", branch_name="", branch_desc="",
                 param_pid="", device_id="", version="", user_id=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.branch_name = branch_name
        self.branch_desc = branch_desc
        self.param_pid = param_pid
        self.device_id = device_id
        self.version = version
        self.user_id = user_id
        self.oem_codes = oem_codes

    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = self._id
        base_json["branch_name"] = self.branch_name
        base_json["branch_desc"] = self.branch_desc
        base_json["param_pid"] = self.param_pid
        base_json["device_id"] = self.device_id
        base_json["version"] = self.version
        base_json["user_id"] = self.user_id
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        return data_json

    def insert_one(self):
        return MongoDBUtil.insert_one(self._doc, self.to_json())

    # 删除数据
    @classmethod
    def delete_by_id(cls, _id, do_user):
        return MongoDBUtil.delete_by_id(cls._doc, _id, do_user)

    # 更新数据
    @classmethod
    def update_entity_json(cls, entity_json):
        return MongoDBUtil.update_one_pro(cls._doc, entity_json)

    # 根据主键_id查询数据
    @classmethod
    def find_by_pk(cls, _id):
        return MongoDBUtil.find_by_id(cls._doc, _id)

    # 根据条件查询数据匹配记录数
    @classmethod
    def find_condition_count(cls, condition):
        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据条件查询数据的集合
    @classmethod
    def find_condition(cls, condition, sorts, limit, skip):
        result = MongoDBUtil.find_condition_page(cls._doc, condition, sorts, skip, limit)
        if result is not None:
            items = []
            for item in result:
                data = TestEnvBranch(**item)
                items.append(data)
            return items
        else:
            return None

# current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
# TestEnvBranch("", current_time, 0, current_time, 0, 0, "lingyu_3.0_test",
#         "lingyu_3.0_test", "279623278", "dds_excel_test_v2", "1.1", "1000000000").insert_one()
# TestEnvBranch("", current_time, 0, current_time, 0, 0, "LLM_TEST",
#         "LLM_TEST", "279623278", "dds_excel_test_v2", "1.1", "1000000000").insert_one()
# TestEnvBranch("", current_time, 0, current_time, 0, 0, "llm_test01",
#         "llm_test01", "279623278", "dds_excel_test_v2", "1.1", "1000000000").insert_one()
# TestEnvBranch("", current_time, 0, current_time, 0, 0, "LLM_prod2.0_TEST",
#         "LLM_prod2.0_TEST", "279623278", "dds_excel_test_v2", "1.1", "1000000000").insert_one()
