from bson import ObjectId
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil


class BaseModel:
    _doc = ""
    _id: str
    oem_codes: str
    create_time: str
    create_by: int
    update_time: str
    update_by: int
    is_del: int

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes=""):
        self._id = _id
        self.create_time = create_time
        self.create_by = create_by
        self.update_time = update_time
        self.update_by = update_by
        self.is_del = is_del
        self.oem_codes = oem_codes

    def to_json(self):
        return {
            "_id": str(self._id),
            "create_time": self.create_time,
            "create_by": self.create_by,
            "update_time": self.update_time,
            "update_by": self.update_by,
            "is_del": self.is_del,
            "oem_codes": self.oem_codes,
        }

    @property
    def id(self):
        return self._id

    def insert_one(self):
        return MongoDBUtil.insert_one(self._doc, self.to_json())

    # 删除数据
    @classmethod
    def delete_by_id(cls, _id, do_user):
        return MongoDBUtil.delete_by_id(cls._doc, _id, do_user)

    # 根据数据项父id，删除其下的所有数据记录项
    @classmethod
    def del_by_pid(cls, pid, do_user):
        condition = {"pid": ObjectId(str(pid))}
        return MongoDBUtil.delete_by_many(cls._doc, condition, do_user)

    # 更新数据
    @classmethod
    def update_entity_json(cls, entity_json):
        return MongoDBUtil.update_one_pro(cls._doc, entity_json)

    # 根据主键_id查询数据
    @classmethod
    def find_by_pk(cls, _id):
        return MongoDBUtil.find_by_id(cls._doc, _id)

    @classmethod
    def find_by_pid(cls, pid):
        condition = {"is_del": 0, "pid": ObjectId(str(pid))}
        count = cls.find_condition_count(condition)
        data_list = cls.find_condition(condition, None, count, 0)
        return data_list

    # 根据条件查询数据匹配记录数
    @classmethod
    def find_condition_count(cls, condition):
        return MongoDBUtil.find_count(cls._doc, condition)

    # 根据条件查询数据的集合
    @classmethod
    def find_condition(cls, condition, sorts, limit, skip):
        result = MongoDBUtil.find_condition_page(cls._doc, condition, sorts, skip, limit)
        if result is not None:
            items = []
            for item in result:
                # 使用 __new__ 方法创建实例
                data = cls.__new__(cls)
                # 手动调用 __init__ 方法进行初始化
                if hasattr(cls, '__init__'):
                    cls.__init__(data, **item)
                    items.append(data)
            return items
        else:
            return None

    @classmethod
    def aggregate(cls, pipeline):
        return MongoDBUtil.aggregate(cls._doc, pipeline)