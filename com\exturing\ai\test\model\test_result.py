from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel
from com.exturing.ai.test.comm.log_tool import et_log

# 测试结果
class TestResult(BaseModel):
    _doc = "test_result"
    parent_id: str # 上一轮id
    task_id: str # 测试任务id
    data_set_item_id: str # 数据集数据项id
    question: str  # 测试的语料（问题）
    actual_task: str # 实际任务类型
    actual_category: str # 实际分类（落域）
    result_category: int  # 落域结果 判定
    actual_answer: str  # 实际回答
    result_answer: int # 回答结果 判定
    answer_score: int # 回答得分
    qa_recall: float # 问题召回率
    first_res_time : int  #首帧时间
    qa_use_time: int # 耗时 单位:秒
    result_final: int # 最终结果 判定
    ad_result_category: int # 校准 落域结果
    ad_result_answer: int # 校准 回答结果 判定
    ad_result_final: int # 校准 最终结果
    is_last: int # 是否终轮

    expected_task: str  # 期望任务类型
    expected_category: str  # 期望分类（落域）
    expected_answer: str # 期望回答（标准答案）
    recall_id: str # agent回答返回id
    task_result_id: str # 测试任务结果id
    model_id: str # 模块id
    dimension_id: str # 维度id
    remark: str # 备注

    re_url: str # 回复的url
    re_cmd: str # 回复的指令cmd
    re_error_id: str # 回复的errorId
    re_interval_time: float # 资讯的时间与回复时间的间隔（单位：天）
    re_url_q: str # Q前缀的url
    re_cmd_q: str # Q前缀的cmd
    re_error_id_q: str # Q前缀的errorId
    oem: str
    is_websearch: int # 是否走websearch 1=走 0=不走
    result_task: int # 实际任务结果判定
    ad_result_task: int # 校准 任务结果判定
    data_tags: str # 数据标签

    metric_calib:[] # 指标标定集合
    eval_config_id: str # 评估通道id

    answer_score_reason:str # 评分原因

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", parent_id="", task_id="", data_set_item_id="",
                 question="", actual_task="", actual_category="", result_category=0, actual_answer="", result_answer=0,
                 answer_score=0, qa_recall=0,first_res_time=0,
                 # qa_keywords,
                 qa_use_time=0, result_final=0, ad_result_category=0, ad_result_answer=0,
                 ad_result_final=0, is_last=1, expected_task="", expected_category="", expected_answer="", recall_id="",
                 task_result_id="", model_id="", dimension_id="", remark="", re_url="", re_cmd="", re_error_id="",
                 re_interval_time=0, re_url_q="", re_cmd_q="", re_error_id_q="", is_websearch=0, result_task=0,
                 ad_result_task=0,oem="",data_tags="", metric_calib=None, eval_config_id="", answer_score_reason=""):

        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.parent_id = parent_id
        self.task_id = task_id
        self.data_set_item_id = data_set_item_id
        self.question = question
        self.actual_task = actual_task
        self.actual_category = actual_category
        self.result_category = result_category
        self.actual_answer = actual_answer
        self.result_answer = result_answer
        self.answer_score = answer_score
        self.qa_recall = qa_recall
        # self.qa_keywords = qa_keywords
        self.first_res_time = first_res_time
        self.qa_use_time = qa_use_time
        self.result_final = result_final
        self.ad_result_category = ad_result_category
        self.ad_result_answer = ad_result_answer
        self.ad_result_final = ad_result_final
        self.is_last = is_last
        self.expected_category = expected_category
        self.expected_task = expected_task
        self.expected_answer = expected_answer
        self.recall_id = recall_id
        self.task_result_id = task_result_id
        self.model_id = model_id
        self.dimension_id = dimension_id
        self.remark = remark

        self.re_url = re_url
        self.re_cmd = re_cmd
        self.re_error_id = re_error_id
        self.re_interval_time = re_interval_time
        self.re_url_q = re_url_q
        self.re_cmd_q = re_cmd_q
        self.re_error_id_q = re_error_id_q
        self.oem = oem
        self.is_websearch = is_websearch

        self.result_task = result_task
        self.ad_result_task = ad_result_task
        self.data_tags = data_tags

        self.oem_codes = oem_codes
        self.metric_calib = metric_calib

        self.eval_config_id = eval_config_id

        self.answer_score_reason = answer_score_reason

    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = ObjectId(self._id) if self._id and len(str(self._id)) > 0 else ""
        base_json["parent_id"] = ObjectId(self.parent_id) if self.parent_id and len(str(self.parent_id)) > 0 else ""
        base_json["task_id"] = ObjectId(self.task_id) if self.task_id and len(str(self.task_id)) > 0 else ""
        base_json["data_set_item_id"] = ObjectId(self.data_set_item_id) if self.data_set_item_id and len(str(self.data_set_item_id)) > 0 else ""
        base_json["question"] = self.question
        base_json["actual_task"] = self.actual_task
        base_json["actual_category"] = self.actual_category
        base_json["result_category"] = self.result_category
        base_json["actual_answer"] = self.actual_answer
        base_json["result_answer"] = self.result_answer
        base_json["answer_score"] = self.answer_score
        base_json["qa_recall"] = self.qa_recall
        base_json["first_res_time"] = self.first_res_time
        # base_json["qa_keywords"] = self.qa_keywords
        base_json["qa_use_time"] = self.qa_use_time
        base_json["result_final"] = self.result_final
        base_json["ad_result_category"] = self.ad_result_category
        base_json["ad_result_answer"] = self.ad_result_answer
        base_json["ad_result_task"] = self.ad_result_task
        base_json["ad_result_final"] = self.ad_result_final
        base_json["is_last"] = self.is_last

        base_json["expected_category"] = self.expected_category
        base_json["expected_task"] = self.expected_task
        base_json["expected_answer"] = self.expected_answer
        base_json["recall_id"] = self.recall_id
        base_json["task_result_id"] = ObjectId(self.task_result_id) if self.task_result_id and len(str(self.task_result_id)) > 0 else ""
        base_json["model_id"] = ObjectId(self.model_id) if self.model_id and len(str(self.model_id)) > 0 else ""
        base_json["dimension_id"] = ObjectId(self.dimension_id) if self.dimension_id and len(str(self.dimension_id)) > 0 else ""
        base_json["remark"] = self.remark

        base_json["re_url"] = self.re_url
        base_json["re_cmd"] = self.re_cmd
        base_json["re_error_id"] = self.re_error_id
        base_json["re_interval_time"] = self.re_interval_time
        base_json["re_url_q"] = self.re_url_q
        base_json["re_cmd_q"] = self.re_cmd_q
        base_json["re_error_id_q"] = self.re_error_id_q
        base_json["oem"] = self.oem

        base_json["is_websearch"] = self.is_websearch
        base_json["result_task"] = self.result_task
        base_json["data_tags"] = self.data_tags

        base_json["metric_calib"] = self.metric_calib
        base_json["eval_config_id"] = self.eval_config_id

        base_json["answer_score_reason"] = self.answer_score_reason

        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["parent_id"] = str(self.parent_id) if self.parent_id and len(str(self.parent_id)) > 0 else ""
        data_json["task_id"] = str(self.task_id) if self.task_id and len(str(self.task_id)) > 0 else ""
        data_json["data_set_item_id"] = str(self.data_set_item_id) if self.data_set_item_id and len(str(self.data_set_item_id)) > 0 else ""
        data_json["task_result_id"] = str(self.task_result_id) if self.task_result_id and len(str(self.task_result_id)) > 0 else ""
        data_json["model_id"] = str(self.model_id) if self.model_id and len(str(self.model_id)) > 0 else ""
        data_json["dimension_id"] = str(self.dimension_id) if self.dimension_id and len(str(self.dimension_id)) > 0 else ""
        data_json["eval_config_id"] = str(self.eval_config_id) if self.eval_config_id and len(str(self.eval_config_id)) > 0 else ""
        return data_json

    # 根据测试任务结果id，删除其下所有测试结果项
    @classmethod
    def del_by_result_id(cls, task_result_id, do_user):
        condition = {"task_result_id": ObjectId(task_result_id)}
        # del_pro = {"is_del": 1, "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        return MongoDBUtil.delete_by_many(cls._doc, condition, do_user)

    # 批量更新数据
    @classmethod
    def update_batch_json(cls, result_item_ids, entity_json):
        object_ids = [ObjectId(id) for id in result_item_ids]
        condition = {"_id": {"$in": object_ids}}
        # 过滤掉空值
        filter_json  = {k: v for k, v in entity_json.items() if v is not None}
        return MongoDBUtil.update_many(cls._doc, condition, filter_json)

    # 根据父id，查询其子节点个数
    @classmethod
    def find_count_by_pid(cls, pid):
        condition = {"is_del": 0}
        if not pid or len(str(pid)) == 0:
            et_log.error(f"find_by_pid error, the parent_id parameter is empty")
            return 0
        condition["parent_id"] = ObjectId(pid)
        return MongoDBUtil.find_count(cls._doc, condition)
    
    # 根据parent_id查询数据集数据项
    @classmethod
    def find_by_pid(cls, pid):
        condition = {"is_del": 0}
        if not pid or len(str(pid)) == 0:
            et_log.error(f"find_by_pid error, the parent_id parameter is empty")
            return None
        condition["parent_id"] = ObjectId(str(pid))
        count = cls.find_count_by_pid(pid)
        data_list = MongoDBUtil.find_condition_page(cls._doc, condition, None, 0, count)
        if data_list is not None:
            items = []
            for item in data_list:
                et_item = TestResult(**item)
                items.append(et_item)
            return items
        else:
            return None

    # 根据task_result_id查询数据
    @classmethod
    def find_by_task_result_id(cls, task_result_id):
        return MongoDBUtil.find_all_by_task_result_id(cls._doc, task_result_id)