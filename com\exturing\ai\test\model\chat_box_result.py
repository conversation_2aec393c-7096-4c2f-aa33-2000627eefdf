from typing import Optional

from com.exturing.ai.test.model.base_pydantic_model import BasePydanticModel


class ChatBoxResult(BasePydanticModel):
    """
    重构测试指标标定
    """
    _doc = "chat_box_result"

    chat_id: Optional[str] = ""     # 会话Id
    question: Optional[str] = "" # 问题
    model_id: Optional[str] = "" # 问题所属模块
    expected_category: Optional[str] = "" # 问题期望落域
    expected_answer: Optional[str] = "" # 问题期望回答
    config_id: Optional[str] = "" # 问题评估通道id

    actual_answer: Optional[str] = "" # 实际全部回答内容
    first_time: Optional[int] = 0   # 首次响应耗时
    use_time: Optional[int] = 0    # 全部回答耗时
    answer_score: Optional[int] = 0 # 回答评分
    answer_score_reason: Optional[str] = "" # 回答评分原因