from com.exturing.ai.test.model import employee
from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel

from com.exturing.ai.test.model.employee import EmployeeQueryModel
from com.exturing.ai.test.service.employee_service import query_name_list


class WorkTaskModel(BaseDataModel):
    requirement_id: Optional[str] = None  # 需求ID
    role: Optional[str] = None  # 角色
    module: Optional[str] = None  # 模块
    content: Optional[str] = None  # 任务
    person_day: Optional[float] = None  # 工时 人天


class WorkHourModel(BaseDataModel):
    employee_id: Optional[str] = None  # 员工ID
    project_id: Optional[str] = None  # 项目ID
    start_time: Optional[str] = None  # 开始时间
    end_time: Optional[str] = None  # 结束时间
    work_task: Optional[list[WorkTaskModel]] = None  # 工作任务


class WorkHourQueryModel(BaseModel):
    employee_id: Optional[str] = None
    project_id: Optional[str] = None
    start_time: Optional[str] = None  # 开始时间
    end_time: Optional[str] = None  # 结束时间
    department_using: Optional[str] = None  # 使用部门
    base: Optional[str] = None  # base地
    level: Optional[str] = None  # 职级
    skill: Optional[str] = None  # 技术栈

    def get_query_condition(self):
        condition = {}

        employee_query = None

        if self.employee_id:
            employee_id_list = self.employee_id.split(",")
            if len(employee_id_list) > 1:
                condition["employee_id"] = {"$in": employee_id_list}
            else:
                condition["employee_id"] = self.employee_id
        if self.project_id:
            condition["project_id"] = self.project_id
        if self.start_time:
            condition["start_time"] = {"$gte": self.start_time}
        if self.end_time:
            condition["end_time"] = {"$lte": self.end_time}
        if self.department_using:
            if not employee_query:
                employee_query = EmployeeQueryModel()
            employee_query.department_using = self.department_using.split(",")
        if self.base:
            if not employee_query:
                employee_query = EmployeeQueryModel()
            employee_query.base = self.base
        if self.level:
            if not employee_query:
                employee_query = EmployeeQueryModel()
            employee_query.level = self.level.split(",")
        if self.skill:
            if not employee_query:
                employee_query = EmployeeQueryModel()
            employee_query.skill = self.skill.split(",")

        if employee_query:
            employee_list = query_name_list(employee_query)
            employee_ids = [str(employee["_id"]) for employee in employee_list]

            if self.employee_id:
                employee_ids = list(
                    set(employee_ids) & set(self.employee_id.split(","))
                )

            condition["employee_id"] = {"$in": employee_ids}

        return condition
