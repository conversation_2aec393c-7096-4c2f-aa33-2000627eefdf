from datetime import datetime
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
import sys

# 落域数据
category_list = [
    ["D", "车辆控制指令", 2],
    ["O", "导航自由说", 1],
    ["Z", "拒识", 1],
    ["C", "壁纸生成", 1],
    ["G", "场景生成", 1],
    ["W", "体育", 1],
    ["M", "音乐", 1],
    ["E", "查询车辆说明书及用车百科", 1],
    ["Q", "美食自由搜", 1],
    ["K", "新闻", 1],
    ["T", "氛围灯设置", 2],
    ["H", "日程提醒", 2],
    ["I", "POI识别", 2],
    ["J", "车辆功能查询", 2],
    ["N", "车况查询", 2],
    ["L", "短视频", 1],
    ["U", "酒店自由搜", 1],
    ["B", "旅游规划", 1],
    ["Y", "敏感信息", 1],
    ["P", "视频自由搜", 1],
    ["e", "教育", 1],
    ["A", "闲聊及百科大问答", 1],
    ["F", "菜谱", 1],
    ["R", "指示灯", 2],
    ["S", "景点自由搜", 1],
]

# 模块维度数据
model_dimension_list = {
    "新闻助手": [
        "政治新闻",
        "文化新闻",
        "科技新闻",
        "军事新闻",
        "娱乐新闻",
        "财经新闻",
        "社会新闻",
        "体育新闻",
        "汽车新闻",
        "国外新闻",
    ],
    "景点助手": [
        "国内总览",
        "景区推荐",
        "亲子活动",
        "家庭出游",
        "休闲放松",
        "自然探索",
        "文化体验",
        "经济旅游",
        "夜间活动",
        "交通便捷",
        "特殊兴趣",
    ],
    "日程提醒": [
        "日程时间提醒",
        "日程地点提醒",
        "日程事务提醒",
    ],
    "绘画助手": [
        "人物肖像",
        "风景自然",
        "城市建筑",
        "动物植物",
        "抽象艺术",
        "科幻幻想",
        "历史场景",
        "文化艺术",
        "体育竞技",
        "交通工具",
        "数码艺术",
        "卡通漫画",
        "传统绘画",
        "现代艺术",
        "极简主义",
    ],
    "音乐自由说": [
        "榜单维度",
        "地域维度",
        "歌词维度",
        "歌手/歌名维度",
        "歌名维度",
        "情感主题维度",
        "社交热度维度",
        "时间维度",
        "使用场景维度",
        "音乐传承维度",
        "音乐创新维度",
        "音乐的节日氛围维度",
        "音乐风格维度",
        "音乐纪念维度",
        "音乐教育流派维度",
        "音乐节维度",
        "音乐空间维度",
        "音乐色彩维度(视觉化相关)",
        "音乐商业维度",
        "音乐社交互动维度",
        "音乐受众维度",
        "音乐与地理地标维度",
        "音乐与健康维度",
        "音乐与科技发明维度",
        "音乐与科技结合维度",
        "音乐与历史时期维度",
        "音乐与旅行目的地维度",
        "音乐与美食维度",
        "音乐与时尚秀场维度",
        "音乐与特殊群体维度",
        "音乐与天文现象维度",
        "音乐与文学作品维度",
        "音乐与舞蹈风格维度",
        "音乐与艺术跨界维度",
        "音乐与游戏维度",
        "音乐与宗教维度",
        "音乐元素维度",
        "音乐制作维",
        "音乐与自然元素维度",
    ],
    "百科助手": [
        "讲故事",
        "诗词",
        "作文",
        "人物百科",
        "生活常识",
        "世界之最",
        "十万个为什么",
        "笑话",
        "天文",
        "地理",
        "数学",
        "物理",
        "化学",
        "历史",
        "植物",
        "计算机",
        "医疗健康",
        "法律",
        "金融",
        "英语",
        "聊天",
        "职场",
        "菜谱",
    ],
    "氛围助手": [
        "颜色调整维度",
        "开关控制维度",
        "亮度调节维度",
        "模式选择维度",
        "场景生成",
    ],
    "用车助手": [
        "查询车辆说明书及用车百科",
        "车况查询",
        "车辆功能查询",
        "车辆控制指令",
    ],
    "行程助手": [
        "路线安排",
        "旅游攻略",
        "美食计划",
        "住宿规划",
        "国外行程",
    ],
    "美食助手": [
        "美食做法",
        "节日推荐",
        "特定场合",
        "口味菜系",
        "特定地点",
        "指定需求",
        "价格环境",
        "品牌餐饮",
    ],
    "影音助手": [
        "音乐自由说",
        "短视频",
        "视频自由说",
    ],
    "氛围助手": [
        "氛围灯",
    ],
    "场景生成": [
        "场景生成",
    ],
}

# 标签数据
tag_list = [
    "有效数据",
    "无效数据",
    "生产数据",
    "测试数据",
]


def del_data(oem: str):
    """删除数据"""

    db = MongoDBUtil().mdb
    db["oem"].delete_many({"oem_codes": oem})
    db["vehicle"].delete_many({"oem_codes": oem})
    db["category_info"].delete_many({"oem_codes": oem})
    db["dic_info"].delete_many({"oem_codes": oem})
    db["data_tag"].delete_many({"oem_codes": oem})


def init_data(oem: str, vehicle: str):
    """初始化数据"""

    db = MongoDBUtil().mdb

    common_dict = {
        "oem_codes": oem,
        "is_del": 0,
        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "update_time": "",
        "create_by": 0,
        "update_by": 0,
    }

    # 1. 初始化OEM数据
    oem_dict = {
        "oem_name": oem,
        "code": oem,
    } | common_dict

    db["oem"].insert_one(oem_dict)

    # 2. 初始化车型数据
    vehicle_dict = {
        "vehicle_name": vehicle,
        "pid": vehicle,
        "oem_code": oem,
    } | common_dict

    db["vehicle"].insert_one(vehicle_dict)

    # 3. 初始化落域数据
    category_dict_list = []
    for category in category_list:
        category_dict = {
            "category_name": category[1],
            "category_code": category[0],
            "category_type": category[2],
        } | common_dict

        category_dict_list.append(category_dict)

    db["category_info"].insert_many(category_dict_list)

    # 4. 初始化字典数据
    for model in model_dimension_list:
        model_dict = {
            "name": model,
            "group_code": "dm-model",
            "group_name": "模块",
        } | common_dict

        model_res = db["dic_info"].insert_one(model_dict)

        dimension_dict_list = []
        for dimension in model_dimension_list[model]:
            dimension_dict = {
                "pid": model_res.inserted_id,
                "name": dimension,
                "group_code": "dm-dimension",
                "group_name": "维度",
            } | common_dict
            dimension_dict_list.append(dimension_dict)

        db["dic_info"].insert_many(dimension_dict_list)

    # 5. 初始化标签数据
    tag_dict_list = []
    for tag in tag_list:
        tag_dict = {
            "tag_name": tag,
            "type": 1,
            "status": 1,
        } | common_dict

        tag_dict_list.append(tag_dict)

    db["data_tag"].insert_many(tag_dict_list)


def comfirm_run():
    """确认是否执行"""
    while True:
        comfirm = input("运行脚本会清空当前OEM数据，确认是否执行(y/n):").strip().lower()
        if comfirm in ("y", "yes"):
            return True
        else:
            print("操作已取消。")
            sys.exit()


if __name__ == "__main__":
    comfirm_run()

    # 新的OEM客户，需要初始化数据
    oem = "demo"  # oem代码
    vehicle = "demo"  # 车型pid

    # 1. 删除数据
    # del_data(oem)
    # 2. 初始化数据
    init_data(oem, vehicle)

# python -m com.exturing.ai.test.init.oem_data_init
