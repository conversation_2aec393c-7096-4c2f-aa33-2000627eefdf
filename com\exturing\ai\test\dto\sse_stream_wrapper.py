class SseStreamWrapper:
    """
    SSE协议 返回流封装对象
    """
    def __init__(self, generator):
        self.generator = generator
        self.return_value = None

    def __iter__(self):
        return self

    def __next__(self):
        try:
            return next(self.generator)
        except StopIteration as e:
            # 捕获生成器的 return 值
            self.return_value = e.value
            raise