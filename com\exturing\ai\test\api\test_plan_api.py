import threading
import traceback

from flask import Blueprint, request, Response

from com.exturing.ai.test.comm.api_result import <PERSON><PERSON><PERSON><PERSON><PERSON>
from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, CTX_USER_ID, URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.model.test_plan import TestPlan
from com.exturing.ai.test.service.data_set_service import DataSetService
from com.exturing.ai.test.service.test_plan_service import TestPlanService

test_plan=Blueprint('test_plan',__name__)

# 新增
@test_plan.route(f'/{URL_PREFIX}/test-plan/create', methods=['POST'])   # 数据集新增
def test_plan_create():
    et_log.info("############test_plan_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_plan_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_plan_create error, param is null", "").to_json()
        if "plan_name" not in data or len(str(data["plan_name"]).strip()) == 0:
            et_log.error(f"test_plan_create error, plan_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "plan_name in param is null ", "").to_json()
        data["plan_name"] = str(data["plan_name"]).strip()
        # check_result = TestPlanService.check_name(plan_name)
        # if check_result:# 有重复
        #     et_log.error(f"test_plan_create error, plan_name has used")
        #     return ApiResult(ResultCode.PARAM_IS_INVALID.code, "plan_name has used", "").to_json()

        data["project_id"] = str(data["project_id"]).strip() if "project_id" in data and len(str(data["project_id"])) > 0 else ""
        data["start_time"] = str(data["start_time"]).strip() if "start_time" in data and len(str(data["start_time"])) > 0 else ""
        data["end_time"] = str(data["end_time"]).strip() if "end_time" in data and len(str(data["end_time"])) > 0 else ""
        data["desc"] = str(data["desc"]).strip() if "desc" in data and len(str(data["desc"])) > 0 else ""
        data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        data["plan_status"] = 1
        insert_result = TestPlanService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"test_plan_create create test_plan exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 查询
@test_plan.route(f'/{URL_PREFIX}/test-plan/query', methods=['POST'])   # 数据集查询
def test_plan_query():
    et_log.info("############test_plan_query################")
    data = request.get_json()
    if not data or "plan_name" not in data or len(str(data["plan_name"]).strip()) == 0:
        data["plan_name"] = ""
    if not data or "create_by" not in data or len(str(data["create_by"]).strip()) == 0:
        data["create_by"] = None
    if not data or "plan_status" not in data or data["plan_status"] < 0:
        data["plan_status"] = None
    if not data or "create_from_time" not in data or len(str(data["create_from_time"]).strip()) == 0:
        data["create_from_time"] = ""
    if not data or "create_to_time" not in data or len(str(data["create_to_time"]).strip()) == 0:
        data["create_to_time"] = ""
    if not data or "page_num" not in data or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or "page_size" not in data or data["page_size"] < 1:
        data["page_size"] = 10
    page = TestPlanService.query_page(data["plan_name"], data["plan_status"], data["create_from_time"],
                                      data["create_to_time"], data["create_by"], data["page_num"], data["page_size"])
    page = "" if page is None else page.to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 删除
@test_plan.route(f'/{URL_PREFIX}/test-plan/del', methods=['POST'])   # 数据集删除
def test_plan_del():
    et_log.info("############test_plan_del################")
    data = request.get_json()
    if not data or "plan_id" not in data:
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = TestPlanService.delete(data["plan_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SYSTEM_INNER_ERROR.msg, "delete test plan error").to_json()

# 更新
@test_plan.route(f'/{URL_PREFIX}/test-plan/update', methods=['POST'])
def test_plan_update():
    et_log.info("############test_plan_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_plan_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "set_id in data_set  is invalid", "").to_json()
        if not data or 'plan_id' not in data:
            et_log.error(f"test_plan_update error, plan_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()
        plan_pk:TestPlan = TestPlanService.find_pk(data["plan_id"])
        if plan_pk is None:
            et_log.error(f"test_plan_update error, plan_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "plan_id id is invalid", "").to_json()

        plan_pk.plan_name = str(data["plan_name"]).strip() if "plan_name" in data and len(str(data["plan_name"])) > 0 else ""
        plan_pk.project_id = str(data["project_id"]).strip() if "project_id" in data and len(str(data["project_id"])) > 0 else ""
        plan_pk.start_time = str(data["start_time"]).strip() if "start_time" in data and len(str(data["start_time"])) > 0 else ""
        plan_pk.end_time = str(data["end_time"]).strip() if "end_time" in data and len(str(data["end_time"])) > 0 else ""
        plan_pk.desc = str(data["desc"]).strip() if "desc" in data and len(str(data["desc"])) > 0 else ""
        plan_pk.plan_status = data["plan_status"] if "plan_status" in data and data["plan_status"] is not None else 0
        plan_pk.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        update_result = TestPlanService.update(plan_pk)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"test_plan_update update data_set exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update exception").to_json()


# 一键评测
@test_plan.route(f'/{URL_PREFIX}/test-plan/auto', methods=['POST'])
def test_auto_eval():
    et_log.info("############test_auto_eval################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"test_auto_eval error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_auto_eval param is null", "").to_json()

        # thread = threading.Thread(
        #     target=TestPlanService.auto_test,
        #     args=(data.get("describe_auto"), CTX_OEM_CODES.get(), CTX_USER_ID.get()),
        # )
        # thread.start()
        # # test_result = TestPlanService.auto_test(data.get("describe_auto"))
        # return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        return Response(TestPlanService.auto_test(data.get("describe_auto"), CTX_OEM_CODES.get(), CTX_USER_ID.get()), mimetype='text/event-stream')
    except Exception as e:
        et_log.error(f"test_auto_eval exception:{e}")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "test_auto_eval exception").to_json()
