import traceback

from flask import Blueprint, request

from com.exturing.ai.test.comm.api_result import A<PERSON><PERSON><PERSON><PERSON>
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.dto.params.auto_dataset_dto import AutoDatasetDto
from com.exturing.ai.test.service.data_set_service import DataSetService

data_set=Blueprint('data_set',__name__)

# 数据集新增
@data_set.route(f'/{URL_PREFIX}/data-set/create', methods=['POST'])   # 数据集新增
def data_set_create():
    et_log.info("############data_set_create################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_create error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set_create error, param is null", "").to_json()
        if 'set_name' not in data and len(str(data["set_name"]).strip()) == 0:
            et_log.error(f"data_set_create error, set_name in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_name in param is null ", "").to_json()
        set_name = str(data["set_name"]).strip()
        dset_list = DataSetService.find_name(set_name, -1)
        if dset_list:
            et_log.error(f"data_set_create error, set_name has used")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "set_name has used", "").to_json()
        data["set_types"] = str(data["set_types"]).strip() if "set_types" in data and len(str(data["set_types"])) > 0 else ""
        data["objective"] = str(data["objective"]).strip() if "objective" in data and len(str(data["objective"])) > 0 else ""
        data["version"] = str(data["version"]).strip() if "version" in data and len(str(data["version"])) > 0 else ""
        data["vm_id"] = str(data["vm_id"]).strip() if "vm_id" in data and len(str(data["vm_id"])) > 0 else ""
        data["test_scope"] = data.get("test_scope", "")
        data["do_user"] = data["do_user"] if "do_user" in data else 0
        data["ds_status"] = 1
        insert_result = DataSetService.insert_one(data)
        if insert_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_result)).to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()
    except Exception as e:
        et_log.error(f"data_set_create create data_set exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "insert error").to_json()

# 数据集查询
@data_set.route(f'/{URL_PREFIX}/data-set/query', methods=['POST'])   # 数据集查询
def data_set_query():
    et_log.info("############data_set_query################")
    data = request.get_json()
    if not data or 'set_name' not in data or len(str(data["set_name"]).strip()) == 0:
        data["set_name"] = ""
    if not data or 'create_by' not in data or data.get("create_by", 0) == 0:
        data["create_by"] = -1
    if not data or 'page_num' not in data or data["page_num"] < 1:
        data["page_num"] = 1
    if not data or 'page_size' not in data or data["page_size"] < 1:
        data["page_size"] = 10
    page = DataSetService.query_page(data["set_name"], data["create_by"], data["page_num"], data["page_size"])
    page = "" if page is None else page.to_json()
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page).to_json()

# 数据集删除
@data_set.route(f'/{URL_PREFIX}/data-set/del', methods=['POST'])   # 数据集删除
def data_set_del():
    et_log.info("############data_set_del################")
    data = request.get_json()
    if not data or 'set_id' not in data:
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    del_result = DataSetService.delete(data["set_id"], data["do_user"])
    if del_result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "delete error").to_json()

# 更新数据集
@data_set.route(f'/{URL_PREFIX}/data-set/update', methods=['POST'])   # 数据集更新
def data_set_update():
    et_log.info("############data_set_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "set_id in data_set  is invalid", "").to_json()
        if not data or 'set_id' not in data:
            et_log.error(f"data_set_update error, set_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, "").to_json()
        dset = DataSetService.find_pk_dataset(data["set_id"])
        if dset is None:
            et_log.error(f"data_set_update error, set_id is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set id is invalid", "").to_json()
        if "set_name" in data:
            dset.name = data["set_name"]
        if "set_types" in data:
            dset.types = data["set_types"]
        if "objective" in data:
            dset.objective = data["objective"]
        if "version" in data:
            dset.version = data["version"]
        if "vm_id" in data:
            dset.vm_id = data["vm_id"]
        if "test_scope" in data:
            dset.test_scope = data["test_scope"]
        if "ds_status" in data:
            dset.ds_status = data["ds_status"]
        dset.update_by = data["do_user"] if "do_user" in data else 0
        update_result = DataSetService.update(dset)
        if update_result:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()
    except Exception as e:
        et_log.error(f"data_set_update update data_set exception")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "update error").to_json()

# 原始语料构建数据集
@data_set.route(f'/{URL_PREFIX}/data-set/build', methods=['POST'])
def data_set_build():
    et_log.info("############data_set_build################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_build error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or 'set_id' not in data:
            et_log.error(f"data_set_build error, set_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id is null", "").to_json()
        if not data or 'origin_ids' not in data:
            et_log.error(f"data_set_build error, origin_ids is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "origin_ids is null", "").to_json()
        set_id = data["set_id"]
        dset = DataSetService.find_pk_dataset(set_id)
        if dset is None:
            et_log.error(f"data_set_build error, set_id:{set_id} is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set id is invalid", "").to_json()
        origin_ids = data["origin_ids"]
        do_user = data["do_user"] if "do_user" in data else 0
        result = DataSetService.build_dataset_origin(set_id, origin_ids, do_user)
        
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()
    except Exception as e:
        et_log.error(f"data_set_build exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "build exception").to_json()

# 评测结果构建数据集
@data_set.route(f'/{URL_PREFIX}/data-set/build-by-result', methods=['POST'])
def data_set_build_by_result():
    et_log.info("############data_set_build_by_result################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_build_by_result error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or 'set_id' not in data:
            et_log.error(f"data_set_build_by_result error, set_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id is null", "").to_json()
        if not data or 'result_item_ids' not in data:
            et_log.error(f"data_set_build_by_result error, result_item_ids is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "origin_ids is null", "").to_json()
        if not data or 'task_result_id' not in data:
            et_log.error(f"data_set_build_by_result error, task_result_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id is null", "").to_json()
        set_id = data["set_id"]
        task_result_id = data["task_result_id"]
        dset = DataSetService.find_pk_dataset(set_id)
        if dset is None:
            et_log.error(f"data_set_build_by_result error, set_id:{set_id} is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set id is invalid", "").to_json()
        result_item_ids = data["result_item_ids"]
        result = DataSetService.build_dataset_result(set_id, task_result_id, result_item_ids)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()
    except Exception as e:
        et_log.error(f"data_set_build_by_result exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "build exception").to_json()

# 数据集构建新的数据集
@data_set.route(f'/{URL_PREFIX}/data-set/build-by-data-set', methods=['POST'])
def data_set_build_by_data_set():
    et_log.info("############data_set_build_by_data_set################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_build_by_data_set error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "param is null", "").to_json()
        if not data or 'set_id' not in data:
            et_log.error(f"data_set_build_by_data_set error, set_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id is null", "").to_json()
        if not data or 'data_set_item_ids' not in data:
            et_log.error(f"data_set_build_by_data_set error, data_set_item_ids is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "data_set_item_ids null", "").to_json()
        if not data or 'base_set_id' not in data:
            et_log.error(f"data_set_build_by_data_set error, base_set_id is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "base_set_id is null", "").to_json()
        set_id = data["set_id"]
        base_set_id = data["base_set_id"]
        dset = DataSetService.find_pk_dataset(set_id)
        if dset is None:
            et_log.error(f"data_set_build_by_data_set error, set_id:{set_id} is invalid")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set id is invalid", "").to_json()
        data_set_item_ids = data["data_set_item_ids"]
        result = DataSetService.build_dataset_by_dataset(set_id, base_set_id, data_set_item_ids)

        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()
    except Exception as e:
        et_log.error(f"data_set_build_by_data_set exception:{e},\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "build exception").to_json()


# 数据集构建新的数据集
@data_set.route(f"/{URL_PREFIX}/data-set/build-by-data-list", methods=["POST"])
def data_set_build_by_data_list():
    et_log.info("############data_set_build_by_data_list################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_set_build_by_data_list error, param is null")
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "param is null", ""
            ).to_json()
        if not data or "set_id" not in data:
            et_log.error(f"data_set_build_by_data_list error, set_id is null")
            return ApiResult(
                ResultCode.PARAM_IS_BLANK.code, "set_id is null", ""
            ).to_json()
        if not data or "data_list" not in data:
            et_log.error(
                f"data_set_build_by_data_list error, data_list is null"
            )
            return ApiResult(
                ResultCode.PARAM_IS_BLANK.code, "data_set_item_ids null", ""
            ).to_json()
        
        set_id = data["set_id"]
        data_list = data["data_list"]
        dset = DataSetService.find_pk_dataset(set_id)
        if dset is None:
            et_log.error(
                f"data_set_build_by_data_list error, set_id:{set_id} is invalid"
            )
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "data_set id is invalid", ""
            ).to_json()
        
        result = DataSetService.build_dataset_by_datalist(
            set_id, data_list
        )

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result
        ).to_json()
    except Exception as e:
        et_log.error(
            f"data_set_build_by_data_set exception:{e},\n{traceback.print_exc()}"
        )
        return ApiResult(
            ResultCode.SYSTEM_INNER_ERROR.code,
            ResultCode.SYSTEM_INNER_ERROR.msg,
            "build exception",
        ).to_json()


# 根据条件自动构建数据集
@data_set.route(f'/{URL_PREFIX}/data-set/auto-build', methods=['POST'])
def auto_build():
    et_log.info("############auto_build################")
    data = request.get_json()
    try:
        if isinstance(data, list) and len(data) > 0:
            auto_params = [AutoDatasetDto(**item) for item in data]
            set_id = DataSetService.auto_build_dataset(auto_params)
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, set_id).to_json()
        return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "auto_build dataset fail").to_json()
    except Exception as e:
        et_log.error(f"auto_build exception:{e}")
        traceback.print_exc()
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, ResultCode.SYSTEM_INNER_ERROR.msg, "auto_build exception").to_json()
