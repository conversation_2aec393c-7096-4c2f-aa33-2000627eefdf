# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.test_model import TestModelModel, TestModelQueryModel
from com.exturing.ai.test.comm.api_result import ApiR<PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_model_service import create, query_page, update, delete

test_model = Blueprint('test_model', __name__)

# 新增模型
@test_model.route(f'/{URL_PREFIX}/test-model/create', methods=['POST'])
def test_model_create():
    et_log.info("############test_model_create################")
    req_data = request.get_json()
    test_model_instance = TestModelModel(**req_data)
    test_model_id = create(test_model_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(test_model_id)).to_json()

# 模型分页查询
@test_model.route(f'/{URL_PREFIX}/test-model/page', methods=['POST'])
def test_model_page():
    et_log.info("############test_model_page################")
    data = request.get_json()
    query = TestModelQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改模型
@test_model.route(f'/{URL_PREFIX}/test-model/update', methods=['POST'])
def test_model_update():
    et_log.info("############test_model_update################")
    req_data = request.get_json()
    id = req_data.get("test_model_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_model_id is null", "").to_json()

    test_model_instance = TestModelModel(**req_data)
    update_result = update(id, test_model_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除模型
@test_model.route(f'/{URL_PREFIX}/test-model/del', methods=['POST'])
def test_model_delete():
    et_log.info("############test_model_delete################")
    req_data = request.get_json()
    id = req_data.get("test_model_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_model_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()