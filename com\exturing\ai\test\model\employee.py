from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel
import re


class EmployeeModel(BaseDataModel):
    name: Optional[str] = None  # 姓名
    level: Optional[str] = None  # 级别
    type: Optional[str] = None  # 员工类型
    salary: Optional[int] = None  # 薪资
    skill: Optional[str] = None  # 技术栈
    position: Optional[str] = None  # 岗位
    base: Optional[str] = None  # base地
    module: Optional[str] = None  # 所属模块
    project: Optional[str] = None  # 所属项目
    mentor: Optional[str] = None  # mentor
    entry_time: Optional[str] = None  # 入职时间
    leave_time: Optional[str] = None  # 离职时间
    department: Optional[str] = None  # 部门
    department_using: Optional[str] = None  # 使用部门
    department_product: Optional[str] = None  # 产品部门
    department_leader: Optional[str] = None  # 部门负责人
    education: Optional[str] = None  # 学历
    potential: Optional[str] = None  # 潜力
    email: Optional[str] = None  # 邮箱
    status: Optional[str] = None  # 当前状态
    account_id: Optional[int] = None  # 账号ID
    telephone: Optional[str] = None  # 电话
    bank_card: Optional[str] = None  # 银行卡号


class EmployeeQueryModel(BaseModel):
    name: Optional[str] = None
    status: Optional[list[str]] = None
    level: Optional[list[str]] = None
    type: Optional[list[str]] = None
    skill: Optional[list[str]] = None
    position: Optional[list[str]] = None
    base: Optional[str] = None
    module: Optional[list[str]] = None
    project: Optional[list[str]] = None
    mentor: Optional[str] = None
    department: Optional[list[str]] = None
    department_using: Optional[list[str]] = None
    department_product: Optional[list[str]] = None
    department_leader: Optional[list[str]] = None
    education: Optional[list[str]] = None
    potential: Optional[list[str]] = None
    salary_range: Optional[list[int]] = None
    entry_time_range: Optional[list[str]] = None
    leave_time_range: Optional[list[str]] = None
    telephone: Optional[str] = None  # 电话
    account_id: Optional[int] = None  # 账号ID

    def get_query_condition(self):
        condition = {"is_del": 0}
        or_conditions = []

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}
        if self.level:
            condition["level"] = {"$in": self.level}
        if self.type:
            condition["type"] = {"$in": self.type}
        if self.skill:
            condition["skill"] = {"$in": self.skill}
        if self.position:
            condition["position"] = {"$in": self.position}
        if self.base:
            condition["base"] = self.base
        if self.module:
            regex_conditions = [
                {"module": {"$regex": f"{re.escape(m)}"}} for m in self.module
            ]
            or_conditions.extend(regex_conditions)
            condition["$or"] = or_conditions
        if self.project:
            regex_conditions = [
                {"project": {"$regex": f"{re.escape(p)}"}} for p in self.project
            ]
            or_conditions.extend(regex_conditions)
            condition["$or"] = or_conditions
        if self.mentor:
            condition["mentor"] = self.mentor
        if self.department:
            condition["department"] = {"$in": self.department}
        if self.department_using:
            condition["department_using"] = {"$in": self.department_using}
        if self.department_product:
            condition["department_product"] = {"$in": self.department_product}
        if self.department_leader:
            condition["department_leader"] = {"$in": self.department_leader}
        if self.education:
            condition["education"] = {"$in": self.education}
        if self.potential:
            condition["potential"] = {"$in": self.potential}
        if self.status:
            condition["status"] = {"$in": self.status}

        if self.salary_range and len(self.salary_range) == 2:
            condition["salary"] = {
                "$gte": self.salary_range[0],
                "$lte": self.salary_range[1],
            }
        if self.entry_time_range and len(self.entry_time_range) == 2:
            condition["entry_time"] = {
                "$gte": self.entry_time_range[0],
                "$lte": self.entry_time_range[1],
            }
        if self.leave_time_range and len(self.leave_time_range) == 2:
            condition["leave_time"] = {
                "$gte": self.leave_time_range[0],
                "$lte": self.leave_time_range[1],
            }

        if self.telephone:
            condition["telephone"] = {
                "$regex": str(self.telephone).strip(),
                "$options": "i",
            }

        if self.account_id:
            condition["account_id"] = self.account_id

        return condition
