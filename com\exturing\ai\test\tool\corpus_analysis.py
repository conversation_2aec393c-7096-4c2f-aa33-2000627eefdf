import json
import re

import requests

from com.exturing.ai.test.comm.log_tool import et_log


class CorpusAnalysis:
    def __init__(self):
        pass

    def get_access_token(self):
        """
        获取 access_token
        """
        API_KEY = "LjdjpvXXrwsWmdOSPj1mxbeX"
        SECRET_KEY = "BgUbIQ47iUPmveroNxhTKsuydYbUcIFj"
        url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={API_KEY}&client_secret={SECRET_KEY}"
        response = requests.post(url)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            raise Exception(f"获取 access_token 失败：{response.text}")

    def data_gen(self, prompt_data):
        access_token = self.get_access_token()
        url = f"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-128k?access_token={access_token}"

        payload = json.dumps({
            "messages": [
                {
                    "role": "user",
                    "content": prompt_data
                }
            ],
            "stream": True
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.post(url, headers=headers, data=payload, stream=True)
        if response.status_code == 200:
            # 收集流式响应结果
            full_text = ""
            for line in response.iter_lines():
                if line:
                    try:
                        decoded_line = line.decode("UTF-8")
                        json_data = json.loads(decoded_line.replace("data: ", ""))
                        if "result" in json_data:
                            full_text += json_data["result"]
                    except json.JSONDecodeError:
                        continue
            return full_text
        else:
            raise Exception(f"API调用失败，HTTP状态码: {response.status_code}, 错误信息: {response.text}")

    def analyze_corpus(self, question):
        """
        分析语料并返回JSON结果
        """
        prompt_data = f"""你是一个数据分析师，请根据描述内容:{question},将其自动转换成下例中对应的json格式数据,并返回一条结果。
        参考如下例：
        情型1：
        描述内容：帮我挑选100条音乐的数据做一下长城项目的中枢Agent评测
        返回结果：{{"construction_manner": "选择", "amount_of_corpus_data": 100, "model": "音乐","project": "长城","test_object": "中枢Agent"}}

        情型2：
        描述内容：帮我挑选100条音乐的数据做一下长城项目的Agent评测
        返回结果：{{"construction_manner": "选择", "amount_of_corpus_data": 100, "model": "音乐","project": "长城","test_object": "agent"}}

        情型3：
        描述内容：帮我挑选10条音乐的数据做一下长城项目的中枢Agent评测
        返回结果：{{"construction_manner": "选择", "amount_of_corpus_data": 10, "model": "音乐","project": "长城","test_object": "中枢Agent"}}

        情型4：
        描述内容：帮我挑选10条音乐的数据做一下奇瑞项目的中枢Agent评测
        返回结果：{{"construction_manner": "选择", "amount_of_corpus_data": 10, "model": "音乐","project": "奇瑞","test_object": "中枢Agent"}}

        情型5：
        描述内容：帮我挑选10条闲聊及大语言回答的数据做一下长城项目的中枢Agent评测
        返回结果：{{"construction_manner": "选择", "amount_of_corpus_data": 10, "model": "闲聊及大语言回答","project": "长城","test_object": "中枢Agent"}}

        情型6：
        描述内容：帮我自动生成10条闲聊及大语言回答的数据做一下长城项目的中枢Agent评测
        返回结果：{{"construction_manner": "自动", "amount_of_corpus_data": 10, "model": "闲聊及大语言回答","project": "长城","test_object": "中枢Agent"}}
        """
        try:
            et_log.info(f"analyze_corpus prompt_data:{prompt_data}")
            result = self.data_gen(prompt_data)
            et_log.info(f"analyze_corpus qianfan result:{result}")
            if result.startswith('```json'):
                result = result[len('```json'):].strip()  # 去掉开头的 ```json
            if result.endswith('```'):
                result = result[:-3].strip()  # 去掉结尾的 ```
            result = result.replace('\n', '').replace(' ', '')  # 去掉换行符和空格
            result_json = json.loads(result)  # 转换为 Python 字典
            return result_json
        except Exception as e:
            return {"construction_manner": "N/A", "amount_of_corpus_data": "N/A", "model": "N/A","project": "N/A","test_object": "N/A"}  # 如果计算失败，返回错误信息

    def gen_ai_result(self, input_desc, result_dic, current_round=0):
        """
        对模型生成的结果打分，如果结果不正确，则用入参重新生成
        :param input_desc: 一键评估的入参语料
        :param result_dic: 结果字典
        :param current_round: 当前轮次
        :return: 检查通过后的结果字典（评分大于90的）
        """
        et_log.info(f"gen_ai_result input_desc:{input_desc} result_dic:{result_dic} current_round:{current_round}")
        try:
            current_round += 1
            auto_condition = {}
            if current_round >= 20:
                et_log.info(f"gen_ai_result fail, input_desc:{input_desc} result_dic:{result_dic} current_round:{current_round}")
                return {"construction_manner": "N/A", "amount_of_corpus_data": "N/A", "model": "N/A","project": "N/A","test_object": "N/A"}
            if not result_dic or str(result_dic).find("N/A") > 0:
                auto_condition = self.analyze_corpus(input_desc)
            eval_result_prompt = f"""你是一个模型评估专家，请根据描述内容:{input_desc};模型输出结果:{auto_condition}，对模型结果进行100分制打分，结果以｛"eval_score":评估的分数｝格式输出。"""
            eval_result_prompt += r"""
            参考如下例：
            举例1-0：
            描述内容：帮我挑选100条音乐的数据做一下长城项目的中枢评测
            模型输出结果：{"construction_manner": "挑选", "amount_of_corpus_data": 100, "model": "音乐","project": "长城","test_object": "中枢"}
            评估结果：｛"eval_score":100｝
            
            举例1-1：
            描述内容：帮我挑选100条音乐的数据做一下长城项目的中枢评测
            模型输出结果：{"construction_manner": "", "amount_of_corpus_data": 10, "model": "音乐","project": "长城","test_object": "中枢"}
            评估结果：｛"eval_score":10｝
            
            举例1-2：
            描述内容：帮我挑选100条音乐的数据做一下长城项目的中枢评测
            模型输出结果：{"construction_manner": "帮我", "amount_of_corpus_data": 100, "model": "音乐","project": "长城","test_object": "中枢"}
            评估结果：｛"eval_score":60｝
            
            举例2-0：
            描述内容：帮我自动生成86条闲聊及大语言回答的数据做一下长城项目的Agent评测
            模型输出结果：{"construction_manner": "自动", "amount_of_corpus_data": 86, "model": "闲聊及大语言回答","project": "长城","test_object": "agent"}
            评估结果：｛"eval_score":100｝
            
            举例2-1：
            描述内容：帮我自动生成86条闲聊及大语言回答的数据做一下长城项目的Agent评测
            模型输出结果：{"construction_manner": "自动", "amount_of_corpus_data": 86, "model": "闲聊及大语言回答","project": "长城项目","test_object": "agent评测"}
            评估结果：｛"eval_score":60｝
            注意：construction_manner值只需要满足语义相似，且对应值为的“选择”或“自动”，则该项即可得满分；其它项则需完全匹配，才可满分。
            """
            et_log.info(f"gen_ai_result eval_result_prompt:{eval_result_prompt}")
            eval_result = self.data_gen(eval_result_prompt)
            et_log.info(f"gen_ai_result eval_result:{eval_result}")
            pattern = r'\{.*?"eval_score":\s*\d+\s*\}'
            match = re.search(pattern, eval_result, re.DOTALL)
            if match:
                eval_result = match.group(0)
                et_log.info(f"gen_ai_result re eval_result:{eval_result}")
            eval_json = json.loads(eval_result)  # 转换为 Python 字典
            if eval_json and eval_json.get("eval_score", 0) >= 80:
                return auto_condition
            else:
                auto_condition = self.gen_ai_result(input_desc, {}, current_round)

            return auto_condition
        except Exception as e:
            et_log.error(f"gen_ai_result exception:{e}")
            return {"construction_manner": "N/A", "amount_of_corpus_data": "N/A", "model": "N/A","project": "N/A","test_object": "N/A"}  # 如果计算失败，返回错误信息

# 使用示例
if __name__ == "__main__":
    analyzer = CorpusAnalysis()
    question = "帮我挑选10条音乐数据，做一下长城项目的中枢agent评测"
    # result = analyzer.analyze_corpus(question)
    result = analyzer.gen_ai_result(question, {})
    print(json.dumps(result, indent=4, ensure_ascii=False))

