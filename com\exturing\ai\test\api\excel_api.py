import io

from flask import Blueprint, request, Flask,make_response

from com.exturing.ai.test.api.data_set_api import data_set_update
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.excel_util import ExcelUtil
from com.exturing.ai.test.comm.log_tool import et_log

excel_import=Blueprint('excel_import',__name__)

@excel_import.route(f'/{URL_PREFIX}/excel_import/dataset', methods=['POST'])   #导入数据集
def excel_import_dataset():
    et_log.info("############excel_import_dataset################")
    if 'excel_file' in request.files:
        uploaded_file = request.files['excel_file']
        # 先判断文件类型是否是Excel（简单判断扩展名，实际应用中可更严谨判断）
        if uploaded_file.filename.endswith('.xlsx') or uploaded_file.filename.endswith('.xls'):
            # 使用io.BytesIO或io.StringIO将文件对象转换为适合pandas读取的格式
            if uploaded_file.content_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                # 对于xlsx格式，使用BytesIO处理二进制数据
                file_content = io.BytesIO(uploaded_file.read())
            else:
                # 对于xls格式，使用StringIO处理文本数据（这里简单示意，实际可能需更适配处理）
                file_content = io.StringIO(uploaded_file.read().decode('utf-8'))
            ExcelUtil.dataset_import(file_content)
        return "File uploaded dataset successfully"

@excel_import.route(f'/{URL_PREFIX}/excel_import/result', methods=['POST'])   #导入测试结果
def excel_import_result():
    et_log.info("############excel_import_result################")
    # # 枚举内容打印
    # et_log.info(f"test enums name:{ResultCode.SUCCESS.name} code:{ResultCode.SUCCESS.code} "
    #             f"msg:{ResultCode.SUCCESS.msg}")

    if 'excel_file' in request.files:
        uploaded_file = request.files['excel_file']
        # 先判断文件类型是否是Excel（简单判断扩展名，实际应用中可更严谨判断）
        if uploaded_file.filename.endswith('.xlsx') or uploaded_file.filename.endswith('.xls'):
            # 使用io.BytesIO或io.StringIO将文件对象转换为适合pandas读取的格式
            if uploaded_file.content_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                # 对于xlsx格式，使用BytesIO处理二进制数据
                file_content = io.BytesIO(uploaded_file.read())
            else:
                # 对于xls格式，使用StringIO处理文本数据（这里简单示意，实际可能需更适配处理）
                file_content = io.StringIO(uploaded_file.read().decode('utf-8'))
            ExcelUtil.test_result_import(file_content)
        return "File uploaded result successfully"


# 导入维度字典数据
@excel_import.route(f'/{URL_PREFIX}/excel_import/model-dimension', methods=['POST'])
def excel_import_dimension():
    et_log.info("############excel_import_dimension################")

    if 'excel_file' in request.files:
        uploaded_file = request.files['excel_file']
        # 先判断文件类型是否是Excel（简单判断扩展名，实际应用中可更严谨判断）
        if uploaded_file.filename.endswith('.xlsx') or uploaded_file.filename.endswith('.xls'):
            # 使用io.BytesIO或io.StringIO将文件对象转换为适合pandas读取的格式
            if uploaded_file.content_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                # 对于xlsx格式，使用BytesIO处理二进制数据
                file_content = io.BytesIO(uploaded_file.read())
            else:
                # 对于xls格式，使用StringIO处理文本数据（这里简单示意，实际可能需更适配处理）
                file_content = io.StringIO(uploaded_file.read().decode('utf-8'))
            ExcelUtil.dimension_import(file_content)
        return "File uploaded model and dimension successfully"