# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.workforce_assessment import (
    WorkforceAssessmentAutoModel,
    WorkforceAssessmentModel,
    WorkforceAssessmentQueryModel,
)
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.workforce_assessment_service import (
    create,
    query_page,
    query_list,
    update,
    delete,
    auto_create,
    import_data,
)

workforce_assessment = Blueprint("workforce_assessment", __name__)


# 新增人力评估
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/create", methods=["POST"]
)
def workforce_assessment_create():
    et_log.info("############workforce_assessment_create################")
    req_data = request.get_json()
    workforce_assessment_instance = WorkforceAssessmentModel(**req_data)

    try:
        workforce_assessment_id = create(workforce_assessment_instance)

        return ApiResult(
            ResultCode.SUCCESS.code,
            ResultCode.SUCCESS.msg,
            str(workforce_assessment_id),
        ).to_json()

    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 人力评估分页查询
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/page", methods=["POST"]
)
def workforce_assessment_page():
    et_log.info("############workforce_assessment_page################")
    data = request.get_json()
    query = WorkforceAssessmentQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 人力评估列表查询
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/list", methods=["POST"]
)
def workforce_assessment_list():
    et_log.info("############workforce_assessment_list################")
    data = request.get_json()
    query = WorkforceAssessmentQueryModel(**data)

    data_list = query_list(query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, data_list
    ).to_json()


# 修改人力评估
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/update", methods=["POST"]
)
def workforce_assessment_update():
    et_log.info("############workforce_assessment_update################")
    req_data = request.get_json()
    id = req_data.get("workforce_assessment_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "workforce_assessment_id is null", ""
        ).to_json()

    workforce_assessment_instance = WorkforceAssessmentModel(**req_data)

    try:
        update_result = update(id, workforce_assessment_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 删除人力评估
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/del", methods=["POST"]
)
def workforce_assessment_delete():
    et_log.info("############workforce_assessment_delete################")
    req_data = request.get_json()
    id = req_data.get("workforce_assessment_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "workforce_assessment_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()


# 一键生成人力评估
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/auto-create", methods=["POST"]
)
def workforce_assessment_auto_create():
    et_log.info("############workforce_assessment_auto_create################")
    req_data = request.get_json()
    project_id = req_data.get("project_id")

    if project_id is None or len(project_id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
        ).to_json()

    query = WorkforceAssessmentAutoModel(**req_data)

    try:
        create_result = auto_create(query)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, create_result
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 人力评估数据导入
@workforce_assessment.route(
    f"/{URL_PREFIX}/staff/workforce-assessment/import", methods=["POST"]
)
def project_requirement_import():
    et_log.info("############workforce-assessment_import################")

    if "excel_file" in request.files:
        uploaded_file = request.files["excel_file"]
        project_id = request.form.get("project_id")

        if not project_id:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "project_id is null", ""
            ).to_json()

        if uploaded_file.filename.endswith(".xlsx") or uploaded_file.filename.endswith(
            ".xls"
        ):
            result = import_data(project_id, uploaded_file)

            if result:
                return ApiResult(
                    ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result
                ).to_json()
            else:
                return ApiResult(
                    ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "导入失败"
                ).to_json()
        else:
            return ApiResult(
                ResultCode.PARAM_IS_INVALID.code, "excel_file is not xlsx or xls", ""
            ).to_json()
    else:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "excel_file is null", ""
        ).to_json()
