import time
import json
from openai import OpenAI
from com.exturing.ai.test.comm.comm_constant import MIND2AGENT_URL,MIND2AGENT_BOT_MODEL
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_config import TestConfigInfoModel
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService


system_prompt = """
{
    "provider": "baidu",
    "model":"deepseek-r1-distill-qwen-32b",
    "device":"wangfeifei",
    "use_reasoner_model": true,
    "rctype":0
}
"""

class AgentllmytmindV2:
    @classmethod
    def run_chat(cls, config_info: TestConfigInfoModel, item: EtDataSetItem, record_id, request_id, session_id,
                                      parent_id, task_id,
                                      task_result_id, do_user):
        result = ""
        start_time = time.time()
        client = OpenAI(
            base_url = MIND2AGENT_URL,
            api_key = "none",
        )
        system_prompt_dict = json.loads(system_prompt)
        completion = client.chat.completions.create(
            model= MIND2AGENT_BOT_MODEL,  # 微调后的模型，需搭配系统提示词使用
            # model="DeepSeek-R1-Distill-Qwen-32B",  # 原始基座模型
            messages=[
                {"role": "system", "content": system_prompt_dict},
                {"role": "user", "content": item.question},

            ],
            temperature=0.3,
            stream = True,
            )
        end_time = time.time()
        elapsed_time = float(end_time - start_time)
        for chunk in completion:
            if chunk.choices:
                delta = chunk.choices[0].delta
                content = getattr(delta, "reasoning_content", "") or getattr(delta, "content", "")

                if content:  # 只有在 content 存在时才处理
                    result += content
                    print(content, end='')

            if chunk.choices[0].finish_reason == "stop":
                break
        et_log.info(f"_____________________{result}")
        test_task:TestTask = TestTask(**TestTask.find_by_pk(task_id))
        item_result = {"parent_id": parent_id, "data_set_item_id": str(item.id), "task_id": task_id,
                       "task_result_id": str(task_result_id), "expected_answer": item.expected_answer,
                       "expected_category": item.expected_category, "expected_task": item.expected_task,
                       "actual_answer": result, "eval_config_id":test_task.config_id,
                       "answer_score": 0, "qa_recall": 0, "first_res_time": elapsed_time,
                       "qa_use_time": elapsed_time, "recall_id": record_id,
                       "remark": f"request_id:{request_id}|session_id:{session_id}", "do_user": do_user,
                       "re_interval_time": 0, "is_websearch": 0}
        item_result_id = TestItemResultService.insert_one(item_result)
        et_log.info(f"run_mind2:{item_result_id}")
        return result
