import time
from com.exturing.ai.test.volcengine_public.volcengine_client import VolcengineClient

api_path = "/dpfm/v1/chat/intent"


def intent_api(data):
    """
    意图识别接口测试
    """
    # 测试数据

    with VolcengineClient() as client:
        start_time = time.time()
        response = client.post(
            path=api_path,
            data=data,
        )

        headers = response.headers
        response_json = response.json()

        result = {}

        if response_json.get("errno") == 0:
            result["log_id"] = headers.get("x-tt-logid")
            result["query"] = data.get("query")
            result["cost_total"] = int((time.time() - start_time) * 1000)

            res_data = response_json.get("data", {})

            result["question_id"] = res_data.get("question_id")
            result["intent_type"] = res_data.get("intent_type")
            result["origin_intent_type"] = res_data.get("origin_intent_type")
            result["cost_duration"] = (
                res_data.get("debug_info", {})
                .get("model_info", {})
                .get("intent-master-normal", {})
                .get("cost_duration", {})
            )

        return result


if __name__ == "__main__":
    test_data = {
        "query": "周杰伦有哪些歌？",
    }

    result = intent_api(test_data)
    print("Response:", result)

# python -m com.exturing.ai.test.volcengine_public.api_test.intent
