import json


# 分页对象
class PageResult:
    page_num: int # 当前页
    page_size: int # 每页记录数
    total_num: int # 总记录
    total_page: int # 总页数
    page_data = [] # 当前页记录集合

    skip: int # 跳过的记录数

    def __init__(self, page_num: int, page_size: int, total_num: int):
        self.page_num = 1 if page_num < 1 else page_num
        self.page_size = page_size if page_size else 10
        self.total_num = total_num
        if total_num > self.page_size:
            self.total_page = int(self.total_num/self.page_size)
            if self.total_num%self.page_size > 0:
                self.total_page += 1
        elif total_num > 0:
            self.total_page = 1
        else:
            self.total_page = 0
        self.skip = (self.page_num - 1) * self.page_size

    def to_json(self):
        return {"page_num": self.page_num, "page_size": self.page_size, "total_num": self.total_num,
                "total_page": self.total_page, "page_data": self.page_data}

