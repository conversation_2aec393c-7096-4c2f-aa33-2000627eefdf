import traceback

import sseclient #通过pip3 install sseclient-py 安装
import hashlib
import hmac
from hashlib import sha256
import base64
import time
import random
import json
import requests
import urllib.parse
from com.exturing.ai.test.comm.comm_constant import HUOSHAN_CHANNNEl, HUOSHAN_APPID, HUOSHAN_VEHICLE_ID
from com.exturing.ai.test.comm.log_tool import et_log






class VolcanoTool:
    def __init__(self, query, model_url, model_ak, model_sk):
        self.query = query
        self.model_url = model_url
        self.ak = model_ak
        self.sk = model_sk

    def gen_sign(self,method, querystr, body=None):
        """
        :param method: 请求方式
        :param querystr: 请求内容
        :param body: 请求参数
        :param sk: sk
        :return: 加密后的数据
        """
        querystr = urllib.parse.quote(querystr, safe=':/&=')
        a = querystr.split("&")
        a.sort()
        sortedquerystr = "&".join(a)
        strtosign = method + "\n" + sortedquerystr + "\n"
        if body is not None and len(body) > 0:
            m = hashlib.md5()
            m.update(body.encode("utf8"))
            strtosign += m.hexdigest() + "\n"
        h = hmac.new(self.sk.encode("utf8"), strtosign.encode("utf8"), sha256).digest()
        return base64.b64encode(h).decode()


    def reject_rec(self):
        """
        拒识方法
        :return: full_link_time 全链路耗时, model_res_time 模型耗时, reject_result 拒识结果 True=拒识 False=不拒识,
        log_id , question_id 对应query的请求id
        """
        try:
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&channel={HUOSHAN_CHANNNEl}&app_id={HUOSHAN_APPID}&vehicle_id={HUOSHAN_VEHICLE_ID}"
            params = {
                "query":self.query,
                "chat_id":"123456789",
            }
            body = json.dumps(params, ensure_ascii=False)
            et_log.info(f"请求bodey————————{body}")
            sign = self.gen_sign(method, querystr,body)
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "X-Use-PPE": "1",
                "X-Tt-Env": "ppe_vehicle_model_test",
                "content-type": "application/json;charset=utf-8"
            }
            # print(headers)
            # response = requests.post(f'{self.model_url}?{querystr}', headers=headers, data=body.encode("utf-8"), stream=True)
            # client = sseclient.SSEClient(response)
            # print(response.text)
            # for event in client.events():
            #     print(event.data)
            time.sleep(5)
                # 非SSE接口请求示范
            start_time = time.time()
            response = requests.post(f'{self.model_url}?{querystr}', headers=headers, data=body.encode("utf-8"))
            end_time = time.time()
            full_link_time = round(float(end_time - start_time),2)*1000
            # print(response)
            # for key, value in response.headers.items():
            #     et_log.info(f"{key}: {value}")
            log_id = response.headers.get('X-Tt-Logid','N/A')
            search_results = response.content.decode("utf-8")
            res = json.loads(search_results)
            et_log.info(f"reject_rec res:{res}")
            if 'rejection-parsing-model' in res['data']['debug_info']['model_info'] and 'cost_duration' in res['data']['debug_info']['model_info']['rejection-parsing-model']:
                model_res_time = res['data']['debug_info']['model_info']['rejection-parsing-model']['cost_duration']
            reject_result = res['data']['reject']

            et_log.info(f"豆包拒识接口结果-------{search_results},\n全链路耗时--------{full_link_time}，\n模型耗时--------{model_res_time}\n"
                        f"拒识结果--------{reject_result},\nlog_id----------{log_id}")
            return full_link_time, model_res_time, reject_result, log_id, res["data"]["question_id"]
        except Exception as e:
            et_log.error(f"VolcanoTool:reject_rec exception:{e},\n{traceback.print_exc()}")
            return 0, 0, False, "", ""

    def intent_rec(self):
        """
        意图识别方法
        :return: origin_intent_type 原始意图, final_intent_type 最终意图, model_res_time 模型耗时, full_link_time 全链耗时,
        log_id , question_id 对应query的请求id
        """
        try:
            intent_custom_intent_time = float(400)
            intent_master_normal_time = float(400)
            intent_web_intent_time = float(400)
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            querystr = f"_timestamp={timestamp}&_nonce={random_integer}&channel={HUOSHAN_CHANNNEl}&app_id={HUOSHAN_APPID}&vehicle_id={HUOSHAN_VEHICLE_ID}"
            params = {
                "query": self.query,
                "chat_id": "123456789",
            }
            body = json.dumps(params, ensure_ascii=False)
            et_log.info(f"请求bodey————————{body}")
            sign = self.gen_sign(method, querystr, body)
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "X-Use-PPE": "1",
                "X-Tt-Env": "ppe_vehicle_model_test",
                "content-type": "application/json;charset=utf-8"
            }
            time.sleep(5)
            start_time = time.time()
            response = requests.post(f'{self.model_url}?{querystr}', headers=headers, data=body.encode("utf-8"))
            end_time = time.time()
            full_link_time = round(float(end_time - start_time), 2) * 1000
            # print(response)
            # for key, value in response.headers.items():
            #     et_log.info(f"{key}: {value}")
            log_id = response.headers.get('X-Tt-Logid', 'N/A')
            search_results = response.content.decode("utf-8")
            res = json.loads(search_results)
            et_log.info(f"intent_rec_________{res}")
            origin_intent_type = res.get('data').get('origin_intent_type','N/A')
            final_intent_type = res.get('data').get('intent_type','N/A')
            question_id = res["data"]["question_id"]
            if 'intent-custom-intent' in res['data']["debug_info"]["model_info"] and 'cost_duration' in \
                    res["data"]["debug_info"]["model_info"]['intent-custom-intent']:
                intent_custom_intent_time = float(
                    res.get("data", {}).get("debug_info", {}).get("model_info", {}).get("intent-custom-intent", {}).get(
                        "cost_duration", '400'))
            if 'intent-master-normal' in res['data']["debug_info"]["model_info"] and 'cost_duration' in \
                    res["data"]["debug_info"]["model_info"]["intent-master-normal"]:
                intent_master_normal_time = float(
                    res.get("data", {}).get("debug_info", {}).get("model_info", {}).get("intent-master-normal", {}).get(
                        "cost_duration", '400'))
            if 'intent-web-intent' in res["data"]["debug_info"]["model_info"] and 'cost_duration' in \
                    res["data"]["debug_info"]["model_info"]["intent-web-intent"]:
                intent_web_intent_time = float(
                    res.get("data", {}).get("debug_info", {}).get("model_info", {}).get("intent-web-intent", {}).get(
                        "cost_duration", '400'))
            model_res_time = min(intent_master_normal_time, intent_web_intent_time, intent_custom_intent_time)
            et_log.info(f"意图返回结果：原始意图------{origin_intent_type},\n最终意图------{final_intent_type}\n"
                        f"意图模型性能----{model_res_time},\n意图性能-----{full_link_time}\n"
                        f"question_id-------{question_id},\nlog_id------{log_id}")
            return origin_intent_type, final_intent_type, model_res_time, full_link_time, log_id,question_id
        except Exception as e:
            et_log.error(f"VolcanoTool:intent_rec exception:{e},\n{traceback.print_exc()}")
            return "", "", 0, 0, "", ""











if __name__ == "__main__":
    # 示例参数 - 请替换为实际值
    query = "帮我画一副迪士尼城堡的照片"
    # model_url = "https://api-vehicle.volcengine.com/dpfm/v1/chat/rejection"
    model_ak = "extour2zmajhjksbgfa2s6mo6oolj3bf"
    model_sk = "nv6onlu6ufyi6q9xpim8qu630bopi7b1"

    # 创建VolcanoTool实例并调用reject_rec
    model_url = "https://api-vehicle.volcengine.com/dpfm/v1/chat/intent"
    # tool = VolcanoTool(query, model_url, model_ak, model_sk)
    tool = VolcanoTool("小跳蛙奥特曼", model_url, model_ak, model_sk)
    # tool.intent_rec()
    tool.model_url = "https://api-vehicle.volcengine.com/dpfm/v1/chat/rejection"
    tool.reject_rec()








