import asyncio
import concurrent.futures
import traceback
from datetime import datetime
from typing import <PERSON>ple

from bson import ObjectId
from pycparser.ply.yacc import MAXINT

from com.exturing.ai.test.agent.eval_doubao_agent import EvalDoubaoAgent
from com.exturing.ai.test.agent.eval_qianfang_agent import Eval<PERSON><PERSON><PERSON>angAgent
from com.exturing.ai.test.comm.comm_constant import *
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.dto.task_result_compare_dto import TaskResultCompareDto, ItemResultCompareDto
from com.exturing.ai.test.model.comm_prompt_template import CommPromptTemplateModel
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_result import TestTaskResult
from com.exturing.ai.test.service.comm_prompt_template_service import CommPromptTemplateService
from com.exturing.ai.test.service.test_config_service import find_config_info_re
from com.exturing.ai.test.service.test_env_service import TestEnvService


# 测试任务结果项Service
class TaskResultService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"test_task_result insert_one post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            task_id = post_data["task_id"]
            test_task_dic = TestTask.find_by_pk(task_id)
            data_set_id = ""
            eval_config_id = ""
            if test_task_dic:
                task = TestTask(**test_task_dic)
                data_set_id = str(task.data_set_id)
                eval_config_id = str(task.config_id)
            subjective_metric_id = post_data["subjective_metric_id"]
            objective_metric_id = post_data["objective_metric_id"]
            rate_final = post_data["rate_final"]
            rate_answer = post_data["rate_answer"]
            rate_category = post_data["rate_category"]
            rate_ad_final = post_data["rate_ad_final"]
            rate_ad_answer = post_data["rate_ad_answer"]
            rate_ad_category = post_data["rate_ad_category"]
            rate_recall = post_data["rate_recall"]
            satisfactory = post_data["satisfactory"]
            total_use_time = post_data["total_use_time"]
            avg_score = post_data["avg_score"]
            ctx_oem_codes = CTX_OEM_CODES.get()
            # eval_config_id = post_data.get("eval_config_id","")
            insert_id = TestTaskResult("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                       ctx_oem_codes, task_id, subjective_metric_id, objective_metric_id, rate_final, rate_answer, rate_category,
                                       rate_ad_final, rate_ad_answer, rate_ad_category, rate_recall, satisfactory, total_use_time,
                                       avg_score, 0, eval_config_id, data_set_id).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"test_task insert_one exception:{e}")
            traceback.print_exc()
            return None

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = TestTaskResult.find_by_pk(_id)
        if data_item is not None:
            return TestTaskResult(**data_item)
        return None

    # 查询 分页数据
    @classmethod
    def query_page(cls, task_id, result_status, create_from_time, create_to_time, page_num: int, page_size: int):
        et_log.info(f"test_task_result query_page params task_id:{task_id} result_status:{result_status}"
                    f"create_from_time:{create_from_time} create_to_time:{create_to_time} "
                    f"page_num:{page_num} page_size:{page_size}")
        try:
            condition = {}
            if task_id and len(str(task_id).strip()) > 0:
                condition["task_id"] = ObjectId(task_id)
            if result_status:
                condition["result_status"] = result_status
            create_time_filter = {}
            if create_from_time and len(str(create_from_time).strip()) > 0:
                create_time_filter["$gte"] = str(create_from_time).strip()
            if create_to_time and len(str(create_to_time).strip()) > 0:
                create_time_filter["$lte"] = str(create_to_time).strip()
            if create_time_filter is not None and len(create_time_filter) > 0:
                condition["create_time"] = create_time_filter
            # 查询匹配的数量
            total = TestTaskResult.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            # # TODO 演示临时添加
            # sorts = [("create_time", pymongo.ASCENDING)]
            # result_list = TestTaskResult.find_condition(condition, sorts, page_size, page.skip)
            result_list = TestTaskResult.find_condition(condition, None, page_size, page.skip)
            if result_list is None or len(result_list) < 1:
                et_log.error(f"test_task_result query_page not found data error")
                return None
            item_json_list = []
            for item in result_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_task_result query_page exception")
            traceback.print_exc()
            return None

    # 删除数据
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_task_result delete by _id:{_id} do_user:{do_user}")
        try:
            del_item = TestTaskResult.find_by_pk(_id)
            if del_item is None:
                et_log.error(f"test_task_result delete by _id error, _id not found data")
                return False

            # 删除id对应的数据记录
            TestTaskResult.delete_by_id(_id, do_user)

            # 删除id对应的测试结果数据项
            TestResult.del_by_result_id(_id, do_user)

            return True
        except Exception as e:
            et_log.error(f"test_task_result delete by _id exception")
            traceback.print_exc()
            return False

    # 更新
    @classmethod
    def update(cls, post_data:TestTaskResult):
        et_log.info(f"test_task_result update by post_data:{post_data}")
        try:
            # 更新-主键检查
            task:TestTaskResult = TestTaskResult.find_by_pk(post_data.id)
            if task is None:
                et_log.error(f"update test_task_result error, _id not found data")
                return 0
            # 更新请求的数据项数据
            return TestTaskResult.update_entity_json(post_data.to_json())
        except Exception as e:
            et_log.error(f"update by test_task_result exception")
            traceback.print_exc()
            return 0

    @classmethod
    def handler_timeout_task(cls):
        """
        超时未完成任务结果记录，标记成超时记录
        :return:
        """
        CTX_OEM_CODES.set(ALL_OEM_CODES)
        # 查询超时未完成的测试任务结果
        timeout_list = TestTaskResult.find_status_timeout()
        if not timeout_list or len(timeout_list) == 0:
            et_log.info(f"handler_timeout_task not found data")
            return
        # 超时未完成任务结果，状态标注成失败
        for task_result in timeout_list:
            task_result.result_status = -1  # 失败
            cls.update(task_result)

    @classmethod
    def rebuild_task_result(cls, task_result_id):
        """
        计算评测任务结果的相关指标
        :param task_result_id: 任务结果记录id
        :return:
        """
        et_log.info(f"rebuild_task_result param task_result_id:{task_result_id}")
        task_result = cls.find_pk(task_result_id)
        if not task_result:
            return
        test_task_dic = TestTask.find_by_pk(task_result.task_id)
        if test_task_dic is None:
            return
        test_task = TestTask(**test_task_dic)
        test_env = TestEnvService.find_pk(test_task.env_id)
        if not test_env:
            return
        # 异步计算准确率、召回率、F1指标
        asyncio.run(cls.calculate_task_result(task_result_id, test_env.adapter_code,0))

    @classmethod
    async def calculate_task_result(cls, task_result_id, adapter_code, use_time, do_user, oems):
        """
        计算任务结果相关指标
        :param use_time:
        :param task_result_id: 任务结果id
        :param adapter_code:适配代码[用来区分不同的结果标注逻辑]
        :param do_user:当前操作人
        :param oems:操作人所属OEM
        :return:
        """
        et_log.info(f"calculate_task_result param task_result_id:{task_result_id} adapter_code:{adapter_code} use_time:{use_time}")
        try:
            CTX_OEM_CODES.set(oems)
            CTX_USER_ID.set(do_user)
            result_dic = TestTaskResult.find_by_pk(str(task_result_id))
            if not result_dic:
                et_log.error(f"calculate_task_result not found test_task_result data")
                return

            # agent-llm-test agent autotest|agent-llm-hub中枢agent|dsk-llm-context语义上下文 需对回答打分
            # 回答先打分，明细打完同时给任务结果也计算平均分
            eval_score_code = [ENV_ADAPTER_CODE_AUTOTEST, ENV_ADAPTER_CODE_HUB, ENV_ADAPTER_CODE_CONTEXT, ENV_ADAPTER_CODE_QIANFAN3_5,
                               ENV_ADAPTER_CODE_QIANFAN4_0, ENV_ADAPTER_CODE_QIANWEN2_5, ENV_ADAPTER_CODE_QIANWEN3_32B,
                               ENV_ADAPTER_CODE_QIANWENA3B, ENV_ADAPTER_CODE_QIANWENQwQ_32B, ENV_ADAPTER_CODE_DOUBAO,
                               ENV_ADAPTER_CODE_DSR1, ENV_ADAPTER_CODE_DSV3, ENV_ADAPTER_CODE_HUAWEI_CONTEXT]
            if adapter_code in eval_score_code:
                await cls.eval_answer_score(task_result_id, adapter_code, do_user, oems)

            task_result = TestTaskResult(**result_dic)
            task_dic = TestTask.find_by_pk(task_result.task_id)
            if not task_dic:
                et_log.error(f"calculate_task_result not found test_task data")
                return
            test_task = TestTask(**task_dic)
            # 结果明细表总记录数
            result_count = TestResult.find_condition_count({"task_result_id":ObjectId(str(task_result_id))})
            # 结果明细表总正确数
            result_true_count = TestResult.find_condition_count({"task_result_id":ObjectId(str(task_result_id)), "result_final": 1})
            # 任务对应的数据集明细总数
            dataset_count = EtDataSetItem.find_comm_condition_count({"data_set_id": ObjectId(str(test_task.data_set_id))})
            et_log.info(f"calculate_task_result task_result_id:{task_result_id} data_set_id:{test_task.data_set_id} "
                        f"result_count:{result_count} result_true_count:{result_true_count} dataset_count:{dataset_count}")
            # 准确率
            rate_final = round(result_true_count*1.0000/result_count*100, 2)
            # 召回率
            rate_recall = round(result_true_count*1.0000/dataset_count*100, 2)
            # F1
            rate_category = 0 if 0 == rate_final and 0 == rate_recall else round(2*rate_final*rate_recall/(rate_final+rate_recall), 2)
            task_result.rate_final = rate_final
            task_result.rate_recall = rate_recall
            task_result.rate_category = rate_category
            # 总耗时、平均分
            total_use_time, total_score, average_score = TaskResultService.query_total_score(task_result_id)
            et_log.info(f"query_total_time_avg_score total_score:{total_score} average_score:{average_score}")
            task_result.total_use_time = total_use_time if not use_time or 0 == use_time else use_time
            task_result.avg_score = round(total_score*1.000/dataset_count, 2)
            task_result.result_status = 1 # 标注任务结果状态=执行完成
            TestTaskResult.update_entity_json(task_result.to_json())
        except Exception as e:
            et_log.error(f"calculate_task_result exception:{e},\n{traceback.print_exc()}")

    @classmethod
    async def eval_answer_score(cls, result_id, adapter_code, do_user, oems):
        """
        根据任务结果id，评估结果明细中回答评分
        :param result_id:任务结果id
        :param adapter_code:适配代码[用来区分不同的结果标注逻辑]
        :param do_user:当前操作人
        :param oems:操作人所属OEM
        :return:
        """
        et_log.info(f"eval_answer_score result_id:{result_id} adapter_code:{adapter_code}")
        try:
            CTX_OEM_CODES.set(oems)
            CTX_USER_ID.set(do_user)
            list_result = TestResult.find_condition({"task_result_id":ObjectId(str(result_id))}, None, MAXINT, 0)
            if not list_result or len(list_result) == 0:
                et_log.info(f"eval_answer_score list_result is null")
                return
            # 迭代更新问题结果标注
            # 创建线程池，设置最大线程数
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                # 提交任务到线程池
                futures = [executor.submit(cls.eval_item_answer_score,result_item, adapter_code, do_user, oems) for result_item in list_result]
                # 获取任务结果
                for future in concurrent.futures.as_completed(futures):
                    try:
                        # 指定线程执行超时时间为300秒
                        result = future.result(timeout=300)
                        et_log.info(f"eval_item_answer_score result: {result}")
                    except Exception as e:
                        et_log.error(f"eval_item_answer_score exception: {e}")
            # # 更新任务结果标注
            # result_dic = TestTaskResult.find_by_pk(result_id)
            # if result_dic:
            #     result = TestTaskResult(**result_dic)
            #     result.avg_score = round(total_score/len(list_result), 2)
            #     TestTaskResult.update_entity_json(result.to_json())
        except Exception as e:
            et_log.error(f"calculate_task_result exception:{e},\n{traceback.print_exc()}")

    @classmethod
    def eval_item_answer_score(cls, result_item:TestResult, adapter_code, do_user, oems):
        """
        对单条语料回答结果进行评分
        :param result_item: 语料结果记录对象
        :param adapter_code: 环境适配代码
        :param do_user:当前操作人
        :param oems:操作人所属OEM
        :return:
        """
        et_log.info(f"eval_item_answer_score result_item:{result_item} adapter_code:{adapter_code}")
        try:
            CTX_OEM_CODES.set(oems)
            CTX_USER_ID.set(do_user)
            if result_item.answer_score and result_item.answer_score > 0:# 不进行重复评分
                return False
            actual_answer = result_item.actual_answer
            if not actual_answer or len(actual_answer) == 0:# 空回答不评分
                return False

            expected_answer = result_item.expected_answer if result_item.expected_answer and len(result_item.expected_answer) > 0 else ""
            if ENV_ADAPTER_CODE_HUAWEI_CONTEXT == adapter_code:# 华为上下文指代
                set_item:EtDataSetItem = EtDataSetItem.find_by_pk(str(result_item.data_set_item_id))
                expected_answer = set_item.qa_keywords
            # 获取提示词模板内容
            prompt_content = CommPromptTemplateService.get_eval_pt(str(result_item.task_id), adapter_code)
            if not prompt_content or len(prompt_content) == 0:
                et_log.error(f"eval_item_answer_score prompt_content is empty, result_item:{result_item.to_json_str()} adapter_code:{adapter_code}")
                return False
            # 根据提示词模板内容进行评分
            score, score_reason = EvalQianFangAgent.eval_by_pt(prompt_content, result_item.question, expected_answer, actual_answer)
            et_log.info(f"score:{score},\nscore_reason:{score_reason}")
            # score, score_reason = EvalQianFangAgent.eval_by_env_code(adapter_code, result_item.question, expected_answer, actual_answer)
            # score, score_reason = EvalQianFangAgent.compare_text_semantics(result_item.question, expected_answer, actual_answer)
            # score = EvalDoubaoAgent.compare_text_semantics(result_item.question, expected_answer, actual_answer)
            result_item.answer_score = score
            result_item.answer_score_reason = score_reason
            result_answer = 1 if score >= 80 else 0
            result_item.result_answer = result_answer
            result_final = 0
            if ENV_ADAPTER_CODE_HUB == adapter_code:
                result_final = 1 if 1 == result_answer and 1 == result_item.result_category else 0
            elif (ENV_ADAPTER_CODE_AUTOTEST == adapter_code or ENV_ADAPTER_CODE_CONTEXT == adapter_code
                  or ENV_ADAPTER_CODE_HUAWEI_CONTEXT == adapter_code):# 回答评估
                result_final = result_answer
            else:
                result_final = result_answer
            result_item.result_final = result_final
            TestResult.update_entity_json(result_item.to_json())
            return True
        except Exception as e:
            et_log.error(f"calculate_task_result exception:{e},\n{traceback.print_exc()}")
            return False

    @classmethod
    def query_total_score(cls, result_id):
        """
        根据任务结果id,统计任务结果明细总耗时和回答平均得分
        :param result_id: 任务结果id
        :return:
        """
        et_log.info(f"query_total_score result_id:{result_id}")
        try:
            pipeline = [
                {
                    "$match": {
                        "task_result_id": ObjectId(result_id),
                        "is_del": 0
                }
              },
              {
                  "$group": {
                      "_id": None,
                      "total_use_time": {"$sum": "$qa_use_time" },
                      "total_score": {"$sum": "$answer_score" },
                      "average_score": {"$avg": "$answer_score" }
                  }
              }
            ]
            results = list(TestResult.aggregate(pipeline))
            if results:
                total_use_time = round(results[0]['total_use_time'], 2)
                total_score = round(results[0]['total_score'], 2)
                average_score = round(results[0]['average_score'], 2)
                return total_use_time, total_score, average_score
            else:
                et_log.error(f"query_total_score not found data")
                return 0, 0, 0
        except Exception as e:
            et_log.error(f"query_total_score exception:{e},\n{traceback.print_exc()}")
            return 0, 0, 0

    @classmethod
    def compare(cls, task_id, page_num, page_size):
        """
        根据任务id，获取对应的比较明细记录列表
        逻辑：先取任务的最近一条评估结果中的评测集id,判断最近5次是否都是同一个评测集，若是同一个，则返回结果比较分页
        :param task_id: 任务结果id数组
        :param page_num: 当前页
        :param page_size: 每页记录数
        :return: 任务结果明细比较记录-分页
        """
        et_log.info(f"compare task_id:{task_id} page_num:{page_num} page_size:{page_size}")
        try:
            new1_list:[] = TestTaskResult.find_condition({"task_id":ObjectId(task_id)}, None, 1, 0)
            if not new1_list or len(new1_list) == 0:
                et_log.error(f"compare error, task_id:{task_id} not found test_result data")
                return None
            new1:TestTaskResult = new1_list[0]
            if not new1.data_set_id or len(str(new1.data_set_id)) == 0:
                et_log.error(f"compare error, data_set_id:{new1.data_set_id} is empty")
                return None
            # 根据任务最近一条评估结果，获取其对应的最近5条相同评测集评估结果
            new5_list = TestTaskResult.find_condition({"task_id":ObjectId(task_id), "data_set_id":ObjectId(str(new1.data_set_id))}, None, 5, 0)
            if not new5_list or len(new5_list) == 0:
                et_log.error(f"compare error, task_id:{task_id} data_set_id:{new1.data_set_id} not found test_result data list")
                return None
            new5_ids = list([str(test_task_result._id) for test_task_result in new5_list])
            # 获取任务对应的最近5条评测集相同评估结果评测集对比分页
            return cls.compare_page(new5_ids, page_num, page_size)
        except Exception as e:
            et_log.error(f"compare exception:{e},\n{traceback.print_exc()}")
            return None

    @classmethod
    def compare_page(cls, result_ids, page_num, page_size):
        """
        根据任务结果id数组，获取对应的比较明细记录列表
        :param result_ids: 任务结果id数组
        :param page_num: 当前页
        :param page_size: 每页记录数
        :return: 任务结果明细比较记录-分页
        """
        et_log.info(f"compare_page result_ids:{result_ids} page_num:{page_num} page_size:{page_size}")
        try:
            if not result_ids or len(result_ids) == 0:
                et_log.error(f"compare_page error, result_ids:{result_ids} is empty")
                return None
            if page_size < 10:
                page_size = 10
            if page_num <= 0:
                page_num = 1

            # 检查参数result_ids对应的数据集是否一致
            check_flag, new_result = cls.check_result_dataset(result_ids)
            if not check_flag:
                return None
            # 查询匹配的数量
            condition = {"data_set_id":ObjectId(str(new_result.data_set_id))}
            total = EtDataSetItem.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return None
            page = PageResult(page_num, page_size, total)
            item_list = EtDataSetItem.find_condition(condition, None, page_size, page.skip)
            if item_list is None or len(item_list) < 1:
                et_log.error(f"test_task_result compare_page not found data error")
                return None
            item_json_list = []
            for item in item_list:# 返回集合转json格式
                compare_dic = {"set_item_id": str(item._id), "data_set_id": str(item.data_set_id), "question": item.question,
                               "expected_answer": item.expected_answer, "expected_category": item.expected_category, "result_new5":[]}
                compare_dto = TaskResultCompareDto(**compare_dic)
                compare_result_list = cls.build_compare_new5(compare_dto.set_item_id, result_ids)
                compare_dto.result_new5 = compare_result_list
                item_json_list.append(compare_dto.model_dump())
            page.page_data = item_json_list
            return page
        except Exception as e:
            et_log.error(f"test_task_result compare_page exception:{e}")
            traceback.print_exc()
            return None

    @classmethod
    def build_compare_new5(cls, set_item_id, result_ids):
        """
        构建TaskResultCompareDto返回DTO对象
        :param set_item_id: 任务对应评测集明细id
        :param result_ids: 任务结果id数组
        :return: 最新5条构建好的ItemResultCompareDto任务明细比对结果DTO
        """
        et_log.info(f"build_compare_new5 result_ids:{result_ids} set_item_id:{set_item_id}")
        try:
            compare_result_list = []
            for rid in result_ids:
                task_result_dic = TestTaskResult.find_by_pk(rid)
                task_result = TestTaskResult(**task_result_dic)
                conf_re = find_config_info_re(str(task_result.eval_config_id))
                item_compare_dto = ItemResultCompareDto(**{"result_id":rid, "result_datetime":task_result.create_time,
                                                           "conf_id": str(task_result.eval_config_id),
                                                           "conf_name": conf_re.config_name
                                                           })
                condition = {"data_set_item_id": ObjectId(str(set_item_id)),
                             "task_result_id": ObjectId(str(rid)), "task_id": ObjectId(str(task_result.task_id))}
                item_list = TestResult.find_condition(condition, None, 1, 0)
                if item_list and len(item_list) > 0:
                    item:TestResult = item_list[0]
                    item_compare_dto.actual_answer = item.actual_answer
                    item_compare_dto.actual_category = item.actual_category
                    item_compare_dto.first_res_time = round(item.first_res_time, 2)
                    item_compare_dto.qa_use_time = round(item.qa_use_time, 2)
                    item_compare_dto.answer_score = item.answer_score
                    item_compare_dto.result_answer = item.result_answer
                    item_compare_dto.result_category = item.result_category
                    item_compare_dto.result_final = item.result_final
                compare_result_list.append(item_compare_dto.model_dump())
            return compare_result_list
        except Exception as e:
            et_log.error(f"build_compare_new5 exception:{e}")
            traceback.print_exc()
            return []


    @classmethod
    def check_result_dataset(cls, result_ids) -> Tuple[bool, TestTaskResult|None] :
        """
        检查待比对的评测结果数据集是否一致
        :param result_ids: 评测结果id集
        :return: 一致=Ture 非一致=False | 最后一条TestTaskResult结果对象
        """
        et_log.info(f"check_result_dataset result_ids:{result_ids}")
        try:
            dataset_id = ""
            task_id = ""
            result = None
            for rid in result_ids:
                result_dic = TestTaskResult.find_by_pk(rid)
                if not result_dic:
                    et_log.error(f"check_result_dataset error, check result_id:{rid} not found data")
                    return False, None
                result = TestTaskResult(**result_dic)
                if not result.data_set_id or len(str(result.data_set_id)) == 0:
                    et_log.error(f"check_result_dataset error, check result_id:{rid} data_set_id is null")
                    return False, None
                if len(dataset_id) > 0 and dataset_id != str(result.data_set_id):
                    et_log.error(f"check_result_dataset error, check result_id:{rid} data_set_id is difference")
                    return False, None
                if len(task_id) > 0 and task_id != str(result.task_id):
                    et_log.error(f"check_result_dataset error, check result_id:{rid} task_id is difference")
                    return False, None
                dataset_id = str(result.data_set_id)
            return True, result
        except Exception as e:
            et_log.error(f"test_task_result check_result_dataset exception:{e}")
            traceback.print_exc()
            return False, None


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))

