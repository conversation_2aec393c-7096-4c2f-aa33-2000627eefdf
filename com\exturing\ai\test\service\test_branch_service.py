import traceback
from datetime import datetime

from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.test_env_branch import TestEnvBranch


# 测试分支Service
class TestEnvBranchService:

    # 新增
    @classmethod
    def insert_one(cls, post_data):
        et_log.info(f"test_env_branch insert_one post_data:{post_data}")
        try:
            # 插入当前请求数据项
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            branch_name = post_data["branch_name"]
            bol_val = cls.check_insert_branch_repeat(branch_name)
            if bol_val:
                et_log.error(f"test_env_branch insert_one branch_name:{branch_name} has already been used")
                return None
            ctx_oem_codes = CTX_OEM_CODES.get()
            insert_id = TestEnvBranch("", current_time, post_data["do_user"], current_time, post_data["do_user"], 0,
                                ctx_oem_codes, branch_name, post_data["branch_desc"], post_data["param_pid"], post_data["device_id"],
                                post_data["version"], post_data["user_id"]).insert_one()
            return insert_id
        except Exception as e:
            et_log.error(f"test_env insert_one exception")
            traceback.print_exc()
            return None

    @classmethod
    def check_insert_branch_repeat(cls, branch_name):
        et_log.info(f"check_insert_branch_repeat params branch_name:{branch_name}")
        try:
            condition = {}
            if branch_name and len(str(branch_name).strip()) > 0:
                condition["branch_name"] = str(branch_name).strip()
                count = TestEnvBranch.find_condition_count(condition)
                if count > 0:
                    return True
            return False
        except Exception as e:
            et_log.error(f"check_insert_branch_repeat test_env_branch exception")
            traceback.print_exc()
            return True

    # 根据主键查询数据
    @classmethod
    def find_pk(cls, _id):
        data_item = TestEnvBranch.find_by_pk(_id)
        if data_item is not None and len(data_item) > 0:
            return TestEnvBranch(**data_item)
        return None

    # 查询 分页数据
    @classmethod
    def query_branch_list(cls, branch_name):
        et_log.info(f"test_env_branch query_branch_list params branch_name:{branch_name}")
        try:
            condition = {}
            if branch_name and len(str(branch_name).strip()) > 0:
                condition["branch_name"] = {"$regex": str(branch_name).strip(), "$options": "i"}
            # 查询匹配的数量
            total = TestEnvBranch.find_condition_count(condition)
            if total < 1: # 未匹配到结果
                return []
            result_list = TestEnvBranch.find_condition(condition, None, total, 0)
            if result_list is None or len(result_list) < 1:
                et_log.error(f"test_env_branch query_branch_list not found data error")
                return None
            item_json_list = []
            for item in result_list:# 返回集合转json格式
                item_json_list.append(item.to_json_str())
            return item_json_list
        except Exception as e:
            et_log.error(f"test_env_branch query_branch_list exception")
            traceback.print_exc()
            return None

    # 删除数据
    @classmethod
    def delete(cls, _id, do_user):
        et_log.info(f"test_env_branch delete by _id:{_id} do_user:{do_user}")
        try:
            del_item = cls.find_pk(_id)
            if del_item is None:
                et_log.error(f"test_env_branch delete by _id error, _id not found data")
                return False
            # 删除id对应的数据记录
            TestEnvBranch.delete_by_id(_id, do_user)
            return True
        except Exception as e:
            et_log.error(f"test_env_branch delete by _id exception")
            traceback.print_exc()
            return False

    # 更新
    @classmethod
    def update(cls, post_data:TestEnvBranch):
        et_log.info(f"test_env_branch update by post_data:{post_data}")
        try:
            # 更新-主键检查
            data:TestEnvBranch = cls.find_pk(post_data.id)
            if data is None:
                et_log.error(f"update test_env_branch error, _id not found data")
                return 0
            # 更新-名称和代码检查
            if cls.check_update_branch_repeat(post_data.branch_name, data.id):
                # 更新请求的数据项数据
                return TestEnvBranch.update_entity_json(post_data.to_json())
            else:
                return 0
        except Exception as e:
            et_log.error(f"update by test_env_branch exception")
            traceback.print_exc()
            return 0

    # 更新环境时，检查名称和代码的重复性
    @classmethod
    def check_update_branch_repeat(cls, branch_name, up_id):
        et_log.info(f"check_update_env_repeat params branch_name:{branch_name} up_id:{up_id}")
        try:
            condition = {"_id": {"$ne": ObjectId(up_id)}}
            if branch_name and len(str(branch_name).strip()) > 0:
                condition["branch_name"] = str(branch_name).strip()
                count = TestEnvBranch.find_condition_count(condition)
                if count > 0:
                    return False
            return True
        except Exception as e:
            et_log.error(f"check_update_env_repeat test_env_branch exception")
            traceback.print_exc()
            return False


# print(DataSetService.find_pk_dataset("6780cfe226cabf8468795e33"))

