# service.py
import datetime
import traceback

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES, ALL_OEM_CODES
from com.exturing.ai.test.comm.comm_util import int_format, str_format, time_format
from com.exturing.ai.test.comm.send_mail_util import SendMailUtil
from com.exturing.ai.test.mail.employee_entry_html import employee_entry_html
from com.exturing.ai.test.model.employee import EmployeeModel, EmployeeQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log
from werkzeug.datastructures import FileStorage
import pandas as pd
from io import BytesIO

_doc = "employee"


# 新增员工
def create(data: EmployeeModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create employee: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询员工
def query_page(
    page_num: int = 1, page_size: int = 10, query: EmployeeQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        json_list = filter_sensitive_info(json_list)
        page.page_data = json_list

    return page


# 分页查询员工(包含敏感信息)
def query_page_all(
    page_num: int = 1, page_size: int = 10, query: EmployeeQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 员工名称列表
def query_name_list(query: EmployeeQueryModel = None):
    condition = query.get_query_condition()
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])

    name_list = []
    for item in result_list:
        name_list.append(
            {
                "_id": str(item["_id"]),
                "account_id": item.get("account_id"),
                "name": item.get("name"),
            }
        )

    return name_list


# 查询员工列表
def query_list(query: EmployeeQueryModel = None):
    condition = query.get_query_condition()
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])

    return result_list


# 修改员工
def update(id: str, data: EmployeeModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data_dict["update_time"] = current_time
    et_log.info(f"update employee: {data_dict}")
    not_del_keys = ["entry_time", "leave_time"]
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict, not_del_keys)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除员工
def delete(id: str) -> bool:
    et_log.info(f"delete employee by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 员工导入
def file_import(file: FileStorage) -> bool:
    df = pd.read_excel(file)
    records = df.to_dict(orient="records")
    employee_list = []

    for record in records:
        employee = {}
        employee["name"] = str_format("姓名", record)
        employee["level"] = str_format("级别", record)
        employee["type"] = str_format("员工类型", record)
        employee["salary"] = int_format("薪资", record)
        employee["skill"] = str_format("技术栈", record)
        employee["position"] = str_format("岗位", record)
        employee["base"] = str_format("base地", record)
        employee["module"] = replace_str(str_format("所属模块", record))
        employee["project"] = replace_str(str_format("所属项目", record))
        employee["mentor"] = str_format("mentor", record)
        employee["entry_time"] = time_format("入职时间", record)
        employee["leave_time"] = time_format("离职时间", record)
        employee["department"] = str_format("部门", record)
        employee["department_leader"] = str_format("部门负责人", record)
        employee["education"] = str_format("学历", record)
        employee["potential"] = str_format("潜力", record)
        employee["status"] = str_format("当前状态", record)

        employee_list.append(EmployeeModel(**employee).model_dump())

    inserted_ids = MongoDBUtil.insert_many(_doc, employee_list)

    return inserted_ids


def replace_str(value):
    if value is not None:
        value = value.replace("，", ",")
        value = value.replace("、", ",")
    return value


def send_entry_mail():
    et_log.info(f"########send_entry_mail########")
    try:
        CTX_OEM_CODES.set(ALL_OEM_CODES)
        # 获取当前日期
        current_date = datetime.datetime.now().date()
        # 计算明天的日期
        tomorrow_date = current_date + datetime.timedelta(days=1)
        condition = {
            "entry_time": tomorrow_date.strftime("%Y-%m-%d %H:%M:%S"),
            "status": "待入职",
        }
        # condition = {"entry_time": "2025-03-14 00:00:00"}
        result = MongoDBUtil.find_condition(_doc, condition)
        if not result:
            et_log.error(f"send_entry_mail not found data")
            return
        employees = [EmployeeModel(**user) for user in result]
        html_tr = r"""<tr class="ant-table-row ant-table-row-level-0">"""
        reserver_set = set()
        for item in employees:
            html_tr += f"""
            <td>{item.name}</td>
            <td>{item.level}</td>
            <td>{item.type}</td>
            <td>{item.department}</td>
            <td>{item.department_leader}</td>
            <td>{item.education}</td>
            <td>{item.skill}</td>
            <td>{item.position}</td>
            <td>{item.base}</td>
            <td>{item.project}</td>
            <td>{item.entry_time[:10]}</td>
            """
            leader = find_by_name(item.department_leader)
            if leader is not None and leader.email:
                reserver_set.add(leader.email)

        # reserver_set.add("<EMAIL>")
        html_tr += r"</tr>"

        subject = tomorrow_date.strftime("%Y-%m-%d") + "入职人员名单"
        send_html = employee_entry_html.substitute(employees_entry=html_tr)
        reserver_set.add("<EMAIL>")  # 程涛
        reserver_set.add("<EMAIL>")  # 沈聪
        reserver_set.add("<EMAIL>")  # 庞雅介
        reserver_set.add("<EMAIL>")  # 吴小航
        SendMailUtil.send_default_html(list(reserver_set), subject, send_html)
        et_log.info(f"send_entry_mail {subject} success")
        return
    except Exception as e:
        et_log.error(f"send_entry_mail exception:{e}")
        traceback.print_exc()
        return None


def find_by_name(employee_name):
    """
    根据员工姓名，查找员工记录
    :param employee_name:员工姓名
    :return:员工记录对象
    """
    et_log.info(f"find_by_name employee_name:{employee_name}")
    if not employee_name or len(employee_name) == 0:
        et_log.info(f"find_by_name employee_name:{employee_name} is empty")
    result = MongoDBUtil.find_one(_doc, {"name": employee_name})
    if not result:
        et_log.info(f"find_by_name employee_name:{employee_name} is invalid")
        return None
    return EmployeeModel(**result)


def filter_sensitive_info(employee_list):
    """
    过滤敏感信息
    :param employee_list: 员工列表
    :return: 过滤后的员工列表
    """
    for employee in employee_list:
        del employee["salary"]  # 删除薪资信息

    return employee_list


# CTX_OEM_CODES.set("all")
# send_entry_mail()


def export_data(query: EmployeeQueryModel = None):
    """
    导出员工数据
    :param query: 查询条件
    :return: 导出的数据
    """
    condition = query.get_query_condition()
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    result_list = filter_sensitive_info(result_list)

    export_list = []
    for item in result_list:
        export_item = get_export_item(item)
        export_list.append(export_item)

    export_df = pd.DataFrame(export_list)
    output = BytesIO()

    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        export_df.to_excel(writer, sheet_name="职员数据", index=False)

    excel_binary = output.getvalue()
    output.close()

    return excel_binary


def export_data_all(query: EmployeeQueryModel = None):
    """
    导出员工数据
    :param query: 查询条件
    :return: 导出的数据
    """
    condition = query.get_query_condition()
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])

    export_list = []
    for item in result_list:
        export_item = get_export_item(item)
        export_list.append(export_item)

    export_df = pd.DataFrame(export_list)
    output = BytesIO()

    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        export_df.to_excel(writer, sheet_name="职员数据", index=False)

    excel_binary = output.getvalue()
    output.close()

    return excel_binary


def get_export_item(item):
    """
    获取导出项
    :param item: 员工记录
    :return: 导出项
    """
    return {
        "姓名": item.get("name"),
        "级别": item.get("level"),
        "员工类型": item.get("type"),
        "薪资": item.get("salary"),
        "技术栈": item.get("skill"),
        "岗位": item.get("position"),
        "base地": item.get("base"),
        "所属模块": item.get("module"),
        "所属项目": item.get("project"),
        "mentor": item.get("mentor"),
        "入职时间": pd.to_datetime(item.get("entry_time"), errors="coerce"),
        "离职时间": pd.to_datetime(item.get("leave_time"), errors="coerce"),
        "部门": item.get("department"),
        "使用部门": item.get("department_using"),
        "产品部门": item.get("department_product"),
        "部门负责人": item.get("department_leader"),
        "学历": item.get("education"),
        "潜力": item.get("potential"),
        "邮箱": item.get("email"),
        "当前状态": item.get("status"),
        "电话": item.get("telephone"),
        "银行卡号": item.get("bank_card"),
    }
