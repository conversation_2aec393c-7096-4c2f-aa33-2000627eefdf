from typing import Any

import pymongo
from bson import ObjectId
from pandas.io.formats.format import return_docstring

from com.exturing.ai.test.comm.comm_constant import DEF_OID
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel


class DicInfo(BaseModel):
    _doc = "dic_info"
    pid: str    # 字典父id
    name: str   # 字典名称
    code: str   # 字典代码
    desc: str   # 字典描述
    order_by: int # 字典排序
    group_code: str # 字典分组代码
    group_name: str # 字典分组名称

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", pid="", name="", code="", desc="",
                 group_code="", order_by=1, group_name=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.pid = pid
        self.name = name
        self.code = code
        self.desc = desc
        self.group_code = group_code
        self.order_by = order_by
        self.group_name =group_name
        self.oem_codes = oem_codes

    def to_json(self):
        base_json = super().to_json()
        base_json["pid"] = ObjectId(str(self.pid)) if self.pid and len(str(self.pid)) > 0 else ""
        base_json["name"] = self.name
        base_json["code"] = self.code
        base_json["desc"] = self.desc
        base_json["group_code"] = self.group_code
        base_json["order_by"] = self.order_by
        base_json["group_name"] = self.group_name
        return base_json

    def to_json_str(self):
        json_data = self.to_json()
        json_data["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        json_data["pid"] = str(self.pid) if self.pid and len(str(self.pid)) > 0 else ""
        return json_data

    @classmethod
    def find_name(cls, pid, name, group_code):
        condition = {"is_del": 0}
        if pid is not None and len(str(pid)) > 0:
            condition["pid"] = ObjectId(pid)
        if name is not None and len(str(name)) > 0:
            condition["name"] = name
        if group_code is not None and len(str(group_code)) > 0:
            condition["group_code"] = group_code
        count = MongoDBUtil.find_count(cls._doc, condition)
        docs = MongoDBUtil.find_condition_page(cls._doc, condition, [("order_by", pymongo.ASCENDING)], 0, count)

        if docs is not None:
            results = []
            for doc in docs:
                pid = "" if DEF_OID == str(doc.get("pid")) else str(doc.get("pid"))
                dic = DicInfo(**doc)
                results.append(dic)
            return results
        else:
            return None

    # 查询字典表的分组信息
    @classmethod
    def query_group_list(cls, where, group, sorts):# 构建聚合管道
        # 构建聚合管道
        # pipeline = [
        #     # 模拟 WHERE 子句
        #     {"$match": {"name": "test"}},
        #     # 模拟 GROUP BY 子句
        #     {"$group": {"_id": {"year": "$year", "sex": "$sex"}}},
        #     # 模拟 ORDER BY 子句，先按 create_time 降序排序，再按 id 升序排序
        #     {"$sort": {"create_time": -1, "id": 1}}
        # ]
        where["is_del"] = 0
        if sorts is None:
            sorts = {"update_time": -1}
        match_oem_codes = MongoDBUtil.oem_codes_condition()
        where["oem_codes"] = match_oem_codes
        pipeline = [
            # 模拟 WHERE 子句
            {"$match": where},
            # 模拟 GROUP BY 子句
            {"$group": {"_id": group}},
            # 模拟 ORDER BY 子句，先按 create_time 降序排序，再按 id 升序排序
            {"$sort": sorts}
        ]
        # 执行聚合操作
        result = MongoDBUtil.aggregate(cls._doc, pipeline)
        result_list = []
        for doc in result:
            result_list.append({"group_code": doc["_id"]["group_code"], "group_name": doc["_id"]["group_name"]})
        return result_list




# DicInfo.find_name(ObjectId("df71f839588e9bd88e8be59d"), "初始化字典集合结构", "INIT")
