import json
import traceback

from com.exturing.ai.test.agent.eval_qianfang_agent import EvalQianFangAgent
from com.exturing.ai.test.comm.comm_constant import ENV_ADAPTER_CODE_QIANWEN2_5, ENV_ADAPTER_CODE_QIANWENA3B, \
    CTX_OEM_CODES, ENV_ADAPTER_CODE_DOUBAO, ENV_ADAPTER_CODE_DSR1, ENV_ADAPTER_CODE_DSV3, ENV_ADAPTER_CODE_QIANFAN4_5
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.dto.chat_box_result_dto import ChatBoxResultDto
from com.exturing.ai.test.dto.response_sse_dto import ResponseSseDto
from com.exturing.ai.test.dto.params.chat_box_dto import ChatBoxParamDto
from com.exturing.ai.test.dto.sse_stream_wrapper import SseStreamWrapper
from com.exturing.ai.test.enums.exception_enum import ResponseEnum
from com.exturing.ai.test.model.chat_box_result import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>ult
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.model.test_config import TestConfigInfoModel, TestConfigInfoReModel
from com.exturing.ai.test.service.origin_data_item_service import check_question, create
from com.exturing.ai.test.service.test_config_service import find_config_info, find_pk
from com.exturing.ai.test.tool.chat_box import ChatBox


class ChatBoxService:
    """
    闲聊ChatBox业务处理Service
    """
    @classmethod
    def query(cls, chat_param:ChatBoxParamDto):
        """
        闲聊ChatBox 问题处理方法
        :param chat_param:参数对象
        :return:
        """
        et_log.info(f"ChatBoxService.query chat_param:{chat_param.model_dump()}")
        try:
            if not chat_param or not chat_param.config_id or len(chat_param.config_id) == 0:
                et_log.error(f"ChatBoxService.query config_id is empty")
                resp_dto_dic = ResponseSseDto.construct_enum(ResponseEnum.PARAM_ERROR, 'config_id is empty').model_dump()
                yield f"""data: {str(resp_dto_dic).replace("'", '"')}\n\n"""
                return
            if not chat_param or not chat_param.question or len(chat_param.question) == 0:
                et_log.error(f"ChatBoxService.query question is empty")
                resp_dto_dic = ResponseSseDto.construct_enum(ResponseEnum.PARAM_ERROR, 'question is empty').model_dump()
                yield f"""data: {str(resp_dto_dic).replace("'", '"')}\n\n"""
                return
            config_dict = find_pk(chat_param.config_id)
            config_info:TestConfigInfoModel = find_config_info(chat_param.config_id)
            config_re_info = TestConfigInfoReModel(**config_info.model_dump())
            if not config_dict or not config_info:# 通道id未匹配到数据
                et_log.error(f"ChatBoxService.query config_id is error")
                resp_dto_dic = ResponseSseDto.construct_enum(ResponseEnum.PARAM_ERROR, 'config_id is error').model_dump()
                yield f"""data: {str(resp_dto_dic).replace("'", '"')}\n\n"""
                return

            config_re_info.config_name = config_dict["name"]
            response_stream_wrapper = None
            # 根据通道获取问题回答
            # 千问2.5
            if ENV_ADAPTER_CODE_QIANWEN2_5 == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).qw_25())
                # response_stream = ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).qw_25()
            # 千问3.0
            elif ENV_ADAPTER_CODE_QIANWENA3B == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question+"\no-think", config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).qw_30())
                # yield from ChatBox(chat_param.question+"\no-think", config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).qw_30()
                # response_stream = ChatBox(chat_param.question+"\no-think", config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).qw_30()
            # 豆包
            elif ENV_ADAPTER_CODE_DOUBAO == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).db())
                # yield from ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).db()
                # response_stream = ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).db()
            # deepseekR1
            elif ENV_ADAPTER_CODE_DSR1 == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).deepseek_R1())
                # yield from ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).deepseek_R1()
                # response_stream = ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).deepseek_R1()
            # deepseekV3
            elif ENV_ADAPTER_CODE_DSV3 == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).deepseek_v3())
                # yield from ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).deepseek_v3()
                # response_stream = ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).deepseek_v3()
            # 千帆4.5
            elif ENV_ADAPTER_CODE_QIANFAN4_5 == config_re_info.adapter_code:
                response_stream_wrapper = SseStreamWrapper(ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                        config_re_info.model_bot).ernie_45_turbo())
                # yield from ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).ernie_45_turbo()
                # response_stream = ChatBox(chat_param.question, config_re_info.model_url, config_re_info.model_ak, config_re_info.model_sk,
                #         config_re_info.model_bot).ernie_45_turbo()
            else:
                et_log.error(f"ChatBoxService.query not match adapter_code")
                resp_dto_dic = ResponseSseDto.construct_enum(ResponseEnum.PARAM_ERROR, 'not match adapter_code').model_dump()
                yield f"""data: {str(resp_dto_dic).replace("'", '"')}\n\n"""
                return

            result_dto:ChatBoxResultDto = ChatBoxResultDto()
            # for chunk in response_stream:
            #     et_log.info(f"ChatBoxService.query yield adapter_code:{config_info.adapter_code} chunk:{chunk}")
            #     chunk = chunk[5:].strip().replace("'", '"')
            #     result_dto = ChatBoxResultDto(**json.loads(chunk)["result"])
            #     # resp_sse_dto = ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", chunk)
            #     # yield f"data: {resp_sse_dto.model_dump()}\n\n"
            #     # yield f"data: {ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", chunk).model_dump()}\n\n".encode("utf-8")

            for chunk in response_stream_wrapper:
                et_log.info(f"ChatBoxService.query response_stream_wrapper chunk:{chunk}")
                yield chunk
                result_dto = ChatBoxResultDto(**json.loads(chunk[5:])["result"])

            if not result_dto or not result_dto.full_answer:
                et_log.error(f"ChatBoxService.query eval chat_box error, not return data")
                resp_dto_dic = ResponseSseDto.construct_enum(ResponseEnum.PARAM_ERROR, 'eval chat_box error, not return data').model_dump()
                yield f"""data: {str(resp_dto_dic).replace("'", '"')}\n\n"""
                return

            # 根据回答结果，获取问题回答评分
            answer_score = 0
            answer_score_reason = "未获取到评估结果"
            if result_dto.full_answer:
                # 进行模型回答评分
                answer_score, answer_score_reason = EvalQianFangAgent.eval_by_env_code(config_re_info.adapter_code, chat_param.question, chat_param.expected_answer, result_dto.full_answer)
                result_dto.answer_score = answer_score
                result_dto.answer_score_reason = answer_score_reason
                resp_sse_dto = ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", result_dto.model_dump())
                yield f"""data: {str(resp_sse_dto.model_dump()).replace("'", '"')}\n\n"""
            else:
                et_log.error(f"ChatBoxService.query eval chat_box error, eval answer score error")
                resp_sse_dto = ResponseSseDto.construct_enum(ResponseEnum.SERVICE_ERROR, 'eval answer score error').model_dump()
                yield f"""data: {str(resp_sse_dto).replace("'", '"')}\n\n"""

            # 保存模型返回结果
            chat_result = ChatBoxResult()
            chat_result.question = chat_param.question
            chat_result.model_id = chat_param.model_id
            chat_result.expected_category = chat_param.expected_category
            chat_result.expected_answer = chat_param.expected_answer
            chat_result.config_id = chat_param.config_id
            chat_result.chat_id = result_dto.chat_id
            chat_result.actual_answer = result_dto.full_answer
            chat_result.first_time = result_dto.first_time
            chat_result.use_time = result_dto.full_time
            chat_result.answer_score = answer_score
            chat_result.answer_score_reason = answer_score_reason
            chat_result.insert_one()

            # 检查原始语料，若该问题原始语料无数据，则补充原始语料
            origin_count = check_question(chat_param.question)
            if origin_count == 0 and chat_result.model_id is not None and len(chat_result.model_id) > 0:
                origin_dic = {
                    "model_id": str(chat_result.model_id),
                    "dimension_id": "",
                    "question": chat_param.question,
                    "expected_answer": chat_result.actual_answer if chat_result.answer_score > 80 else "",
                    "expected_task": chat_result.expected_category,
                    "expected_category": chat_result.expected_category,
                    "is_last": 1
                }
                origin_data = OriginDataItemModel(**origin_dic)
                origin_id = create(origin_data)
                et_log.info(f"ChatBoxService.query insert origin_id:{str(origin_id)}")
        except Exception as e:
            et_log.error(f"ChatBoxService.query exception:{e} \n\n {traceback.print_stack()}")
