import threading

from dbutils.pooled_db import PooledDB
import pymysql

from com.exturing.ai.test.comm.log_util import logger


class SQLHelper(object):
    """
    使用 threading.local 实现上下文管理，且是单例模式
    """
    def __init__(self):
        self.pool = PooledDB(
            creator=pymysql,  # 使用链接数据库的模块
            maxconnections=20,  # 连接池允许的最大连接数，0和None表示不限制连接数
            mincached=3,  # 初始化时，链接池中至少创建的空闲的链接，0表示不创建
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待。True，等待；False，不等待然后报错
            ping=0,
            # ping MySQL服务端，检查是否服务可用。如：0 = None = never, 1 = default = whenever it is requested, 2 = when a cursor is created, 4 = when a query is executed, 7 = always
            host='rm-bp1xr139p0k69eg4vvo.mysql.rds.aliyuncs.com',
            port=3306,
            user='user_rds_test',
            password='extour_rds_test01',
            database='model_testing_dev',
            charset='utf8'
        )
        self.local = threading.local()  # 维护一个栈
        """
        storage = {
            线程ID: {'stack': [(conn, cusor), ]},
        }
        """

    def open(self):
        conn = self.pool.connection()  # 去连接池中获取一个连接
        cur = conn.cursor()
        return conn, cur

    def close(self, conn, cur):
        cur.close()
        conn.close()  # 将连接放回到连接池，并不会关闭连接，当线程终止时，连接自动关闭

    def get_list(self, sql, args=None):
        """
        获取所有数据
        :param sql: SQL语句
        :param args: SQL语句的占位参数
        :return: 查询结果
        """
        conn, cur = self.open()
        logger.info("execute get_list sql:%s    args:%s", sql, args)
        cur.execute(sql, args)
        result = cur.fetchall()
        self.close(conn, cur)
        return result

    def get_one(self, sql, args=None):
        """
        获取单条数据
        :return: 查询结果
        """
        conn, cur = self.open()
        logger.info("execute get_one sql:%s    args:%s", sql, args)
        cur.execute(sql, args)
        result = cur.fetchone()
        self.close(conn, cur)
        return result

    def modify(self, sql, args=None):
        """
        修改、增加、删除操作
        :return: 受影响的行数
        """
        conn, cur = self.open()
        logger.info("execute modify sql:%s    args:%s", sql, args)
        result = cur.execute(sql, args)
        conn.commit()
        self.close(conn, cur)
        return result

    def bulk_modify(self, sql, args=None):
        """
        批量修改、增加、删除操作
        :return: 受影响的行数
        """
        conn, cur = self.open()
        logger.info("execute bulk_modify sql:%s    args:%s", sql, args)
        result = cur.executemany(sql, args)
        conn.commit()
        self.close(conn, cur)
        return result

    def create(self, sql, args=None):
        """
        增加数据
        :return: 新增数据行的ID
        """
        conn, cur = self.open()
        logger.info("execute create sql:%s    args:%s", sql, args)
        cur.execute(sql, args)
        conn.commit()
        self.close(conn, cur)
        return cur.lastrowid

    def __enter__(self):
        conn, cur = self.open()
        rv = getattr(self.local, 'stack', None)
        if not rv:
            self.local.stack = [(conn, cur), ]
        else:
            self.local.stack.append((conn, cur))
        return conn, cur

    def __exit__(self, exc_type, exc_val, exc_tb):
        rv = getattr(self.local, 'stack', None)
        if not rv:
            return
        conn, cur = self.local.stack.pop()
        cur.close()
        conn.close()


if __name__ == '__main__':
    db = SQLHelper()

    with db as (conn, cur):
        cur.execute("insert into data_set (name, types, objective, version) values (%s, %s, %s, %s)", ['test', '1', 'SQL验证', 'v1.0.0'])
        conn.commit()

    with db as (conn, cur):
        cur.execute("select * from data_set")
        # adapter = cur.fetchmany(3)  # 取前3条
        result = cur.fetchall()
        print(result)