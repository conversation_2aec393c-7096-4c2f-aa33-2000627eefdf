import os
import traceback
import io
from urllib.parse import quote
from bson import ObjectId
from com.exturing.ai.test.service.test_item_result_service import TestItemResultService
from flask import Blueprint, request, send_file
import pandas as pd
from com.exturing.ai.test.service.test_task_service import TestTaskService
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX, DEF_OID
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.data_item_service import DataItemService
from com.exturing.ai.test.service.data_set_service import DataSetService

data_item=Blueprint('data_item',__name__)
test_item_result=Blueprint('test_item_result',__name__)

# 数据集数据项查询
@data_item.route(f'/{URL_PREFIX}/data-item/query', methods=['POST'])   # 数据集数据项查询
def data_item_query():
    et_log.info("############data_item_query################")
    data = request.get_json()
    if not data or 'set_id' not in data:
        et_log.error(f"data_item_query error, set_id not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id not in param", "").to_json()
    if 'question' not in data or len(str(data["question"]).strip()) == 0:
        data["question"] = ""
    if 'model_id' not in data or len(str(data["model_id"]).strip()) == 0:
        data["model_id"] = ""
    if 'dimension_id' not in data or len(str(data["dimension_id"]).strip()) == 0:
        data["dimension_id"] = ""
    if 'real_time_start' not in data or len(str(data["real_time_start"]).strip()) == 0:
        data["real_time_start"] = ""
    if 'real_time_end' not in data or len(str(data["real_time_end"]).strip()) == 0:
        data["real_time_end"] = ""
    if 'item_tags' not in data or len(data["item_tags"]) == 0:
        data["item_tags"] = []
    if 'page_num' not in data or data["page_num"] < 1:
        data["page_num"] = 1
    if 'page_size' not in data or data["page_size"] < 1:
        data["page_size"] = 10

    model_dimension = data.get("model_dimension", {})

    page = DataItemService.query_page(data["set_id"], data["model_id"], data["dimension_id"], data["question"],
                                      model_dimension, data["item_tags"], data["page_num"], data["page_size"],
                                      data["real_time_start"], data["real_time_end"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "" if page is None else page.to_json()).to_json()

# 数据集数据项 删除
@data_item.route(f'/{URL_PREFIX}/data-item/del', methods=['POST'])
def data_item_del():
    et_log.info("############data_item_del################")
    data = request.get_json()
    if not data or "item_id" not in data:
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "", "").to_json()

    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    result = DataItemService.delete(data["item_id"], data["do_user"])
    if result:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
    else:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "delete data_set_item error", "").to_json()

# 更新数据项
@data_item.route(f'/{URL_PREFIX}/data-item/update', methods=['POST'])
def data_item_update():
    et_log.info("############data_item_update################")
    data = request.get_json()
    try:
        if not data:
            et_log.error(f"data_item_update error, param is null")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "set_id in data_set  is invalid", "").to_json()
        if not data or 'item_id' not in data:
            et_log.error(f"data_item_update error, item_id in param is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "item_id must have a value", "").to_json()
        _item = DataItemService.find_pk(data["item_id"])
        if _item is None:
            et_log.error(f"data_item_update error, item_id not found data")
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "data_set_item id is invalid", "").to_json()

        if "parent_id" in data:
            _item.parent_id = data["parent_id"]
        if "data_set_id" in data:
            _item.data_set_id = data["data_set_id"]
        if "metric_id" in data:
            _item.metric_id = data["metric_id"]
        if "model_id" in data:
            _item.model_id = data["model_id"]
        if "dimension_id" in data:
            _item.dimension_id = data["dimension_id"]
        if "item_type" in data:
            _item.item_type = data["item_type"]
        if "scene_id" in data:
            _item.scene_id = data["scene_id"]
        if "dimension_id" in data:
            _item.question = data["question"]
        if "expected_answer" in data:
            _item.expected_answer = data["expected_answer"]
        if "expected_task" in data:
            _item.expected_task = data["expected_task"]
        if "expected_category" in data:
            _item.expected_category = data["expected_category"]
        if "item_tags" in data:
            _item.item_tags = data["item_tags"]
        if "real_time" in data:
            _item.real_time = data["real_time"]
        if "data_src" in data:
            _item.data_src = data.get("data_src", "")
        _item.qa_keywords = data.get("qa_keywords","")
        _item.update_by = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
        update_result = DataItemService.update(_item)
        if update_result and update_result.modified_count > 0:
            return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
        else:
            return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "update data_set_item error", "").to_json()
    except Exception as e:
        et_log.error(f"data_item_update update data_set_item exception,\n{traceback.print_exc()}")
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "update data_set_item exception", "").to_json()

# 检查数据项是否重复（相同数据集、可带模块、维度等条件）
@data_item.route(f'/{URL_PREFIX}/data-item/check', methods=['POST'])
def check_question():
    et_log.info("############check_question################")
    data = request.get_json()
    if not data or 'set_id' not in data:
        et_log.error(f"check_question error, set_id not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id not in param", "").to_json()
    if 'question' not in data or len(str(data["question"]).strip()) == 0:
        et_log.error(f"check_question error, question not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "question not in param", "").to_json()
    if 'model_id' not in data or len(str(data["model_id"]).strip()) == 0:
        data["model_id"] = ""
    if 'dimension_id' not in data or len(str(data["dimension_id"]).strip()) == 0:
        data["dimension_id"] = ""

    bol_val = DataItemService.check_question(data["set_id"], data["model_id"], data["dimension_id"], data["question"])
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, bol_val).to_json()

# 新增数据项
@data_item.route(f'/{URL_PREFIX}/data-item/create', methods=['POST'])
def create():
    et_log.info("############create################")
    data = request.get_json()
    if not data or 'set_id' not in data:
        et_log.error(f"check_question error, set_id not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id not in param", "").to_json()
    if 'question' not in data or len(str(data["question"]).strip()) == 0:
        et_log.error(f"check_question error, question not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "question not in param", "").to_json()
    if 'parent_id' not in data or len(str(data["parent_id"]).strip()) == 0:
        data["parent_id"] = ""
    if 'model_id' not in data or len(str(data["model_id"]).strip()) == 0:
        data["model_id"] = ""
    if 'dimension_id' not in data or len(str(data["dimension_id"]).strip()) == 0:
        data["dimension_id"] = ""
    if 'metric_id' not in data or len(str(data["metric_id"]).strip()) == 0:
        data["metric_id"] = ""
    if 'item_type' not in data:
        data["item_type"] = 0
    if 'scene_id' not in data or len(str(data["scene_id"]).strip()) == 0:
        data["scene_id"] = ""
    if 'expected_answer' not in data or len(str(data["expected_answer"]).strip()) == 0:
        data["expected_answer"] = ""
    if 'expected_task' not in data or len(str(data["expected_task"]).strip()) == 0:
        data["expected_task"] = ""
    if 'expected_category' not in data or len(str(data["expected_category"]).strip()) == 0:
        data["expected_category"] = ""
    if 'item_tags' not in data or len(str(data["item_tags"]).strip()) == 0:
        data["item_tags"] = ""
    if 'real_time' not in data or len(str(data["real_time"]).strip()) == 0:
        data["real_time"] = ""
    if 'data_src' not in data or len(str(data["data_src"]).strip()) == 0:
        data["data_src"] = ""
    data["is_last"] = 1
    data["do_user"] = data["do_user"] if "do_user" in data and data["do_user"] is not None else 0
    insert_id = DataItemService.insert_one(data)
    if insert_id:
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(insert_id)).to_json()
    else:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "insert error", "").to_json()

# 获取所有子数据项的列表
@data_item.route(f'/{URL_PREFIX}/data-item/query-child', methods=['POST'])
def query_child():
    et_log.info("############query_child################")
    try:
        data = request.get_json()
        if not data or "item_pid" not in data or len(str(data["item_pid"])) == 0:
            et_log.error(f"query_child error, item_pid is null")
            return ApiResult(ResultCode.PARAM_IS_BLANK.code, "item_pid is null", "").to_json()
        result_list = DataItemService.query_child_list(str(data["item_pid"]))
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result_list).to_json()
    except Exception as e:
        et_log.error(f"query_child query data_set_item child list exception")
        traceback.print_exc()
        return ApiResult(ResultCode.INTERFACE_INNER_INVOKE_ERROR.code, f"query_child exception", "").to_json()

# 按data_set_id批量更新数据项
@data_item.route(f'/{URL_PREFIX}/data-item/update-by-set-id', methods=['POST'])
def update_by_set_id():
    et_log.info("############update-by-set-id################")
    data = request.get_json()
    if not data or 'data_set_id' not in data:
        et_log.error(f"update_by_set_id error, data_set_id not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "data_set_id not in param", "").to_json()
    
    res = DataItemService.update_by_set_id(data['data_set_id'], data)

    if res is None:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "update error", "").to_json()
    
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()

# 按ids批量更新数据项
@data_item.route(f'/{URL_PREFIX}/data-item/update-batch', methods=['POST'])
def update_batch():
    et_log.info("############update-batch################")
    data = request.get_json()
    if not data or 'ids' not in data or len(data["ids"]) < 1:
        et_log.error(f"update_by_ids error, data_ids not in param")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "ids not in param", "").to_json()
    
    res = DataItemService.update_batch(data['ids'], data)

    if res is None:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "update error", "").to_json()
    
    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()

# 导出数据集数据
@data_item.route(f'/{URL_PREFIX}/data-item/export', methods=['POST'])
def origin_data_item_export():
    et_log.info("############origin_data_item_export################")
    data = request.get_json()
    set_id = data.get("set_id", "")
    export_keys = data.get("export_keys", [])

    if not set_id:
        et_log.error(f"origin_data_item_export error, set_id is null")
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, "set_id is null", "").to_json()

    excel_binary = DataItemService.export_data(set_id, export_keys)
    return excel_binary

@data_item.route(f'/{URL_PREFIX}/data-item/del-batch', methods=['POST'])
def del_batch():
    et_log.info("############data-item del_batch################")
    data = request.get_json()
    if not data or "data_set_id" not in data or len(data["data_set_id"]) < 1:
        et_log.error(f"del_batch error, data_set_id not in param")
        return ApiResult(
            ResultCode.PARAM_IS_BLANK.code, "data_set_id not in param", ""
        ).to_json()

    if not data or "item_ids" not in data or len(data["item_ids"]) < 1:
        et_log.error(f"del_batch error, item_ids not in param")
        return ApiResult(
            ResultCode.PARAM_IS_BLANK.code, "item_ids not in param", ""
        ).to_json()

    res = DataItemService.delete_batch(data["data_set_id"], data["item_ids"])

    if res is None:
        return ApiResult(ResultCode.SYSTEM_INNER_ERROR.code, "delete error", "").to_json()

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, "").to_json()
