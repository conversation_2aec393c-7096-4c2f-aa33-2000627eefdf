import traceback

from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.dto.params.metric_calib_dto import MetricCalibDto
from com.exturing.ai.test.model.test_metric_calib import TestMetricCalibModel


class TestMetricCalibService:
    """
    （重构）指标标定Service
    """

    @classmethod
    # 新增标定指标
    def create(cls, data: TestMetricCalibModel):
        et_log.info(f"create test_metric_calib: {None if data is None else data.model_dump()}")
        try:
            if not TestMetricCalibModel.check_name(None, data.metric_name, data.metric_pid):
                et_log.error(f"create test_metric_calib metric_name:{data.metric_name} metric_pid:{data.metric_pid} check fail")
                return None
            result_id = data.insert_one()
            return result_id
        except Exception as e:
            et_log.error(f"check_name exception:{e},\n{traceback.print_exc()}")
            return None

    @classmethod
    # 分页查询标定指标
    def query_page(cls, page_num: int = 1, page_size: int = 10, query: MetricCalibDto = None):
        et_log.info(f"query_page test_metric_calib query: {None if query is None else query.model_dump()}")
        try:
            condition = query.build_dto_condition()
            metric_name = condition.get("metric_name", "").strip()
            if metric_name and len(metric_name) > 0:
                condition["metric_name"] = {"$regex": metric_name, "$options": "i"}
            total = TestMetricCalibModel.find_condition_count(condition)
            page = PageResult(page_num, page_size, total)
            if total > 0:
                result = TestMetricCalibModel.find_page_list(condition, None, page.skip, page_size)
                result_list = list(result or [])
                json_list = [data.serialize_model() for data in result_list]
                page.page_data = json_list
            return page
        except Exception as e:
            et_log.error(f"query_page test_metric_calib exception:{e},\n{traceback.print_exc()}")
            return None

    @classmethod
    # 修改标定指标
    def update(cls, metric_id: str, data: TestMetricCalibModel) -> bool:
        data_dict = data.model_dump()
        data_dict['_id'] = metric_id
        if not TestMetricCalibModel.check_name(metric_id, data.metric_name, data.metric_pid):
            et_log.error(f"update test_metric_calib metric_id:{metric_id} metric_name:{data.metric_name} metric_pid:{data.metric_pid} check fail")
            return False
        et_log.info(f"update test_metric_calib: {data_dict}")
        update_num = TestMetricCalibModel.update_entity_json(data_dict)
        if update_num.modified_count and update_num.modified_count > 0:
            return True
        else:
            return False

    @classmethod
    # 删除标定指标
    def delete(cls, metric_id: str) -> bool:
        et_log.info(f"delete test_metric_calib by metric_id:{metric_id}")
        return TestMetricCalibModel.delete_by_id(metric_id, 0) > 0