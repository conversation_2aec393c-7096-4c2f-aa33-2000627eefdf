import os
import stat
from datetime import datetime
from loguru import logger

from com.exturing.ai.test.comm.comm_constant import LOG_PATH

et_log: logger = None
def configure_logger(log_file_path: str = '/app.log', level: str = 'INFO') -> logger:
    """
    配置日志记录器
    :param log_file_path: 日志文件路径
    :param level: 日志级别，可选值为 DEBUG, INFO, WARNING, ERROR, CRITICAL
    """

    # logfile_dir = os.path.dirname(os.path.abspath(__name__)) + "../../../../../../logs/ai_test" # log文件的目录
    # logfile_dir = "/var/logs/ai_test" # log文件的目录
    logfile_dir = LOG_PATH # log文件的目录
    log_file_path = logfile_dir + log_file_path
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file_path)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        os.chmod(log_dir, stat.S_IRWXU + stat.S_IRWXG + stat.S_IRWXO)  # 设置文件权限为rw-r--r--

    # 设置日志格式
    formatter = "{time:YYYY-MM-DD HH:mm:ss} | {process.name}:{thread.name} | {module}.{function}:{line} | {level} | {message}"

    # 创建日志记录器
    logger.add(log_file_path, format=formatter, level=level.upper(), rotation="00:00", retention="7 days",
               backtrace=True, catch=True)
    # logger.add(lambda msg: print(msg), format=formatter, level=level.upper())
    return logger

et_log = configure_logger('/et-app.log', 'DEBUG') if et_log is None else et_log

# class LogTool:
#
#     @staticmethod
#     def configure_logger(log_file_path='/app.log', level='INFO'):
#         """
#         配置日志记录器
#         :param log_file_path: 日志文件路径
#         :param level: 日志级别，可选值为 DEBUG, INFO, WARNING, ERROR, CRITICAL
#         """
#         # logfile_dir = os.path.dirname(os.path.abspath(__file__)) + "../../../../../../log"  # log文件的目录
#         # logfile_dir = "./" + "log"  # log文件的目录
#         # logfile_dir = os.path.dirname(os.path.abspath(__name__)) +  "../../../../../../log"  # log文件的目录
#         # logfile_dir = "C://work//py_workspace//exturing_ai_test//log" # log文件的目录
#         # log_file_path = logfile_dir + log_file_path
#         log_file_path = os.path.dirname(os.path.abspath(__name__)) +  "../../../../../../log" + log_file_path
#         # 确保日志目录存在
#         log_dir = os.path.dirname(log_file_path)
#         if not os.path.exists(log_dir):
#             os.makedirs(log_dir)
#
#         # 设置日志格式
#         formatter = "{time:YYYY-MM-DD HH:mm:ss} | {process.name}:{thread.name} | {module}.{function}:{line} | {level} | {message}"
#
#         # 创建日志记录器
#         logger.add(log_file_path, format=formatter, level=level.upper(), rotation="00:00", retention="7 days", backtrace=True, catch=True)
#         # logger.add(lambda msg: print(msg), format=formatter, level=level.upper())
#         return logger
#
#     @staticmethod
#     def debug(message):
#         """
#         记录调试级别的日志
#         :param message: 日志消息
#         """
#         logger.debug(message)
#
#     @staticmethod
#     def info(message):
#         """
#         记录信息级别的日志
#         :param message: 日志消息
#         """
#         logger.info(message)
#
#     @staticmethod
#     def warning(message):
#         """
#         记录警告级别的日志
#         :param message: 日志消息
#         """
#         logger.warning(message)
#
#     @staticmethod
#     def error(message):
#         """
#         记录错误级别的日志
#         :param message: 日志消息
#         """
#         logger.error(message)
#
#     @staticmethod
#     def critical(message):
#         """
#         记录严重错误的日志
#         :param message: 日志消息
#         """
#         logger.critical(message)

# 示例用法
# if __name__ == "__main__":
#     LogTool.configure_logger('/et-app.log', 'DEBUG')
#     LogTool.info('这是一条信息日志')
#     LogTool.debug('这是一条调试日志')
#     LogTool.warning('这是一条警告日志')
#     LogTool.error('这是一条错误日志')
#     LogTool.critical('这是一条严重错误日志')
