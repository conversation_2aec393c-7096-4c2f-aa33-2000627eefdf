# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.test_config import TestConfigModel, TestConfigQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_config_service import create, query_page, update, delete

test_config = Blueprint('test_config', __name__)

# 新增评测通道
@test_config.route(f'/{URL_PREFIX}/test-config/create', methods=['POST'])
def test_config_create():
    et_log.info("############test_config_create################")
    req_data = request.get_json()
    test_config_instance = TestConfigModel(**req_data)
    test_config_id = create(test_config_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(test_config_id)).to_json()

# 评测通道分页查询
@test_config.route(f'/{URL_PREFIX}/test-config/page', methods=['POST'])
def test_config_page():
    et_log.info("############test_config_page################")
    data = request.get_json()
    query = TestConfigQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

# 修改评测通道
@test_config.route(f'/{URL_PREFIX}/test-config/update', methods=['POST'])
def test_config_update():
    et_log.info("############test_config_update################")
    req_data = request.get_json()
    id = req_data.get("test_config_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_config_id is null", "").to_json()

    test_config_instance = TestConfigModel(**req_data)
    update_result = update(id, test_config_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除评测通道
@test_config.route(f'/{URL_PREFIX}/test-config/del', methods=['POST'])
def test_config_delete():
    et_log.info("############test_config_delete################")
    req_data = request.get_json()
    id = req_data.get("test_config_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "test_config_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()