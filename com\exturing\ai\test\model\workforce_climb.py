from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class WorkforceClimbModel(BaseDataModel):
    month: Optional[str] = None  # 月份
    project_id: Optional[str] = None  # 项目ID
    role: Optional[str] = None  # 角色
    type: Optional[str] = None  # 类型 现有正式，现有外包，新增正式，新增外包
    base: Optional[str] = None  # base地
    requirement_id: Optional[str] = None  # 项目需求ID
    labor: Optional[float] = None  # 人力数量
    desc: Optional[str] = None  # 描述


class WorkforceClimbQueryModel(BaseModel):
    project_id: Optional[str] = None
    month: Optional[str] = None
    type: Optional[str] = None
    role: Optional[str] = None
    base: Optional[str] = None

    def get_query_condition(self):
        condition = {}
        if self.project_id:
            project_id_list = self.project_id.split(",")
            if len(project_id_list) > 1:
                condition["project_id"] = {"$in": project_id_list}
            else:
                condition["project_id"] = self.project_id

        if self.month:
            condition["month"] = self.month

        if self.type:
            type_list = self.type.split(",")
            if len(type_list) > 1:
                condition["type"] = {"$in": type_list}
            else:
                condition["type"] = self.type

        if self.role:
            role_list = self.role.split(",")
            if len(role_list) > 1:
                condition["role"] = {"$in": role_list}
            else:
                condition["role"] = self.role

        if self.base:
            base_list = self.base.split(",")
            if len(base_list) > 1:
                condition["base"] = {"$in": base_list}
            else:
                condition["base"] = self.base

        return condition
