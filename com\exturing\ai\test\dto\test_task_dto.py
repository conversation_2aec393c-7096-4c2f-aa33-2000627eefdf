from typing import Optional, List
from bson import ObjectId
from pydantic import BaseModel

from com.exturing.ai.test.dto.params.auto_dataset_dto import AutoDatasetDto
from com.exturing.ai.test.dto.params.base_dto import BaseDto
from com.exturing.ai.test.dto.task_execution_frequency_dto import (
    TaskExecutionFrequencyDto,
)


class TestTaskDto(BaseDto):
    task_id: Optional[str] = ""
    plan_id: Optional[str] = ""
    data_set_type: Optional[str] = ""
    data_set_id: Optional[str] = ""
    task_name: Optional[str] = ""
    env_id: Optional[str] = ""
    config_id: Optional[str] = ""
    task_type: Optional[int] = None
    auto_run: Optional[bool] = False
    data_set_builds: List[Optional[AutoDatasetDto]] = None
    execute_frequency: List[Optional[TaskExecutionFrequencyDto]] = None

    eval_type: Optional[int] = None  # 评测类型 默认0=单通道 1=多通道
    eval_multi: Optional[str] = "" # 评测模型，多通道模型id之间“,”分割

    answer_pt_code: Optional[str] = ""  # 问题回答提示词模板代码
    eval_pt_code: Optional[str] = ""    # 问题回答评分提示词模板代码

    def get_auto_run_json(self):
        if not self.execute_frequency or len(self.execute_frequency) == 0:
            return []
        cycles = []
        for data in self.execute_frequency:
            cycle_item = {"cycle": data.cycle}
            if data.cycle_value and data.cycle_value > 0:
                cycle_item["cycle_value"] = data.cycle_value
            if data.time_value and len(data.time_value) > 0:
                cycle_item["time_value"] = data.time_value
            if data.custom_times and len(data.custom_times) > 0:
                cycle_item["custom_times"] = data.custom_times
            cycles.append(cycle_item)
        return cycles

    def get_dataset_builds_json(self):
        if not self.data_set_builds or len(self.data_set_builds) == 0:
            return []
        return [
            {
                "model_id": data.model_id,
                "dimension_id": data.dimension_id if data.dimension_id else "",
                "tags": data.tags if data.tags else "",
                "size": data.size if data.size else 0,
            }
            for data in self.data_set_builds
        ]


class TestTaskQueryDto(BaseModel):
    plan_id: Optional[str] = None
    task_name: Optional[str] = None
    task_status: Optional[int] = None
    auto_run: Optional[bool] = None
    create_from_time: Optional[str] = None
    create_to_time: Optional[str] = None
    has_run_count: Optional[bool] = True

    def get_query_condition(self):
        condition = {"is_del": 0}

        if self.task_name:
            condition["task_name"] = {
                "$regex": str(self.task_name).strip(),
                "$options": "i",
            }

        if self.plan_id:
            condition["plan_id"] = ObjectId(self.plan_id)

        if self.task_status is not None:
            condition["task_status"] = self.task_status

        if self.auto_run is not None:
            condition["auto_run"] = self.auto_run

        create_time_filter = {}
        if self.create_from_time:
            create_time_filter["$gte"] = self.create_from_time
        if self.create_to_time:
            create_time_filter["$lte"] = self.create_to_time

        if not create_time_filter and len(create_time_filter) > 0:
            condition["create_time"] = create_time_filter

        return condition
