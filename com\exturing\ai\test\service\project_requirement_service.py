# service.py
from com.exturing.ai.test.comm.comm_util import (
    bool_format,
    filter_tree_by_keyword,
    list_to_tree,
    str_format,
)
from com.exturing.ai.test.model.project_requirement import (
    ProjectRequirementModel,
    ProjectRequirementQueryModel,
)
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from werkzeug.datastructures import FileStorage
from com.exturing.ai.test.comm.log_tool import et_log
import pandas as pd

_doc = "project_requirement"


# 新增项目需求
def create(data: ProjectRequirementModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create project_requirement: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询项目需求
def query_page(
    page_num: int = 1, page_size: int = 10, query: ProjectRequirementQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 查询项目需求树
def query_tree(query: ProjectRequirementQueryModel = None) -> list:
    condition = query.get_query_tree_condition()
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    json_tree = list_to_tree(json_list)

    if query.name:
        json_tree = filter_tree_by_keyword(json_tree, query.name)

    return json_tree


# 修改项目需求
def update(id: str, data: ProjectRequirementModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update project_requirement: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除项目需求
def delete(id: str) -> bool:
    et_log.info(f"delete project_requirement by id:{id}")
    assert not query_children_by_id(id), "请先删除子需求"

    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 根据ID查询子需求列表
def query_children_by_id(id: str) -> list:
    et_log.info(f"query_children_by_id project_requirement by id:{id}")
    condition = {"parent_id": id}
    result = MongoDBUtil.find_condition(_doc, condition)
    result_list = list(result or [])
    json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]

    return json_list


# 导入项目需求数据
def import_data(project_id, file: FileStorage):
    all_sheets = pd.read_excel(file, sheet_name=None)

    for sheet_name in all_sheets:
        df = all_sheets[sheet_name]
        list = df.to_dict(orient="records")

        level_map = {}

        def data_addition(data, item):
            data["content"] = str_format("功能描述", item)
            data["priority"] = str_format("需求优先级", item)
            data["type"] = str_format("分类", item)
            data["contractor"] = str_format("承接方", item)
            data["is_main"] = bool_format("是否主线", item)

        def insert_level(item, level=1, id_map={}, parent_id_map={}):
            level_key = f"Level_{level}"
            level_name = str_format(level_key, item)

            level_key_next = f"Level_{level + 1}"
            level_name_next = str_format(level_key_next, item)

            level_key_last = f"Level_{level - 1}"
            level_name_last = str_format(level_key_last, item)

            if level_name:
                if not id_map.get(level_name):
                    insert_item = {
                        "project_id": project_id,
                        "name": level_name,
                    }

                    if parent_id_map.get(level_name_last):
                        parent_id = parent_id_map.get(level_name_last).get("id")
                        insert_item["parent_id"] = parent_id

                    if not level_name_next:
                        data_addition(insert_item, item)

                    insert_id = create(ProjectRequirementModel(**insert_item))

                    return insert_id

        for item in list:
            level_1_name = str_format("Level_1", item)
            level_2_name = str_format("Level_2", item)

            insert_id_1 = insert_level(item, 1, level_map)
            if insert_id_1:
                level_map[level_1_name] = {
                    "id": str(insert_id_1),
                    "children": {},
                }

            level_map_2 = level_map.get(level_1_name, {}).get("children")
            insert_id_2 = insert_level(item, 2, level_map_2, level_map)

            if insert_id_2:
                level_map_2[level_2_name] = {
                    "id": str(insert_id_2),
                    "children": {},
                }

            insert_level(item, 3, {}, level_map_2)

    return True
