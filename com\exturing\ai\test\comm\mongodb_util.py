import traceback
from datetime import datetime

import pymongo
from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import MONGODB_URL, CTX_OEM_CODES, CTX_USER_ID, MONGODB_DB
from com.exturing.ai.test.comm.log_tool import et_log

mongo_client = pymongo.MongoClient(MONGODB_URL, authMechanism="SCRAM-SHA-256")
# mongo_client = pymongo.MongoClient("mongodb://localhost:27017/")


class MongoDBUtil:

    mdb = mongo_client[MONGODB_DB]
    # mdb = mongo_client['et-ai-test']
    del_pro = {"is_del": 1, "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

    @classmethod
    def insert_one(cls, collection, json):
        et_log.info(f"insert_one collection:{collection} data:{json}")
        if json is not None and "_id" in json:
            del json["_id"]
        now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ctx_user = CTX_USER_ID.get()
        json["create_by"] = ctx_user if ctx_user > 0 else json.get("create_by", 0)
        json["create_time"] = now_time
        json["update_by"] = ctx_user if ctx_user > 0 else json.get("update_by", 0)
        json["update_time"] = now_time
        json["is_del"] = 0
        # 当前用户oem_codes数据权限
        json["oem_codes"] = CTX_OEM_CODES.get()
        et_log.info(f"insert_one collection:{collection} insert_data:{json}")
        inserted_id = cls.mdb[collection].insert_one(json).inserted_id
        et_log.info(f"collection:{collection} insert_one success, inserted_id:{inserted_id}")
        return inserted_id

    @classmethod
    def insert_many(cls, collection, json):
        et_log.info(f"insert_many collection:{collection} data:{json}")
        now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ctx_user = CTX_USER_ID.get()
        for item in json:
            if item is not None and "_id" in item:
                del item["_id"]
            item["create_by"] = ctx_user
            item["create_time"] = now_time
            item["update_by"] = ctx_user
            item["update_time"] = now_time
            item["oem_codes"] = CTX_OEM_CODES.get()
            item["is_del"] = 0
        inserted_ids = cls.mdb[collection].insert_many(json).inserted_ids
        et_log.info(f"collection:{collection} insert_many success, inserted_ids:{inserted_ids}")
        return inserted_ids

    @classmethod
    def delete_by_id(cls, collection, _id, do_user):
        et_log.info(f"delete_by_id collection:{collection} _id:{_id} do_user:{do_user}")
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        update = {"is_del": 1, "update_time": current_time, "update_by": CTX_USER_ID.get()}
        condition = {"_id":ObjectId(_id)}
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        result = cls.mdb[collection].update_one(condition, {"$set": update})
        et_log.info(f"collection:{collection} delete_by_id success, deleted_count:{result.modified_count}")
        return result.modified_count

    @classmethod
    def delete_by_many(cls, collection, del_filter, do_user = 0):
        et_log.info(f"delete_by_many collection:{collection} del_filter:{del_filter}")
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        update = {"is_del": 1, "update_time": current_time, "update_by": CTX_USER_ID.get()}
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        del_filter["oem_codes"] = match_oem_codes
        result = cls.mdb[collection].update_many(del_filter, {"$set": update})
        et_log.info(f"collection:{collection} delete_by_many success, deleted_count:{result.modified_count}")
        return result.modified_count

    # 用时请验证
    @classmethod
    def update_one(cls, collection, condition, set_data):
        et_log.info(f"update_one collection:{collection} condition:{condition} set_data:{set_data}")
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        set_data["update_by"] = CTX_USER_ID.get()
        if set_data is not None and "_id" in set_data:
            del set_data["_id"]
        if set_data is not None and "id" in set_data:
            del set_data["id"]
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        set_data["update_time"] = current_time
        result  = cls.mdb[collection].update_one(condition, {"$set": set_data})
        et_log.info(f"collection:{collection} update_one success, match_count:{result.matched_count} update_count:{result.modified_count}")
        return result

    # 用时请验证
    @classmethod
    def find_one_and_update(cls, collection, _id, set_data):
        et_log.info(f"find_one_and_update collection:{collection} _id:{_id} set_data:{set_data}")
        condition = {"_id": ObjectId(_id)}
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        if set_data is not None and "_id" in set_data:
            del set_data["_id"]
        if set_data is not None and "id" in set_data:
            del set_data["id"]
        set_data["update_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result  = cls.mdb[collection].find_one_and_update(condition, {"$set": set_data})
        et_log.info(f"collection:{collection} update_one success")
        return result

    # 用时请验证
    @classmethod
    def update_by_id(cls, collection, _id, set_data):
        et_log.info(f"update_by_id collection:{collection} _id:{_id} set_data:{set_data}")
        condition = {"_id": ObjectId(_id)}
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        set_data["update_by"] = CTX_USER_ID.get()
        if set_data is not None and "_id" in set_data:
            del set_data["_id"]
        if set_data is not None and "id" in set_data:
            del set_data["id"]
        set_data["update_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result  = cls.mdb[collection].update_one(condition, {"$set": set_data})
        et_log.info(f"collection:{collection} update_by_id success, match_count:{result.matched_count} update_count:{result.modified_count}")
        return result

    @classmethod
    def update_many(cls, collection, condition, set_data):
        et_log.info(f"update_many collection:{collection} condition:{condition} set_data:{set_data}")
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        set_data["update_by"] = CTX_USER_ID.get()
        set_data["update_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result  = cls.mdb[collection].update_many(condition, {"$set": set_data})
        et_log.info(f"collection:{collection} update_many success, match_count:{result.matched_count} update_count:{result.modified_count}")
        return result

    @classmethod
    def find_one(cls, collection, condition):
        et_log.info(f"find_one collection:{collection} condition:{condition}")
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        result  = cls.mdb[collection].find_one(condition)
        et_log.info(f"collection:{collection} find_one success, result:{result}")
        return result

    @classmethod
    def find_by_id(cls, collection, _id):
        et_log.info(f"find_by_id collection:{collection} _id:{_id}")
        condition = {"_id": ObjectId(_id), "is_del": 0}
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        result  = cls.mdb[collection].find_one(condition)
        et_log.info(f"collection:{collection} find_by_id success, result:{result}")
        return result

    @classmethod
    def find_condition(cls, collection, condition):
        condition["is_del"] = 0
        et_log.info(f"find_condition collection:{collection} condition:{condition}")
        # 排序
        sorts = [("create_time", pymongo.DESCENDING), ("_id", pymongo.DESCENDING)]
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        result = cls.mdb[collection].find(condition).sort(sorts).limit(100000)
        if result is None or cls.mdb[collection].count_documents(condition) < 1:
            return None
        et_log.info(f"collection:{collection} find_condition success, result:{result}")
        return result

    @classmethod
    def find_condition_page(cls, collection, condition, sorts, skip, limit):
        condition["is_del"] = 0
        et_log.info(f"find_condition_page collection:{collection} sorts:{sorts} skip:{skip} limit:{limit} condition:{condition}")
        # 排序
        if sorts is None or len(sorts) == 0:
            sorts = [("create_time", pymongo.DESCENDING), ("_id", pymongo.DESCENDING)]
        # 取数量
        if limit is None:
            limit = -1
        # 偏移（忽略行数据）
        if skip is None:
            skip = 0
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        result = cls.mdb[collection].find(condition).sort(sorts).limit(limit).skip(skip)
        if result is None or cls.mdb[collection].count_documents(condition) < 1:
            return None
        et_log.info(f"collection:{collection} find_condition_page success, result:{result}")
        return result

    @classmethod
    def find_count(cls, collection, condition):
        try:
            condition["is_del"] = 0
            # 数据权限过滤
            match_oem_codes = cls.oem_codes_condition()
            condition["oem_codes"] = match_oem_codes
            et_log.info(f"find_count collection:{collection} condition:{condition}")
            result  = cls.mdb[collection].count_documents(condition)
            et_log.info(f"collection:{collection} find_count success, count:{result}")
            return result
        except TypeError as e:
            et_log.error(f"find_count TypeError:{e}, {traceback.print_exc()}")
            return 0
        except AttributeError as e:
            et_log.error(f"find_count AttributeError:{e}, {traceback.print_exc()}")
            return 0
        except Exception as e:
            et_log.error(f"find_count exception:{e}, {traceback.print_exc()}")
            return 0

    @classmethod
    def update_one_pro(cls, collection, entity_json, not_del_keys:list[str] = []):
        et_log.info(f"update_one_pro collection:{collection} entity_json:{entity_json}")
        if entity_json is None:
            et_log.error(f"update_one_pro error, entity_json is null")
            return None
        if "_id" not in entity_json:
            et_log.error(f"update_one_pro error, entity_json not has _id")
            return None

        keys_to_delete = set()
        _id = entity_json["_id"]
        keys_to_delete.add("_id")
        for key in entity_json.keys():
            value = entity_json[key]
            if value is None: # 去除实体json中的空值内容
                # del update_json[key]
                keys_to_delete.add(key)
                continue
            if str == type(value) and len(str(value).strip()) == 0:
                # del update_json[key]
                keys_to_delete.add(key)
                continue

        for key in keys_to_delete:
            if key in not_del_keys:
                continue
            del entity_json[key]

        entity_json["update_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        entity_json["update_by"] = CTX_USER_ID.get()
        result  = cls.mdb[collection].update_one({"_id": ObjectId(_id)}, {"$set": entity_json})
        et_log.info(f"collection:{collection} update_one_pro success, match_count:{result.matched_count} update_count:{result.modified_count}")
        return result

    @classmethod
    def aggregate(cls, collection, pipeline):
        et_log.info(f"aggregate collection:{collection} pipeline:{pipeline}")
        return cls.mdb[collection].aggregate(pipeline)
    
    @classmethod
    def serialize_document(cls, doc):
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)  # 将 ObjectId 转换为字符串
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()  # 将 datetime 转换为 ISO 格式字符串
        return doc

    @classmethod
    def oem_codes_condition(cls):
        ctx_oem_codes = CTX_OEM_CODES.get()
        if not ctx_oem_codes or len(ctx_oem_codes) == 0:
            return None
        match_arr = ctx_oem_codes.split(",")
        if not match_arr or len(match_arr) == 0:
            return None
        return {
            "$regex": "|".join(match_arr), "$options": "i"  # 忽略大小写
        }

    @classmethod
    def find_all_by_set_id(cls, collection, condition):
        et_log.info(f"find_all_by_set_id collection:{collection} condition:{condition}")
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        # 使用 find() 方法查询符合条件的所有数据项
        result = cls.mdb[collection].find(condition)

        # 将查询结果转为列表并返回
        result_list = list(result)

        et_log.info(f"collection:{collection} find_all_by_set_id success, result count:{len(result_list)}")
        return result_list


    @classmethod
    def find_all_by_task_result_id(cls, collection, condition):
        et_log.info(f"find_all_by_task_result_id  collection:{collection} condition:{condition}")
        # 数据权限过滤
        match_oem_codes = cls.oem_codes_condition()
        condition["oem_codes"] = match_oem_codes
        # 使用 find() 方法查询符合条件的所有数据项
        result = cls.mdb[collection].find(condition)

        # 将查询结果转为列表并返回
        result_list = list(result)

        et_log.info(f"collection:{collection} find_all_by_task_result_id success, result count:{len(result_list)}")
        return result_list


# MongoDBUtil.insert_many("test", [{"name": "王五", "age":21, "tel":"13457777777", "sex": "女"}])
# print(MongoDBUtil.find_by_id("dic_info", "6780cfe226cabf8468795e33"))
# MongoDBUtil.find_condition("dic_info", {"pid":ObjectId("df71f839588e9bd88e8be59d")}, None, None, None, None)

# print(MongoDBUtil.delete_by_id("dic_info", ObjectId("6780baf6b3443bc00dcef71b")))
# print(MongoDBUtil.delete_by_many("dic_info", {"_id":ObjectId("6780baf6b3443bc00dcef71b")}))
# print(MongoDBUtil.find_by_id("et_data_set", "6780cfe226cabf8468795e33"))
# print(MongoDBUtil.oem_codes_condition())

