# api.py

from flask import Blueprint, request
import pandas as pd
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel, OriginDataItemQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.origin_data_item_service import (
    create,
    query_page,
    query_children,
    update,
    delete,
    delete_batch,
    import_data,
    export_data,
    update_batch,
)


origin_data_item = Blueprint('origin_data_item', __name__)

# 新增OriginDataItem
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/create', methods=['POST'])
def origin_data_item_create():
    et_log.info("############origin_data_item_create################")
    req_data = request.get_json()
    item_instance = OriginDataItemModel(**req_data)
    item_id = create(item_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(item_id)).to_json()

# OriginDataItem分页查询
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/page', methods=['POST'])
def origin_data_item_page():
    et_log.info("############origin_data_item_page################")
    data = request.get_json()
    query = OriginDataItemQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()).to_json()

@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/children', methods=['POST'])
def origin_data_item_children():
    et_log.info("############origin_data_item_children################")
    data = request.get_json()
    parent_id = data.get("origin_data_item_id")

    list = query_children(parent_id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, list).to_json()

# 修改OriginDataItem
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/update', methods=['POST'])
def origin_data_item_update():
    et_log.info("############origin_data_item_update################")
    req_data = request.get_json()
    id = req_data.get("origin_data_item_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "origin_data_item_id is null", "").to_json()

    item_instance = OriginDataItemModel(**req_data)
    update_result = update(id, item_instance)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 批量修改原始语料数据
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/update-batch', methods=['POST'])
def origin_data_item_batch_update():
    et_log.info("############origin_data_item_batch_update################")
    req_data = request.get_json()
    ids = req_data.get("origin_data_item_ids")

    if ids is None or len(ids) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "origin_data_item_ids is null", "").to_json()

    update_result = update_batch(ids, req_data)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)).to_json()

# 删除OriginDataItem
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/del', methods=['POST'])
def origin_data_item_delete():
    et_log.info("############origin_data_item_delete################")
    req_data = request.get_json()
    id = req_data.get("origin_data_item_id")

    if id is None or len(id) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "origin_data_item_id is null", "").to_json()
    
    delete_result = delete(id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()

# 批量删除OriginDataItem
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/del-batch', methods=['POST'])
def origin_data_item_batch_delete():
    et_log.info("############origin_data_item_batch_delete################")
    req_data = request.get_json()
    ids = req_data.get("origin_data_item_ids")

    if ids is None or len(ids) < 1:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "origin_data_item_ids is null", "").to_json()

    delete_result = delete_batch(ids)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result).to_json()

# 导入原始数据
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/import', methods=['POST'])
def origin_data_item_import():
    et_log.info("############origin_data_item_import################")
    
    if('excel_file' in request.files):
        uploaded_file = request.files['excel_file']

        if uploaded_file.filename.endswith('.xlsx') or uploaded_file.filename.endswith('.xls'):
            result = import_data(uploaded_file)

            if result:
                return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, result).to_json()
            else:
                return ApiResult(ResultCode.FAILURE.code, ResultCode.FAILURE.msg, "导入失败").to_json()
        else:
            return ApiResult(ResultCode.PARAM_IS_INVALID.code, "excel_file is not xlsx or xls", "").to_json()
    else:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, "excel_file is null", "").to_json()


# 导出原始数据
@origin_data_item.route(f'/{URL_PREFIX}/origin-data-item/export', methods=['POST'])
def origin_data_item_export():
    et_log.info("############origin_data_item_export################")
    data = request.get_json()
    export_keys = data.get("export_keys", [])
    query = OriginDataItemQueryModel(**data)

    excel_binary = export_data(query, export_keys)

    return excel_binary
