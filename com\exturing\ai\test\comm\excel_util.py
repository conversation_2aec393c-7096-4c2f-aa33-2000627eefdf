import math
import traceback
from datetime import datetime

import pandas as pd
from bson import ObjectId

from com.exturing.ai.test.comm.comm_constant import DIC_CODE_MODEL, DIC_CODE_DIMENSION, CTX_OEM_CODES
from com.exturing.ai.test.comm.date_util import DateUtil
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.model.data_set_item import EtDataSetItem
from com.exturing.ai.test.model.dic_info import DicInfo
from com.exturing.ai.test.model.et_data_set import EtDataSet
from com.exturing.ai.test.model.origin_data_item import OriginDataItemModel
from com.exturing.ai.test.model.test_plan import TestPlan
from com.exturing.ai.test.model.test_result import TestResult
from com.exturing.ai.test.model.test_task import TestTask
from com.exturing.ai.test.model.test_task_result import TestTaskResult
from com.exturing.ai.test.service.data_item_service import DataItemService
from com.exturing.ai.test.service.origin_data_item_service import create, check_question


class ExcelUtil:

    # def_user = 1  # 默认用户id

    @classmethod
    def dataset_import(cls, file):
        try:
            excel_file = pd.ExcelFile(file)
            # 获取 Excel 文件中的所有 sheet 名称
            sheet_names = excel_file.sheet_names
            # 遍历每个sheet名称
            for sheet_name in sheet_names:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ctx_oem_codes = CTX_OEM_CODES.get()
                # 根据sheet创建数据集
                data_set_id = EtDataSet("", current_time, None, current_time, None, 0,
                                        ctx_oem_codes, sheet_name, "", "", "v0.0.1", "",
                                        1, 0).insert_one()

                # 读取每个 sheet 到 DataFrame
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                # 遍历 DataFrame 的每一行和每一列
                index: int
                for index, row in df.iterrows():
                    data_json = {}
                    for col in df.columns:
                        cell_value = row[col]
                        data_json[col] = cell_value
                        et_log.info(f"Sheet: {sheet_name}, Row: {index+1}, Column: {col}, Value: {cell_value}")

                    # 根据行数据创建数据集数据项，依赖外层数据集id（可多层）
                    # 根据行数据创建测试过程记录，依赖测试计划id、测试数据集id（可多层）
                    cls.dataset_item_import(data_set_id, data_json, current_time)
                # 更新数据集 数量
                count = EtDataSetItem.find_count_set_id(data_set_id)
                EtDataSet.update_set_total(data_set_id, count)
        except Exception as e:
            et_log.error(f"dataset_import exception:{e}")
            traceback.print_exc()

    # 按行处理导入excel导入数据,处理数据集中的数据项，一行数据的处理数据集数据项导入处理
    @classmethod
    def dataset_item_import(cls, set_id, row_json, current_time):
        et_log.info(f"dataset_item_import params set_id:{set_id} row_json:{row_json}")
        try:
            item_id = ""
            child_flag = False
            origin_pid = ""
            ctx_oem_codes = CTX_OEM_CODES.get()
            for i in range(100):# 处理行记录迭代100轮的case如果发现中间有跳号，则不处理
                index = i+1
                if f"Q{index}" not in row_json:
                    break
                question = str(row_json[f"Q{index}"])
                if not question or len(question) == 0 or "nan" == question:
                    break
                is_last = 0 if f"Q{index+1}" in row_json and len(str(row_json[f"Q{index+1}"])) > 0 and "nan" != str(row_json[f"Q{index+1}"]) else 1
                # 数据集数据项处理
                model_name = cls.get_str_json_attr(f"Q{index}_model", row_json)
                model_id = cls.get_dic_id(model_name, DIC_CODE_MODEL)
                dimension_name = cls.get_str_json_attr(f"Q{index}_dimension", row_json)
                dimension_id = cls.get_dic_id(dimension_name, DIC_CODE_DIMENSION)
                if len(str(model_id)) == 0 and len(str(dimension_id)) > 0:
                    dimension_dic = DicInfo.find_by_pk(str(dimension_id))
                    if dimension_dic is not None and len(str(dimension_dic.get("pid", ""))) > 0:
                        model_id = ObjectId(str(dimension_dic.get("pid", "")))
                excepted_answer = cls.get_str_json_attr(f"A{index}_expected", row_json)
                excepted_task = cls.get_str_json_attr(f"Q{index}_task_expected", row_json)
                excepted_category = cls.get_str_json_attr(f"Q{index}_category_expected", row_json)
                qa_keywords = cls.get_str_json_attr(f"Q{index}_keyword", row_json)
                real_time = cls.get_str_json_attr(f"Q{index}_real_time", row_json)
                if real_time and 0 < len(real_time) < 19:
                    real_time = DateUtil.date_format_convert(real_time)
                data_src = cls.get_str_json_attr("data_src", row_json)

                # 检查语料重复性 同数据集(仅检查最终节点的语料重复性)
                if 1 == is_last:
                    bol_val = DataItemService.check_question(str(set_id), str(model_id), str(dimension_id), question)
                    if bol_val:
                        et_log.error(f"set_id:{set_id} question:{question} has repeat")
                        break
                eds_item = EtDataSetItem("", current_time, None, current_time, None, 0,
                                         ctx_oem_codes, item_id, set_id, "", model_id, dimension_id,
                                         0, "", question, excepted_answer, excepted_task,
                                         excepted_category, "", is_last, real_time, qa_keywords, data_src)
                item_id = eds_item.insert_one()

                re_url_q = cls.get_str_json_attr(f"Q{index}_url", row_json)
                re_cmd_q = cls.get_str_json_attr(f"Q{index}_cmd", row_json)
                re_error_id_q = cls.get_str_json_attr(f"Q{index}_errorId", row_json)
                # 原始语料导入处理
                if len(str(eds_item.parent_id)) > 0 and child_flag:# 子节点如果父节点补充了原始语料，则子节点直接插原始语料记录，不用考虑重复性
                    origin_pid = cls.insert_origin_set_item(current_time, origin_pid, model_id, dimension_id, question, excepted_answer,
                                               excepted_task, excepted_category, re_url_q, re_cmd_q, re_error_id_q, is_last,
                                               qa_keywords, data_src, real_time)
                elif len(str(eds_item.parent_id)) == 0:
                    origin_count = check_question(question)
                    if origin_count == 0:
                        origin_pid = cls.insert_origin_set_item(current_time, origin_pid, model_id, dimension_id, question, excepted_answer,
                                                   excepted_task, excepted_category, re_url_q, re_cmd_q, re_error_id_q, is_last,
                                                   qa_keywords, data_src, real_time)
                        child_flag = True
        except Exception as e:
            et_log.error(f"dataset_item_import exception:{e}")
            traceback.print_exc()

    @classmethod
    # 测试结果excel导入处理,请勿出现空行
    def test_result_import(cls, file):
        try:
            ctx_oem_codes = CTX_OEM_CODES.get()
            excel_file = pd.ExcelFile(file)
            # 获取 Excel 文件中的所有 sheet 名称
            sheet_names = excel_file.sheet_names
            # 遍历每个sheet名称
            for sheet_name in sheet_names:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 根据sheet创建数据集
                data_set_id = EtDataSet("", current_time, None, current_time, None, 0,
                                        ctx_oem_codes, sheet_name, "", "", "v0.0.1", "",
                                        1, 0).insert_one()
                # 根据sheet创建测试计划，依赖项目id
                plan_id = TestPlan("", current_time, None, current_time, None, 0,
                                   ctx_oem_codes, "", sheet_name + "-测试计划", 1, current_time,
                                   "", sheet_name).insert_one()
                # 根据sheet创建测试任务，依赖测试计划id
                task_id = TestTask("", current_time, None, current_time, None, 0,
                                   ctx_oem_codes, plan_id, "",
                                   data_set_id, sheet_name + "-测试任务", 1, 0, "").insert_one()
                # 根据测试任务，创建测试结果记录，依赖测试任务id
                task_result_id = TestTaskResult("", current_time, None, current_time, None, 0,
                                                ctx_oem_codes, task_id,
                                                "", "", 0.00, 0.00, 0.00,
                                                0.00, 0.00, 0.00, 0.00,
                                                1, 0.000000000, 0.00, 0,"", data_set_id).insert_one()

                # 读取每个 sheet 到 DataFrame
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                # 遍历 DataFrame 的每一行和每一列
                index: int
                for index, row in df.iterrows():
                    data_json = {}
                    for col in df.columns:
                        cell_value = row[col]
                        data_json[col] = cell_value
                        et_log.info(f"Sheet: {sheet_name}, Row: {index+1}, Column: {col}, Value: {cell_value}")

                    # 根据行数据创建数据集数据项，依赖外层数据集id（可多层）
                    # 根据行数据创建测试过程记录，依赖测试计划id、测试数据集id（可多层）
                    cls.handle_import_excel_row(data_set_id, task_id, task_result_id, data_json, current_time)
                # 更新数据集 数量
                count = EtDataSetItem.find_count_set_id(data_set_id)
                EtDataSet.update_set_total(data_set_id, count)
        except Exception as e:
            et_log.error(f"test_result_import exception:{e}")
            traceback.print_exc()

    # 按行处理导入excel导入数据,处理数据集中的数据项，一行数据的处理数据集数据项导入处理
    @classmethod
    def handle_import_excel_row(cls, set_id, task_id, task_result_id, row_json, current_time):
        et_log.info(f"handle_import_excel_row params set_id:{set_id} task_id:{task_id} task_result_id:{task_result_id} row_json:{row_json}")
        try:
            item_id = ""
            result_id = ""
            child_flag = False
            origin_pid = ""
            ctx_oem_codes = CTX_OEM_CODES.get()
            for i in range(100):# 处理行记录迭代100轮的case如果发现中间有跳号，则不处理
                index = i+1
                if f"Q{index}" not in row_json:
                    break

                question = str(row_json[f"Q{index}"])
                if not question or len(question) == 0 or "nan" == question:
                    break
                is_last = 0 if f"Q{index+1}" in row_json and len(str(row_json[f"Q{index+1}"])) > 0 and "nan" != str(row_json[f"Q{index+1}"]) else 1
                # 数据集数据项处理
                model_name = cls.get_str_json_attr(f"Q{index}_model", row_json)
                model_id = cls.get_dic_id(model_name, DIC_CODE_MODEL)
                dimension_name = cls.get_str_json_attr(f"Q{index}_dimension", row_json)
                dimension_id = cls.get_dic_id(dimension_name, DIC_CODE_DIMENSION)
                if len(str(model_id)) == 0 and len(str(dimension_id)) > 0:
                    dimension_dic = DicInfo.find_by_pk(str(dimension_id))
                    if dimension_dic is not None and len(str(dimension_dic.get("pid", ""))) > 0:
                        model_id = ObjectId(str(dimension_dic.get("pid", "")))
                excepted_answer = cls.get_str_json_attr(f"A{index}_expected", row_json)
                excepted_task = cls.get_str_json_attr(f"Q{index}_task_expected", row_json)
                excepted_category = cls.get_str_json_attr(f"Q{index}_category_expected", row_json)
                qa_keywords = cls.get_str_json_attr(f"Q{index}_keyword", row_json)
                real_time = cls.get_str_json_attr(f"Q{index}_real_time", row_json)
                if real_time and 0 < len(real_time) < 19:
                    real_time = DateUtil.date_format_convert(real_time)
                data_src = cls.get_str_json_attr(f"Q{index}_data_src", row_json)

                # 检查语料重复性 同数据集(仅检查最终节点的语料重复性)
                if 1 == is_last:
                    bol_val = DataItemService.check_question(str(set_id), str(model_id), str(dimension_id), question)
                    if bol_val:
                        # 向上清除父节点
                        EtDataSetItem.up_del_by_pk(item_id)
                        et_log.error(f"set_id:{set_id} question:{question} has repeat")
                        break
                eds_item = EtDataSetItem("", current_time, None, current_time, None, 0,
                                         ctx_oem_codes, item_id, set_id,
                                         "", model_id, dimension_id, 0, "", question,
                                          excepted_answer, excepted_task, excepted_category, "",
                                          is_last, real_time, qa_keywords, data_src)
                item_id = eds_item.insert_one()

                re_url_q = cls.get_str_json_attr(f"Q{index}_url", row_json)
                re_cmd_q = cls.get_str_json_attr(f"Q{index}_cmd", row_json)
                re_error_id_q = cls.get_str_json_attr(f"Q{index}_errorId", row_json)

                if len(str(eds_item.parent_id)) > 0 and child_flag:# 子节点如果父节点补充了原始语料，则子节点直接插原始语料记录，不用考虑重复性
                    origin_pid = cls.insert_origin_set_item(current_time, origin_pid, model_id, dimension_id, question, excepted_answer,
                                               excepted_task, excepted_category, re_url_q, re_cmd_q, re_error_id_q, is_last,
                                               qa_keywords, data_src, real_time)
                elif len(str(eds_item.parent_id)) == 0:
                    origin_count = check_question(question)
                    if origin_count == 0:
                        origin_pid = cls.insert_origin_set_item(current_time, origin_pid, model_id, dimension_id, question, excepted_answer,
                                                   excepted_task, excepted_category, re_url_q, re_cmd_q, re_error_id_q, is_last,
                                                   qa_keywords, data_src, real_time)
                        child_flag = True

                # 测试结果处理（对应数据集数据项）
                actual_task = cls.get_str_json_attr(f"Q{index}_task_actual", row_json)
                actual_category = cls.get_str_json_attr(f"Q{index}_category_actual", row_json)
                result_category = cls.get_col_result(f"Q{index}_result", row_json)
                actual_answer = cls.get_str_json_attr(f"A{index}_actual", row_json)
                result_answer = cls.get_col_result(f"A{index}_result", row_json)
                result_final = cls.get_col_result(f"QA{index}_final_result", row_json)
                recall_id = cls.get_str_json_attr(f"A{index}_recall_id", row_json)

                # 同时获取A前缀和Q前缀的字段
                re_url = cls.get_str_json_attr(f"A{index}_url", row_json)
                re_cmd = cls.get_str_json_attr(f"A{index}_cmd", row_json)
                re_error_id = cls.get_str_json_attr(f"A{index}_errorId", row_json)

                re_interval_time = cls.get_col_number(f"A{index}_interval_time", row_json, -99)
                ws_key = f"A{index}_websearch"
                is_websearch = 0
                if ws_key in row_json and row_json[ws_key] is not None and str(row_json[ws_key]) == "是":
                    is_websearch = 1
                if index==1:
                    oem = cls.get_str_json_attr("oem", row_json)
                else:
                    oem = ""
                qa_use_time = float(row_json[f"QA{index}_consume_time"]) if (f"QA{index}_consume_time" in row_json
                                                                             and not math.isnan(float(row_json[f"QA{index}_consume_time"]))) else 0
                #if len(actual_task) > 0 or len(actual_category) > 0 or len(actual_answer) > 0:
                result_id = TestResult("", current_time, None, current_time, None, 0,
                                       ctx_oem_codes, result_id, task_id,
                                       item_id, question, actual_task, actual_category, result_category, actual_answer,
                                       result_answer, 0, 0, 0, qa_use_time, result_final,
                                       result_category, result_answer, result_final, is_last, excepted_task,
                                       excepted_category, excepted_answer, recall_id, task_result_id, model_id, dimension_id,
                                       "", re_url, re_cmd, re_error_id, re_interval_time, re_url_q, re_cmd_q, re_error_id_q,
                                       is_websearch, 0, 0,oem).insert_one()
        except Exception as e:
            et_log.error(f"test_result_import exception:{e}")
            traceback.print_exc()

    # 补充原始语料记录
    @classmethod
    def insert_origin_set_item(cls, current_time, parent_id, model_id, dimension_id, question, excepted_answer, expected_task,
                               expected_category, expected_url, expected_cmd, expected_error_id, is_last, qa_keywords, data_src="",
                               real_time=""):

        ctx_oem_codes = CTX_OEM_CODES.get()
        origin_dic = {
            "create_time": current_time,
            "create_by": None,
            "update_time": current_time,
            "update_by": None,
            "is_del": 0,
            "parent_id": str(parent_id) if parent_id else "",
            "model_id": str(model_id) if model_id else "",
            "dimension_id": str(dimension_id) if dimension_id else "",
            "question": question,
            "expected_answer": excepted_answer,
            "expected_task": expected_task,
            "expected_category": expected_category,
            "expected_url": int(float(expected_url)) if expected_url and len(expected_url) > 0 else 0,
            "expected_cmd": int(float(expected_cmd)) if expected_cmd and len(expected_cmd) > 0 else 0,
            "expected_error_id": expected_error_id,
            "is_last": is_last,
            "qa_keywords": qa_keywords,
            "oem_codes": ctx_oem_codes,
            "data_src": data_src,
            "real_time": real_time
        }
        origin_data = OriginDataItemModel(**origin_dic)
        return create(origin_data)

    # 获取单元格内非空字符串值
    @classmethod
    def get_str_json_attr(cls, key, json):
        if key in json and json[key] is not None and len(str(json[key])) > 0 and "nan" != str(json[key]):
            return str(json[key])
        else:
            return ""

    # 获取excel单元格测试结果 pass=1|other=0
    @classmethod
    def get_col_result(cls, key, json):
        if key in json and json[key] is not None and len(str(json[key])) > 0 and "pass" == str(json[key]).lower():
            return 1
        else:
            return 0

    # 获取excel单元格测试结果 pass=1|other=0
    @classmethod
    def get_col_number(cls, key, json, def_val):
        if key in json and json[key] is not None and not math.isnan(json[key]):
            return json[key]
        else:
            return def_val

    # 根据字典名称和分组代码，获取对应该名称和分组的字典id
    @classmethod
    def get_dic_id(cls, name, group_code):
        if name is None or len(name) < 1:
            return ""

        results = DicInfo.find_name(None, name, group_code)
        if results and len(results) > 0:
            return ObjectId(results[0].id)
        else:
            return ""


    @classmethod
    # 测试结果excel导入处理,请勿出现空行
    def dimension_import(cls, file):
        try:
            ctx_oem_codes = CTX_OEM_CODES.get()
            excel_file = pd.ExcelFile(file)
            # 获取 Excel 文件中的所有 sheet 名称
            sheet_names = excel_file.sheet_names
            # 遍历每个sheet名称
            for sheet_name in sheet_names:
                # 读取每个 sheet 到 DataFrame
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                # 遍历 DataFrame 的每一行和每一列
                index: int
                model_order = 1 # 模块序号
                dimension_order = 1 # 维度序号
                for index, row in df.iterrows():
                    # 维度字典 模块处理
                    model_id = ""
                    dm_id = ""
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    for col in df.columns:
                        cell_value = row[col]
                        et_log.info(f"Sheet: {sheet_name}, Row: {index+1}, Column: {col}, Value: {cell_value}")
                        if "模块" == col and cell_value is not None:
                            # 检查model名称是否已经存在
                            results = DicInfo.find_name("", cell_value, DIC_CODE_MODEL)
                            if results is None:
                                model_id = DicInfo("", current_time, None, current_time, None, 0,
                                                   ctx_oem_codes, "", cell_value, "", "", DIC_CODE_MODEL,
                                                   model_order, "模块").insert_one()
                                model_order += 1
                                dimension_order = 1
                            else:
                                model_id = results[0].id
                        elif "维度" == col and cell_value is not None:
                            # 检查model名称是否已经存在
                            results = DicInfo.find_name(model_id, cell_value, DIC_CODE_DIMENSION)
                            if results is None:
                                dm_id = DicInfo("", current_time, None, current_time, None, 0,
                                                ctx_oem_codes, ObjectId(model_id), cell_value, "", "",
                                                DIC_CODE_DIMENSION, dimension_order, "维度").insert_one()
                                dimension_order += 1
                            else:
                                dm_id = results[0].id
                        et_log.info(f"dimension_import success model_id:{model_id} dm_id:{dm_id}")

        except Exception as e:
            et_log.error(f"dimension_import exception:{e}")
            traceback.print_exc()

    
