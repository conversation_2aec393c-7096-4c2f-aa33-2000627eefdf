# api.py

from flask import Blueprint, request
from com.exturing.ai.test.model.labor_cost import LaborCostModel, LaborCostQueryModel
from com.exturing.ai.test.comm.api_result import ApiResult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.labor_cost_service import (
    create,
    query_page,
    update,
    delete,
)

labor_cost = Blueprint("labor_cost", __name__)


# 新增人力成本
@labor_cost.route(f"/{URL_PREFIX}/staff/labor-cost/create", methods=["POST"])
def labor_cost_create():
    et_log.info("############labor_cost_create################")
    req_data = request.get_json()
    labor_cost_instance = LaborCostModel(**req_data)

    try:
        labor_cost_id = create(labor_cost_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(labor_cost_id)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 人力成本分页查询
@labor_cost.route(f"/{URL_PREFIX}/staff/labor-cost/page", methods=["POST"])
def labor_cost_page():
    et_log.info("############labor_cost_page################")
    data = request.get_json()
    query = LaborCostQueryModel(**data)

    page_num = data.get("page_num") or 1
    page_size = data.get("page_size") or 10

    page = query_page(page_num, page_size, query)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, page.to_json()
    ).to_json()


# 修改人力成本
@labor_cost.route(f"/{URL_PREFIX}/staff/labor-cost/update", methods=["POST"])
def labor_cost_update():
    et_log.info("############labor_cost_update################")
    req_data = request.get_json()
    id = req_data.get("labor_cost_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "labor_cost_id is null", ""
        ).to_json()

    labor_cost_instance = LaborCostModel(**req_data)

    try:
        update_result = update(id, labor_cost_instance)

        return ApiResult(
            ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, str(update_result)
        ).to_json()
    except AssertionError as e:
        return ApiResult(ResultCode.PARAM_IS_INVALID.code, str(e), "").to_json()


# 删除人力成本
@labor_cost.route(f"/{URL_PREFIX}/staff/labor-cost/del", methods=["POST"])
def labor_cost_delete():
    et_log.info("############labor_cost_delete################")
    req_data = request.get_json()
    id = req_data.get("labor_cost_id")

    if id is None or len(id) < 1:
        return ApiResult(
            ResultCode.PARAM_IS_INVALID.code, "labor_cost_id is null", ""
        ).to_json()

    delete_result = delete(id)

    return ApiResult(
        ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, delete_result
    ).to_json()
