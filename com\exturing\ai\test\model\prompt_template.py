from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class PromptTemplateModel(BaseDataModel):
    name: str
    model_id: Optional[str] = None
    dimension_ids: Optional[str] = None
    prompt: Optional[str] = None
    count: Optional[int] = None
    desc: Optional[str] = None


class PromptTemplateQueryModel(BaseModel):
    name: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}

        return condition
