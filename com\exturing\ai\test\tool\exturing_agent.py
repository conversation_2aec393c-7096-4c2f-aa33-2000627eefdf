import datetime
import traceback
import uuid
from uuid import uuid4
import sseclient  # 通过pip3 install sseclient-py 安装
import hashlib
import hmac
from hashlib import sha256
import base64
import time
import random
import json
import requests
import urllib.parse
from com.exturing.ai.test.comm.comm_constant import HUOSHAN_CHANNNEl, HUOSHAN_APPID, HUOSHAN_VEHICLE_ID, \
    HUOSHAN_HUAWEI_DEVICE_ID
from com.exturing.ai.test.comm.log_tool import et_log


class HwVolcanoTool:
    def __init__(self, query, model_url, model_ak, model_sk):
        self.query = query
        self.model_url = model_url
        self.ak = model_ak
        self.sk = model_sk

    def gen_sign(self, method, query_str, body=None):
        """
        :param method: 请求方式
        :param query_str: 请求内容
        :param body: 请求参数
        :param sk: sk
        :return: 加密后的数据
        """
        query_str = urllib.parse.quote(query_str, safe=':/&=')
        a = query_str.split("&")
        a.sort()
        sorted_query_str = "&".join(a)
        sorted_sign = method + "\n" + sorted_query_str + "\n"
        if body is not None and len(body) > 0:
            m = hashlib.md5()
            m.update(body.encode("utf8"))
            sorted_sign += m.hexdigest() + "\n"
        h = hmac.new(self.sk.encode("utf8"), sorted_sign.encode("utf8"), sha256).digest()
        return base64.b64encode(h).decode()

    def intent_rec(self):
        """
        意图识别方法
        :return: origin_intent_type 原始意图, final_intent_type 最终意图, model_res_time 模型耗时, full_link_time 全链耗时,
        log_id , question_id 对应query的请求id
        """
        try:
            # intent_custom_intent_time = float(400)
            # intent_master_normal_time = float(400)
            # intent_web_intent_time = float(400)
            method = "POST"
            timestamp = int(time.time())
            random_integer = random.randint(0, 65535)
            chat_id = int(datetime.datetime.now().timestamp() * 1000)
            question_id = uuid4().hex
            query_str = f"_timestamp={timestamp}&_nonce={random_integer}&chat_id={chat_id}&question_id={question_id}"
            params = {
                "query": self.query
            }
            body = json.dumps(params, ensure_ascii=False)
            et_log.info(f"body————————{body}")
            sign = self.gen_sign(method, query_str, body)
            headers = {
                "X-Signature": f'{self.ak}:{sign}',
                "Device-Id": HUOSHAN_HUAWEI_DEVICE_ID
            }
            time.sleep(5)
            et_log.info(f"HwVolcanoTool.intent_rec url: {self.model_url},query_str:{query_str}, headers: {headers}, body: {body}")
            response = requests.post(f'{self.model_url}?{query_str}', headers=headers, data=body.encode("utf-8"))
            et_log.info(f"HwVolcanoTool.intent_rec response:{response.text}")
            return json.loads(response.text)
        except Exception as e:
            et_log.error(f"HwVolcanoTool.intent_rec exception:{e}, {traceback.format_exc()}")
            return None


if __name__ == "__main__":
    # # 示例参数 - 请替换为实际值
    # query = "帮我画一副迪士尼城堡的照片"
    # model_url = "https://api-vehicle.volcengine.com/dpfm/v1/chat/rejection"
    model_ak = "Huawei2c65382373d2248f324d5240699f87f17"
    model_sk = "cptxYxMygcXbIELkbqLrLzyJhqXRTlxlWULwZdUrpQNp"
    #
    # # 创建VolcanoTool实例并调用reject_rec
    model_url = " https://llm-test.extour-inc.com:8989/exturing/v1/chat/intent"
    # # tool = VolcanoTool(query, model_url, model_ak, model_sk)
    tool = HwVolcanoTool("帮我画一副迪士尼城堡的照片", model_url, model_ak, model_sk)
    # tool.intent_rec()
    tool.model_url = "https://llm-test.extour-inc.com:8989/exturing/v1/chat/intent"
    tool.intent_rec()
