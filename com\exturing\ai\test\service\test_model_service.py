# service.py

from datetime import datetime

from com.exturing.ai.test.comm.comm_constant import CTX_OEM_CODES
from com.exturing.ai.test.model.test_model import TestModelModel, TestModelQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.comm.log_tool import et_log

_doc = "test_model"

# 新增模型
def create(data: TestModelModel) -> str:
    data_dict = data.model_dump()
    ctx_oem_codes = CTX_OEM_CODES.get()
    data_dict['oem_codes'] = ctx_oem_codes
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    data_dict['create_time'] = current_time
    et_log.info(f"create test_model: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)

# 分页查询模型
def query_page(page_num: int = 1, page_size: int = 10, query: TestModelQueryModel = None):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(_doc, condition, None, page.skip, page_size)
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page

# 修改模型
def update(id: str, data: TestModelModel) -> bool:
    data_dict = data.model_dump()

    data_dict['_id'] = id
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    data_dict['update_time'] = current_time
    et_log.info(f"update test_model: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False

# 删除模型
def delete(id: str) -> bool:
    et_log.info(f"delete test_model by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0

# 根据id查询模型
def find_pk(id: str):
    et_log.info(f"find test_model by id:{id}")
    res = MongoDBUtil.find_by_id(_doc, id)
    return MongoDBUtil.serialize_document(res) if res else None
