import datetime
import traceback
import json
import requests
from openai import OpenAI
from volcenginesdkarkruntime import Ark

from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.dto.chat_box_result_dto import ChatBoxResultDto
from com.exturing.ai.test.dto.response_sse_dto import ResponseSseDto
from com.exturing.ai.test.enums.exception_enum import ResponseEnum


class ChatBox:
    def __init__(self, query, model_url, model_ak, model_sk, model_bot):
        self.query = query
        self.model_url = model_url
        self.model_ak = model_ak
        self.model_sk = model_sk
        self.model_bot = model_bot

    def qw_25(self):
        """
        千问2.5闲聊ChatBox
        :return:
        """
        # # 获取生成器对象
        # response_stream = self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)
        #
        # # 迭代生成器并实时打印
        # for chunk in response_stream:
        #     print(chunk, end="", flush=True)  # 使用end=""避免换行，flush=True立即输出
        # print()  # 最后换行

        # yield response_stream
        yield from self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)



    def qw_30(self):
        """
        千问3.0闲聊ChatBox
        :return:
        """
        yield from self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)
        # response_stream = self.qw_chat_speak(self.query+"/no_think", self.model_url, self.model_ak, self.model_bot)
        #
        # # 迭代生成器并实时打印
        # for chunk in response_stream:
        #     print(chunk, end="", flush=True)  # 使用end=""避免换行，flush=True立即输出
        # print()  # 最后换行

    def qw_chat_speak(self, query, model_url, model_ak, model_bot):
        """
        千问闲聊ChatBox
        :param query:
        :param model_url:
        :param model_ak:
        :param model_bot:
        :return:
        """
        first_time = 0
        start_time = int(datetime.datetime.now().timestamp()*1000)
        client = OpenAI(
            base_url=model_url,
            api_key=model_ak,
        )

        completion = client.chat.completions.create(
            model=model_bot,
            messages=[
                {"role": "system", "content": "你好，千问"},
                {"role": "user", "content": query},
            ],
            temperature=0.01,
            stream=True,
        )

        first_flag = False
        full_answer = ""
        chat_id = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        chat_box_result_dto = None
        for chunk in completion:
            if chunk.choices[0].delta.content is not None:
                chunk_content = chunk.choices[0].delta.content
                et_log.info(f"qw_chat_speak chunk:{chunk_content}")
                if not first_flag:
                    first_flag = True
                    first_time = int(datetime.datetime.now().timestamp()*1000 - start_time)
                re_chunk_content = chunk_content.replace('"','`').replace("'", '`')
                full_answer += re_chunk_content
                # yield chunk.choices[0].delta.content
                chat_box_result_dto = ChatBoxResultDto(**{"chat_id":chat_id,
                                            "full_answer":full_answer,
                                            "first_time":first_time,
                                            "full_time":int(datetime.datetime.now().timestamp()*1000 - start_time),
                                            "chunk_answer":{
                                                "chunk":re_chunk_content,
                                                "ctime":int(datetime.datetime.now().timestamp()*1000)
                                            }}
                                       )
                resp_dto = ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", chat_box_result_dto.model_dump())
                # yield chat_box_result_dto.model_dump()
                yield f"""data: {str(resp_dto.model_dump()).replace("'", '"')}\n\n"""
        return chat_box_result_dto

    def db(self):
        """
        豆包闲聊ChatBox
        :return:
        """
        yield from self.doubao(self.query, self.model_url, self.model_ak, self.model_bot)
        # response_stream = self.doubao(self.query, self.model_url, self.model_ak, self.model_bot)
        # # 迭代生成器并实时打印
        # for chunk in response_stream:
        #     print(chunk, end="", flush=True)  # 使用end=""避免换行，flush=True立即输出
        # print()  # 最后换行

    def doubao(self, query, model_url, model_ak, model_bot):
        """
        流式调用豆包模型，并逐块返回响应内容
        :param query: 用户查询内容
        :param model_url: 模型API地址
        :param model_ak: 模型API密钥
        :param model_bot: 模型名称
        :yield: 流式返回每个内容块
        """
        try:
            first_time = 0
            start_time = int(datetime.datetime.now().timestamp()*1000)
            # 初始化 Ark 客户端
            client = Ark(
                api_key=model_ak,
                base_url=model_url
            )
            et_log.info(f"compute_similarity prompt_data:{query}")

            # 调用流式接口
            stream = client.bot_chat.completions.create(
                model=model_bot,
                messages=[
                    {"role": "system", "content": "你是豆包，是由字节跳动开发的AI人工智能助手。"},
                    {"role": "user", "content": query}
                ],
                stream=True
            )
            first_flag = False
            full_answer = ""
            chat_id = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            chat_box_result_dto = None
            # 流式返回每个内容块
            for chunk in stream:
                if chunk.references:
                    et_log.info(f"引用内容: {chunk.references}")
                if not chunk.choices:
                    et_log.info("响应内容为空或格式不正确")
                    continue
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    et_log.info(f"doubao_chat_speak chunk:{content}")
                    re_content = content.replace('"','`').replace("'", '`')
                    if not first_flag:
                        first_flag = True
                        first_time = int(datetime.datetime.now().timestamp()*1000-start_time)
                    full_answer+= re_content
                    # yield chunk.choices[0].delta.content
                    chat_box_result_dto = ChatBoxResultDto(**{"chat_id": chat_id,
                                                              "full_answer": full_answer,
                                                              "first_time": first_time,
                                                              "full_time": int(
                                                                  datetime.datetime.now().timestamp() * 1000 - start_time),
                                                              "chunk_answer": {
                                                                  "chunk": re_content,
                                                                  "ctime": int(
                                                                      datetime.datetime.now().timestamp() * 1000)
                                                              }}
                                                           )
                    resp_dto = ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", chat_box_result_dto.model_dump())
                    # yield chat_box_result_dto.model_dump()
                    yield f"""data: {str(resp_dto.model_dump()).replace("'", '"')}\n\n"""
            return chat_box_result_dto
        except Exception as e:
            et_log.error(f"请求失败，错误信息：{e},\n{traceback.print_exc()}")
            raise e

    def deepseek_v3(self):
        '''获取生成器对象，并迭代实时打印'''
        # response_stream = self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)
        # for chunk in response_stream:
        #     print(chunk, end="", flush=True)
        # print()
        yield from self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)

    def deepseek_R1(self):
        '''获取生成器对象，并迭代实时打印'''
        # response_stream = self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)
        # for chunk in response_stream:
        #     print(chunk, end="", flush=True)
        # print()
        yield from self.qw_chat_speak(self.query, self.model_url, self.model_ak, self.model_bot)

    def ernie_45_turbo(self):
        yield from self.ernie_45_turbo_model(self.query, self.model_url, self.model_ak, self.model_bot)
        '''获取生成器对象，并迭代实时打印'''
        # response_stream = self.ernie_45_turbo_model(self.query, self.model_url, self.model_ak, self.model_bot)
        # for chunk in response_stream:
        #     print("\n",chunk, end="", flush=True)
        # print()




    def ernie_45_turbo_model(self, query, model_url, model_ak, model_bot):
        start_time = int(datetime.datetime.now().timestamp() * 1000)
        first_flag = False
        full_answer = ""
        chat_id = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        payload = json.dumps({
            "model": model_bot,
            "messages": [
                {"role": "system", "content": "你好，千帆"},
                {"role": "user", "content": query}
            ],
            "web_search": {
                "enable": False,
                "enable_citation": False,
                "enable_trace": False
            },
            "stream": True
        })

        headers = {
            'Content-Type': 'application/json',
            'Authorization': model_ak
        }

        try:
            with requests.post(model_url, headers=headers, data=payload, stream=True) as response:
                response.raise_for_status()

                chat_box_result_dto = None
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8').strip()
                        if decoded_line.startswith("data:") and decoded_line != "data: [DONE]":
                            try:
                                data = json.loads(decoded_line[5:])  # 去掉 "data:"
                                chunk_content = data.get("choices", [{}])[0].get("delta", {}).get("content", "")

                                if chunk_content:
                                    if not first_flag:
                                        first_flag = True
                                        first_time = int(datetime.datetime.now().timestamp() * 1000 - start_time)

                                    re_content = chunk_content.replace('"', '`').replace("'", '`')
                                    full_answer += re_content  # 累积完整答案

                                    # 返回当前分片数据（流式核心）
                                    chat_box_result_dto = ChatBoxResultDto(**{
                                        "chat_id": chat_id,
                                        "full_answer": full_answer,
                                        "first_time": first_time,
                                        "full_time": int(datetime.datetime.now().timestamp() * 1000 - start_time),
                                        "chunk_answer": {
                                            "chunk": re_content,
                                            "ctime": int(datetime.datetime.now().timestamp() * 1000)
                                        }
                                    })
                                    resp_dto = ResponseSseDto.construct_enum(ResponseEnum.SUCCESS, "", chat_box_result_dto.model_dump())
                                    yield f"""data: {str(resp_dto.model_dump()).replace("'", '"')}\n\n"""
                            except Exception as e:
                                et_log.error(f"JSON解析失败: {e}")
            return chat_box_result_dto
        except requests.RequestException as e:
            et_log.error(f"请求失败: {e}")
            yield {"error": str(e)}


if __name__ == '__main__':
    '''千问2.5'''
    # query = "介绍下上海"
    # model_url = "http://47.117.182.61/Qwen2.5-14B-GPTQ/v1"
    # model_ak = "none"
    # model_sk = "none"
    # model_bot = "Qwen2.5-14B-GPTQ"
    # Tool = ChatBox(query, model_url, model_ak, model_sk, model_bot)
    # Tool.qw_25()
    '''千问3-30B-A3B'''
    # model_url = "http://47.117.182.61/Qwen3-30B-A3B/v1"
    # model_ak = "none"
    # model_sk = "none"
    # model_bot = "Qwen3-30B-A3B"
    # Tool = ChatBox(query, model_url, model_ak, model_sk, model_bot)
    # Tool.qw_25()  # 现在会流式打印输出

    '''豆包'''
    # model_url = "https://ark.cn-beijing.volces.com/api/v3"
    # model_ak = '38152498-88ec-42ee-a09f-cf0df75ad4b0'
    # model_sk = "none"
    # model_bot = "bot-20240617091417-s5tzs"
    # Tool = ChatBox("介绍下北京", model_url, model_ak, model_sk, model_bot)
    # Tool.db()
    '''deepseekR1'''
    # query = "介绍下上海"
    # model_url = "https://qianfan.baidubce.com/v2"
    # model_ak = 'bce-v3/ALTAK-C4aC0sePgOrgnQgqVdubR/84edf53990c45d2440cb969c35de3eaf08387952'
    # model_sk = "none"
    # model_bot = "deepseek-r1-distill-qwen-32b"
    # Tool = ChatBox(query, model_url, model_ak, model_sk, model_bot)
    # Tool.deepseek_R1()
    '''deepseekV3'''
    # query = "介绍下北京"
    # model_url = 'https://qianfan.baidubce.com/v2'
    # model_ak = 'bce-v3/ALTAK-C4aC0sePgOrgnQgqVdubR/84edf53990c45d2440cb969c35de3eaf08387952'
    # model_sk = 'none'
    # model_bot = 'deepseek-v3'
    # Tool = ChatBox(query, model_url, model_ak, model_sk, model_bot)
    # Tool.deepseek_v3()
    '''千帆4.5'''
    # query = "介绍下上海"
    # model_url = 'https://qianfan.baidubce.com/v2/chat/completions'
    # model_ak = 'Bearer bce-v3/ALTAK-C4aC0sePgOrgnQgqVdubR/84edf53990c45d2440cb969c35de3eaf08387952'
    # model_sk = 'none'
    # model_bot = 'ernie-4.5-turbo-vl-32k'
    # Tool = ChatBox(query, model_url, model_ak, model_sk, model_bot)
    # Tool.ernie_45_turbo()
