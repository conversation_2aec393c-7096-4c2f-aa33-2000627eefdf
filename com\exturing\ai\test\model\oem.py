from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class OemModel(BaseDataModel):
    oem_name: str
    code: str
    desc: Optional[str] = None
    
class OemQueryModel(BaseModel):
    oem_name: Optional[str] = None
    code: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}
        
        if self.oem_name:
            condition["oem_name"] = {"$regex": str(self.oem_name).strip(), "$options": "i"}

        if self.code:
            condition["code"] = {"$regex": str(self.code).strip(), "$options": "i"}
       
        return condition

    