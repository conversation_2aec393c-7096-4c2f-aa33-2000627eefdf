from datetime import datetime

from bson import ObjectId
from pydantic import BaseModel, root_validator, <PERSON>
from typing import Optional

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil

class BasePydanticModel(BaseModel):
    _doc: Optional[str] = ""
    id: Optional[ObjectId] = Field(default=None, alias='_id')
    create_time: Optional[str] = None
    create_by: Optional[int] = None
    update_time: Optional[str] = None
    update_by: Optional[int] = None
    is_del: Optional[int] = 0
    oem_codes: Optional[str] = None

    class Config:
        # underscore_attrs_are_private = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

    def insert_one(self):
        data_dict = self.model_dump()
        if "_doc" in data_dict:
            del data_dict["_doc"]
        if "id" in data_dict:
            del data_dict["id"]
        return MongoDBUtil.insert_one(self._doc, data_dict)

    # 删除数据
    @classmethod
    def delete_by_id(cls, _id, do_user):
        return MongoDBUtil.delete_by_id(cls._doc.__getattribute__("default"), _id, do_user)

    # 根据数据项父id，删除其下的所有数据记录项
    @classmethod
    def del_by_pid(cls, pid, do_user):
        condition = {"pid": ObjectId(str(pid))}
        return MongoDBUtil.delete_by_many(cls._doc.__getattribute__("default"), condition, do_user)

    # 根据数据项父id，删除其下的所有数据记录项
    @classmethod
    def del_by_condition(cls, condition, do_user):
        return MongoDBUtil.delete_by_many(cls._doc.__getattribute__("default"), condition, do_user)

    # 更新数据
    @classmethod
    def update_entity_json(cls, entity_json):
        if "_doc" in entity_json:
            del entity_json["_doc"]
        return MongoDBUtil.update_one_pro(cls._doc.__getattribute__("default"), entity_json)

    # 根据主键_id查询数据
    @classmethod
    def find_by_pk(cls, _id):
        item = MongoDBUtil.find_by_id(cls._doc.__getattribute__("default"), _id)
        if item:
            # 使用 __new__ 方法创建实例
            data = cls.__new__(cls)
            # 手动调用 __init__ 方法进行初始化
            if hasattr(cls, '__init__'):
                cls.__init__(data, **item)
            return data
        return None

    @classmethod
    def find_by_pid(cls, pid):
        condition = {"is_del": 0, "pid": ObjectId(str(pid))}
        count = cls.find_condition_count(condition)
        data_list = cls.find_page_list(condition, None, count, 0)
        return data_list

    # 根据条件查询数据匹配记录数
    @classmethod
    def find_condition_count(cls, condition):
        return MongoDBUtil.find_count(cls._doc.__getattribute__("default"), condition)

    # 根据条件查询数据的集合
    @classmethod
    def find_page_list(cls, condition, sorts, skip, limit):
        result = MongoDBUtil.find_condition_page(cls._doc.__getattribute__("default"), condition, sorts, skip, limit)
        if result is not None:
            items = []
            for item in result:
                data = cls(**item)
                items.append(data)
            return items
        else:
            return None

    @classmethod
    def find_condition(cls, condition):
        result = MongoDBUtil.find_condition(cls._doc.__getattribute__("default"), condition)
        if result is not None:
            items = []
            for item in result:
                # 使用 __new__ 方法创建实例
                data = cls.__new__(cls)
                # 手动调用 __init__ 方法进行初始化
                if hasattr(cls, '__init__'):
                    cls.__init__(data, **item)
                    items.append(data)
            return items
        else:
            return None

    # 分析方法
    @classmethod
    def aggregate(cls, pipeline):
        return MongoDBUtil.aggregate(cls._doc.__getattribute__("default"), pipeline)

    @classmethod
    def serialize_doc(cls, doc):
        if "_doc" in doc:
            del doc["_doc"]
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)  # 将 ObjectId 转换为字符串
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()  # 将 datetime 转换为 ISO 格式字符串
        return doc

    def serialize_model(self):
        """
        对象转换成字典
        :return: 对象的字典，处理了Object和datetime类型数据
        """
        doc = self.model_dump()
        if "_doc" in doc:
            del doc["_doc"]
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)  # 将 ObjectId 转换为字符串
            elif isinstance(value, datetime):
                doc[key] = value.isoformat()  # 将 datetime 转换为 ISO 格式字符串
        return doc