from bson import ObjectId

from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 测试计划
class TestPlan(BaseModel):

    _doc = "test_plan"

    project_id: str # 项目id
    plan_name: str  # 计划名称
    plan_status: int  # 计划状态 0=待生效 1=已生效
    start_time: str  # 计划开始时间
    end_time: str  # 计划结束时间
    desc: str  # 计划描述

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", project_id="", plan_name="",
                 plan_status=1, start_time="", end_time="", desc=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.project_id = project_id
        self.plan_name = plan_name
        self.plan_status = plan_status
        self.start_time = start_time
        self.end_time = end_time
        self.desc = desc
        self.oem_codes = oem_codes

    def to_json(self):
        base_json = super().to_json()
        base_json["_id"] = self._id
        base_json["project_id"] = ObjectId(self.project_id) if self.project_id and len(str(self.project_id)) > 0 else ""
        base_json["plan_name"] = self.plan_name
        base_json["plan_status"] = self.plan_status
        base_json["start_time"] = self.start_time
        base_json["end_time"] = self.end_time
        base_json["desc"] = self.desc
        return base_json

    # 读数据使用
    def to_json_str(self):
        data_json = self.to_json()
        data_json["_id"] = str(self._id) if self._id and len(str(self._id)) > 0 else ""
        data_json["project_id"] = str(self.project_id) if self.project_id and len(str(self.project_id)) > 0 else ""
        return data_json

