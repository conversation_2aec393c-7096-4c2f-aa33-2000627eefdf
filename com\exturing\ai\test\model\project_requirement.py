from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class ProjectRequirementModel(BaseDataModel):
    name: Optional[str] = None  # 需求名称
    type: Optional[str] = None  # 需求类型
    parent_id: Optional[str] = None  # 父级需求id
    project_id: Optional[str] = None  # 项目id
    content: Optional[str] = None  # 需求内容
    priority: Optional[str] = None  # 需求优先级 P0：高 P1：中 P2：低
    remark: Optional[str] = None  # 需求备注
    is_main: Optional[bool] = None  # 是否主线产品
    contractor: Optional[str] = None  # 承接方


class ProjectRequirementQueryModel(BaseModel):
    name: Optional[str] = None
    type: Optional[int] = None
    project_id: Optional[str] = None
    parent_id: Optional[str] = None
    priority: Optional[str] = None
    is_main: Optional[bool] = None

    def get_query_condition(self):
        condition = {}

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}

        if self.type:
            condition["type"] = self.type

        if self.project_id:
            condition["project_id"] = self.project_id

        if self.parent_id:
            condition["parent_id"] = self.parent_id

        if self.priority:
            condition["priority"] = self.priority

        if self.is_main is not None:
            condition["is_main"] = self.is_main

        return condition

    def get_query_tree_condition(self):
        condition = {}

        if self.project_id:
            condition["project_id"] = self.project_id

        return condition
