from com.exturing.ai.test.model.base_data_model import BaseDataModel
from typing import Optional
from pydantic import BaseModel


class TestConfigModel(BaseDataModel):
    name: str  # 评测通道名称
    tool_id: Optional[str] = None  # 评测工具id
    model_id: Optional[str] = None  # 评测模型id
    env_id: Optional[str] = None  # 评测环境id
    desc: Optional[str] = None  # 评测通道描述


class TestConfigQueryModel(BaseModel):
    name: Optional[str] = None
    tool_id: Optional[str] = None

    def get_query_condition(self):
        condition = {"is_del": 0}

        if self.name:
            condition["name"] = {"$regex": str(self.name).strip(), "$options": "i"}
        if self.tool_id:
            condition["tool_id"] = self.tool_id

        return condition


class TestConfigInfoModel(BaseModel):
    env_uri: Optional[str] = None  # 环境URI
    branch_name: Optional[str] = None  # 分支名称
    oem_code: Optional[str] = None  # oem_code
    pid_val: Optional[str] = None  # 车型pid
    device_id: Optional[str] = None  # 设备id
    user_id: Optional[str] = None  # 用户id
    version: Optional[str] = None  # 版本
    adapter_code: Optional[str] = None  # 脚本代码
    env_script: Optional[str] = None  # 环境脚本
    model_url: Optional[str] = None  # 模型地址
    model_bot: Optional[str] = None  # 模型bot_model
    model_ak: Optional[str] = None  # 模型ak
    model_sk: Optional[str] = None  # 模型sk
    model_params : Optional[str] = None   # 模型params

class TestConfigInfoReModel(TestConfigInfoModel):
    config_name: Optional[str] = None  # 通道名称
