
import smtplib
import traceback
from email.header import Header
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText

from com.exturing.ai.test.comm.comm_constant import SENDER_MAIL, SENDER_PW, MAIL_SMTP
from com.exturing.ai.test.comm.comm_util import get_file_type_by_magic_number
from com.exturing.ai.test.comm.log_tool import et_log


class SendMailUtil:
    """
    发送邮件 工具
    """

    @classmethod
    def send_default_text(cls, receivers, subject, message_text):
        """
        使用默认系统邮箱，发送文本邮件
        :param receivers:接收人数组
        :param subject:主题
        :param message_text:文本内容
        :return:True|False
        """
        et_log.info(f"send_default_text receivers:{receivers} subject:{subject} message_text:{message_text}")
        if not receivers or len(receivers) == 0:
            et_log.info(f"send_default_text receivers:{receivers} subject:{subject} fail, receivers is empty")
            return False
        return cls.send_text(SENDER_MAIL, SENDER_PW, MAIL_SMTP, receivers, subject, message_text)

    @classmethod
    def send_default_html(cls, receivers, subject, html_content):
        """
        使用默认系统邮箱，发送HTML邮件
        :param receivers:接收人数组
        :param subject:主题
        :param html_content:HTML内容
        :return:True|False
        """
        et_log.info(f"send_default_html receivers:{receivers} subject:{subject} html_content:{html_content}")
        if not receivers or len(receivers) == 0:
            et_log.info(f"send_default_html receivers:{receivers} subject:{subject} fail, receivers is empty")
            return False
        return cls.send_html(SENDER_MAIL, SENDER_PW, MAIL_SMTP, receivers, subject, html_content)

    @classmethod
    def send_default_file(cls, receivers, subject, mail_body, files):
        """
        使用默认系统邮箱，发送HTML邮件
        :param receivers:接收人数组
        :param subject:主题
        :param mail_body:邮件内容
        :param files:邮件附件file数组
        :return:True|False
        """
        et_log.info(f"send_text receivers:{receivers} subject:{subject} mail_body:{mail_body} files:{files}")
        if not receivers or len(receivers) == 0:
            et_log.info(f"send_text receivers:{receivers} subject:{subject} fail, receivers is empty")
            return False
        return cls.send_file(SENDER_MAIL, SENDER_PW, MAIL_SMTP, receivers, subject, mail_body, files)

    @classmethod
    def send_text(cls, sender_mail, sender_pw, smtp, receivers, subject, message_text):
        """
        发送文本内容邮件
        :param sender_mail: 发件人的帐号
        :param sender_pw: 发件人的帐号密码
        :param smtp: 发送邮件的SMTP
        :param receivers: 收件人数组
        :param subject: 邮件主题
        :param message_text: 邮件文本内容
        :return: 邮件发送结果     <EMAIL>
        """
        et_log.info(f"send_text sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} receivers:{receivers} "
                    f"subject:{subject} message_text:{message_text}")
        smtpObj = None
        try:
            # 邮件内容
            message = MIMEMultipart()
            message['From'] = Header(sender_mail, 'utf-8')
            message['To'] = Header(','.join(receivers), 'utf-8')

            # 邮件主题
            # subject = '纯文本邮件测试'
            message['Subject'] = Header(subject, 'utf-8')

            message.attach(MIMEText(message_text, 'plain', 'utf-8'))

            # 连接 SMTP 服务器
            # smtpObj = smtplib.SMTP(smtp, 25)
            smtpObj = smtplib.SMTP_SSL(smtp, 465)
            # 登录发件人邮箱
            smtpObj.login(sender_mail, sender_pw)
            # 发送邮件
            smtpObj.sendmail(sender_mail, receivers, message.as_string())
            et_log.info("文本邮件发送成功")
            return True
        except smtplib.SMTPDataError as e:
            et_log.error(f"邮件发送被拒绝，可能是垃圾邮件：{e}. 请检查邮件内容和发件人信誉。")
            return False
        except smtplib.SMTPAuthenticationError as e:
            et_log.error(f"认证失败：{e}. 请检查邮箱授权码或是否开启SMTP服务。")
            return False
        except smtplib.SMTPException as e:
            et_log.error(f"文本邮件发送失败: {e}")
            traceback.print_exc()
            return False
        except Exception as e:
            et_log.error(f"send_text fail {e},sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} receivers:{receivers} "
                    f"subject:{subject} message:{message_text}")
            traceback.print_exc()
            return False
        finally:
            # 关闭连接
            if smtpObj:
                smtpObj.quit()

    @classmethod
    def send_html(cls, sender_mail, sender_pw, smtp, receivers, subject, html_content):
        """
        发送HTML内容邮件
        :param sender_mail: 发件人的帐号
        :param sender_pw: 发件人的帐号密码
        :param smtp: 发送邮件的SMTP
        :param receivers: 收件人数组
        :param subject: 邮件主题
        :param html_content: 邮件html内容
        :return: 邮件发送结果     <EMAIL>
        """
        et_log.info(f"send_html sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} receivers:{receivers} "
                    f"subject:{subject} html_content:{html_content}")
        smtpObj = None
        try:
            # 邮件内容
            message = MIMEMultipart()
            message['From'] = Header(sender_mail, 'utf-8')
            message['To'] = Header(','.join(receivers), 'utf-8')
            message.attach(MIMEText(html_content, 'html', 'utf-8'))

            # 邮件主题
            # subject = '纯文本邮件测试'
            message['Subject'] = Header(subject, 'utf-8')

            # 连接 SMTP 服务器
            smtpObj = smtplib.SMTP_SSL(smtp, 465)
            # 登录发件人邮箱
            smtpObj.login(sender_mail, sender_pw)
            # 发送邮件
            smtpObj.sendmail(sender_mail, receivers, message.as_string())
            et_log.info("HTML邮件发送成功")
            return True
        except smtplib.SMTPDataError as e:
            et_log.error(f"邮件发送被拒绝，可能是垃圾邮件：{e}. 请检查邮件内容和发件人信誉。")
            return False
        except smtplib.SMTPAuthenticationError as e:
            et_log.error(f"认证失败：{e}. 请检查邮箱授权码或是否开启SMTP服务。")
            return False
        except smtplib.SMTPException as e:
            et_log(f"HTML邮件发送失败: {e}")
            traceback.print_exc()
            return False
        except Exception as e:
            et_log.error(f"send_html fail {e},sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} "
                         f"receivers:{receivers} subject:{subject} html_content:{html_content}")
            traceback.print_exc()
            return False
        finally:
            # 关闭连接
            if smtpObj:
                smtpObj.quit()

    @classmethod
    def send_file(cls, sender_mail, sender_pw, smtp, receivers, subject, mail_body, files):
        """
        发送HTML内容邮件
        :param sender_mail: 发件人的帐号
        :param sender_pw: 发件人的帐号密码
        :param smtp: 发送邮件的SMTP
        :param receivers: 收件人数组
        :param subject: 邮件主题
        :param mail_body: 邮件内容
        :param files: 邮件附件[file1, file2]附件对象数组
        :return: 邮件发送结果     <EMAIL>
        """
        et_log.info(
            f"send_file sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} receivers:{receivers} "
            f"subject:{subject} mail_body:{mail_body} files:{files}")
        smtpObj = None
        try:
            # 邮件内容
            message = MIMEMultipart()
            message['From'] = Header(sender_mail, 'utf-8')
            message['To'] = Header(','.join(receivers), 'utf-8')

            # 邮件主题
            # subject = '纯文本邮件测试'
            message['Subject'] = Header(subject, 'utf-8')

            # 邮件正文
            message.attach(MIMEText(mail_body, 'plain', 'utf-8'))

            # 添加附件
            for file in files:
                file_type = get_file_type_by_magic_number(file)
                if file_type is None:
                    et_log.error(f"attachment type parsing error")
                    return False
                attachment = MIMEApplication(file.read(), _subtype=file_type)
                attachment.add_header('Content-Disposition', 'attachment', filename=file.name)
                message.attach(attachment)

            # 连接 SMTP 服务器
            smtpObj = smtplib.SMTP_SSL(smtp, 465)
            # 登录发件人邮箱
            smtpObj.login(sender_mail, sender_pw)
            # 发送邮件
            smtpObj.sendmail(sender_mail, receivers, message.as_string())
            et_log.info("附件邮件发送成功")
            return True
        except smtplib.SMTPDataError as e:
            et_log.error(f"邮件发送被拒绝，可能是垃圾邮件：{e}. 请检查邮件内容和发件人信誉。")
            return False
        except smtplib.SMTPAuthenticationError as e:
            et_log.error(f"认证失败：{e}. 请检查邮箱授权码或是否开启SMTP服务。")
            return False
        except smtplib.SMTPException as e:
            et_log(f"附件邮件发送失败: {e}")
            traceback.print_exc()
            return False
        except Exception as e:
            et_log.error(f"send_file fail {e},sender_mail:{sender_mail} sender_pw:{sender_pw} smtp:{smtp} "
                         f"receivers:{receivers} subject:{subject} mail_body:{mail_body} files:{files}")
            traceback.print_exc()
            return False
        finally:
            # 关闭连接
            if smtpObj:
                smtpObj.quit()