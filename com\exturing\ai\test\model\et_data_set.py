import pymongo

from com.exturing.ai.test.comm.db_util import S<PERSON>Helper
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.model.base_model import BaseModel

# 数据集对象
class EtDataSet(BaseModel):
    _doc = "et_data_set"
    name: str
    types: str  # 测试类型 1:功能测试，2:性能测试，3:安全测试，4:能力测试
    objective: str  # 测试目的
    version: str    # 数据集版本
    ds_status: int # 数据集状态 1=启用 -1=禁用
    total_num: int # 数据集数据项数量
    vm_id: str  # 关联车型id
    test_scope: str # 测试范围 1=冒烟测试、2=全量测试、3=回归测试

    def __init__(self, _id, create_time, create_by, update_time, update_by, is_del, oem_codes="", name="", types="", objective="",
                 version="", vm_id="", ds_status=1, total_num=0, test_scope=""):
        super().__init__(_id, create_time, create_by, update_time, update_by, is_del, oem_codes)
        self.name = name
        self.types = types
        self.objective = objective
        self.version = version
        self.vm_id = vm_id
        self.ds_status = ds_status
        self.total_num = total_num
        self.oem_codes = oem_codes
        self.test_scope = test_scope

    def to_json(self):
        base_json = super().to_json()
        base_json["name"] = self.name
        base_json["types"] = self.types
        base_json["objective"] = self.objective
        base_json["version"] = self.version
        base_json["ds_status"] = self.ds_status
        base_json["total_num"] = self.total_num
        base_json["vm_id"] = self.vm_id
        base_json["test_scope"] = self.test_scope
        return base_json

    def to_json_str(self):
        base_json = self.to_json()
        del base_json["name"]
        base_json["set_name"] = self.name
        return base_json

    def insert_et_dataset(self):
        db = SQLHelper()
        return db.create("insert into data_set(name, types, objective, version, vm_id, create_by) values (%s, %s, %s, %s, %s)",
                         (self.name, self.types, self.objective, self.version, self.vm_id, self.create_by))

    # 更新数据集 数据数量
    @classmethod
    def update_set_total(cls, _id, total_num):
        return MongoDBUtil.find_one_and_update(cls._doc, _id, {"total_num": total_num})

    # 根据数据集名称模糊查询数据集分页的集合
    @classmethod
    def find_page_condition(cls, name: str, create_by, limit, skip):
        condition = {"is_del": 0}
        if name and name.strip() != "":
            condition["name"] = name.strip()
        if create_by > 0:
            condition["create_by"] = create_by
        # # TODO 演示临时添加
        # sorts = [("create_time", pymongo.ASCENDING)]
        # result = MongoDBUtil.find_condition_page(cls._doc, condition, sorts, skip, limit)
        result = MongoDBUtil.find_condition_page(cls._doc, condition, None, skip, limit)
        if result is not None:
            sets = []
            for doc in result:
                et_set = EtDataSet(**doc)
                sets.append(et_set)
            return sets
        else:
            return None

    # 根据数据集名称，查询匹配到的数据集总记录数
    @classmethod
    def find_count(cls, name: str, create_by):
        condition = {"is_del": 0}
        if name and name.strip() != "":
            condition["name"] = {"$regex": name.strip(), "$options": "i"}
        if create_by > 0:
            condition["create_by"] = create_by
        return MongoDBUtil.find_count(cls._doc, condition)




# EtDataSet(0, None, 1, None, None, None, "test2", "功能测试",
#           "测试插入数据", "v1.0.0").insert_et_dataset()

