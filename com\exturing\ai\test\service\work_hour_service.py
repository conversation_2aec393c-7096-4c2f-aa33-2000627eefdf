# service.py
from tracemalloc import start
from com.exturing.ai.test.comm.comm_constant import CTX_USER_ID
from com.exturing.ai.test.model import employee
from com.exturing.ai.test.model.employee import EmployeeQueryModel
from com.exturing.ai.test.model.work_hour import WorkHourModel, WorkHourQueryModel
from com.exturing.ai.test.comm.mongodb_util import MongoDBUtil
from com.exturing.ai.test.comm.page_result import PageResult
from com.exturing.ai.test.service.employee_service import query_list
from com.exturing.ai.test.comm.log_tool import et_log

_doc = "work_hour"


# 新增工时信息
def create(data: WorkHourModel) -> str:
    data_dict = data.model_dump()
    et_log.info(f"create work_hour: {data_dict}")
    return MongoDBUtil.insert_one(_doc, data_dict)


# 分页查询工时信息
def query_page(
    page_num: int = 1, page_size: int = 10, query: WorkHourQueryModel = None
):
    condition = query.get_query_condition()
    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 分页查询个人工时信息
def query_self_page(
    page_num: int = 1, page_size: int = 10, query: WorkHourQueryModel = None
):
    condition = query.get_query_condition()
    employees = query_list(EmployeeQueryModel(**{"account_id": CTX_USER_ID.get()}))

    if not employees:
        return PageResult(page_num, page_size, 0)

    employee_ids = [str(employee["_id"]) for employee in employees]
    if (
        not condition.get("employee_id")
        or condition.get("employee_id") not in employee_ids
    ):
        condition["employee_id"] = {"$in": employee_ids}

    total = MongoDBUtil.find_count(_doc, condition)

    page = PageResult(page_num, page_size, total)

    if total > 0:
        result = MongoDBUtil.find_condition_page(
            _doc, condition, None, page.skip, page_size
        )
        result_list = list(result or [])
        json_list = [MongoDBUtil.serialize_document(doc) for doc in result_list]
        page.page_data = json_list

    return page


# 修改工时信息
def update(id: str, data: WorkHourModel) -> bool:
    data_dict = data.model_dump()

    data_dict["_id"] = id
    et_log.info(f"update work_hour: {data_dict}")
    update_num = MongoDBUtil.update_one_pro(_doc, data_dict)

    if update_num.modified_count and update_num.modified_count > 0:
        return True
    else:
        return False


# 删除工时信息
def delete(id: str) -> bool:
    et_log.info(f"delete work_hour by id:{id}")
    return MongoDBUtil.delete_by_id(_doc, id, 0) > 0


# 检查上报周期是否重叠
def check_time_overlap(data: WorkHourModel, id="") -> bool:
    result = MongoDBUtil.find_condition(_doc, {"employee_id": data.employee_id})
    result_list = list(result or [])
    if not result_list:
        return False

    for item in result_list:
        if id and str(item.get("_id")) == id:
            continue
        if data.end_time > item.get("start_time") and data.start_time < item.get(
            "end_time"
        ):
            return True
    return False
