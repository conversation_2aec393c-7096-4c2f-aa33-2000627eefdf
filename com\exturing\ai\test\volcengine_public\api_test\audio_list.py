import time
from com.exturing.ai.test.volcengine_public.volcengine_client import VolcengineClient

api_path = "/dpfm/v1/audio/list"


def audio_list_api(data=None):
    """
    音频热榜接口测试
    """
    # 测试数据

    with VolcengineClient() as client:
        start_time = time.time()
        response = client.post(
            path=api_path,
            data=data,
        )

        print("Response JSON:", response.text)

        headers = response.headers
        response_json = response.json()

        print("Response JSON:", response_json)

        result = {}

        if response_json.get("errno") == 0:
            result["log_id"] = headers.get("x-tt-logid")
            result["cost_total"] = int((time.time() - start_time) * 1000)

            res_data = response_json.get("data", {})

            result["list"] = res_data.get("list")

        return result


if __name__ == "__main__":
    pass
    # 接口404

    # result = audio_list_api()
    # print("Response:", result)

# python -m com.exturing.ai.test.volcengine_public.api_test.audio_list
