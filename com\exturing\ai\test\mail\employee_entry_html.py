from string import Template

employee_entry_html = Template(r"""
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>表格标题</title>
    <style>
      /* 在这里添加CSS样式 */
      body {
        font-family: Arial, sans-serif;
        background-color: #f0f0f0;
        color: #333;
        font-size: 14px;
      }
      /* 基本表格样式 */
      table {
        width: 100%; /* 表格宽度 */
        border-collapse: collapse; /* 合并边框 */
        font-family: Arial, sans-serif; /* 字体 */
        margin: 20px 0; /* 外边距 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
        background-color: #ffffff; /* 背景色 */
        font-size: 12px; /* 字体大小 */
      }

      /* 表头样式 */
      th {
        background-color: #29bdf2; /* 背景色 */
        color: white; /* 文字颜色 */
        font-weight: bold; /* 字体加粗 */
        padding: 12px; /* 内边距 */
        text-align: center; /* 文字对齐方式 */
        border-bottom: 2px solid #1e85aa; /* 底部边框 */
      }

      /* 表格单元格样式 */
      td {
        padding: 10px; /* 内边距 */
        border-bottom: 1px solid #dddddd; /* 底部边框 */
        color: #333333; /* 文字颜色 */
        text-align: center; /* 文字对齐方式 */
      }

      /* 表格行悬停效果 */
      tr:hover {
        background-color: #f1f1f1; /* 悬停背景色 */
        transition: background-color 0.3s ease; /* 过渡效果 */
      }

      /* 表格斑马纹效果 */
      tr:nth-child(even) {
        background-color: #f9f9f9; /* 偶数行背景色 */
      }

      /* 表格边框样式 */
      td,
      th {
        border-right: 1px solid #dddddd; /* 右侧边框 */
      }

      /* 最后一列去除右侧边框 */
      td:last-child,
      th:last-child {
        border-right: none;
      }
    </style>
  </head>
  <body>
    <table>
      <thead>
        <tr>
          <th width="10%">姓名</th>
          <th width="5%">级别</th>
          <th width="5%">员工类型</th>
          <th width="10%">部门</th>
          <th width="10%">部门负责人</th>
          <th width="5%">学历</th>
          <th width="10%">技术栈</th>
          <th width="10%">岗位</th>
          <th width="5%">Base</th>
          <th width="10%">所属项目</th>
          <th width="8%">入职时间</th>
        </tr>
      </thead>
      <tbody class="ant-table-tbody">
        $employees_entry
      </tbody>
    </table>
  </body>
</html>
""")
